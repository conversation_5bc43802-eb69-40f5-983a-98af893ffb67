import { AppDataSource } from "../config/database";
import { Restaurant } from "../models/Restaurant";
import { Playlist, PlaylistStatus } from "../models/Playlist";
import { Suggestion, SuggestionStatus } from "../models/Suggestion";
import { youtubeOAuthService } from "./YouTubeOAuthService";
import { collaborativePlaylistService } from "./CollaborativePlaylistService";
import { logger } from "../utils/logger";
import { Not, IsNull } from "typeorm";
import { WebSocketService } from "./WebSocketService";
import { redisClient } from "../config/redis";
import { PlaylistSchedule } from "../models/PlaylistSchedule";

/**
 * 🔄 SERVIÇO DE REORDENAÇÃO AUTOMÁTICA DE PLAYLISTS
 *
 * Executa a cada 5 minutos para reordenar playlists do YouTube
 * baseado no ranking de supervotos em tempo real.
 *
 * Funcionalidades:
 * - Busca ranking de supervotos por restaurante
 * - Reordena playlist do YouTube automaticamente
 * - Logs de auditoria para monitoramento
 * - Tratamento de erros robusto
 */

export interface ReorderResult {
  success: boolean;
  restaurantId: string;
  playlistId?: string;
  tracksReordered: number;
  message: string;
  timestamp: Date;
  // status adicional para diferenciar "skipped" de falha real
  status?: "success" | "skipped" | "failed";
}

export interface PlaylistItem {
  videoId: string;
  position: number;
  title?: string;
  voteCount?: number;
  isPaid?: boolean;
  paymentAmount?: number;
}

export class PlaylistReorderService {
  private static instance: PlaylistReorderService;
  private restaurantRepository = AppDataSource.getRepository(Restaurant);
  private playlistRepository = AppDataSource.getRepository(Playlist);
  private suggestionRepository = AppDataSource.getRepository(Suggestion);
  private scheduleRepository = AppDataSource.getRepository(PlaylistSchedule);
  private reorderInterval: NodeJS.Timeout | null = null;
  private isRunning = false;
  // Janela de "graça" para última leva de votos antes de aplicar a reordenação (em segundos)
  private static readonly GRACE_WINDOW_SECONDS = 15;

  private constructor() {
    // WebSocketService será inicializado quando necessário
    logger.info("🔄 PlaylistReorderService inicializado");
  }

  static getInstance(): PlaylistReorderService {
    if (!PlaylistReorderService.instance) {
      PlaylistReorderService.instance = new PlaylistReorderService();
    }
    return PlaylistReorderService.instance;
  }

  /**
   * 🚀 INICIAR REORDENAÇÃO AUTOMÁTICA
   * Executa a cada 5 minutos (300.000ms)
   */
  startAutoReorder(): void {
    if (this.isRunning) {
      logger.warn("⚠️ Reordenação automática já está em execução");
      return;
    }

    this.isRunning = true;
    logger.info("🚀 Iniciando reordenação automática a cada 5 minutos");

    // Executar imediatamente na inicialização
    this.executeReorderCycle();

    // Agendar execução a cada 5 minutos
    this.reorderInterval = setInterval(() => {
      this.executeReorderCycle();
    }, 5 * 60 * 1000); // 5 minutos
  }

  /**
   * 🛑 PARAR REORDENAÇÃO AUTOMÁTICA
   */
  stopAutoReorder(): void {
    if (this.reorderInterval) {
      clearInterval(this.reorderInterval);
      this.reorderInterval = null;
    }
    this.isRunning = false;
    logger.info("🛑 Reordenação automática parada");
  }

  /**
   * 🔄 EXECUTAR CICLO DE REORDENAÇÃO
   * Processa todos os restaurantes ativos
   */
  private async executeReorderCycle(): Promise<void> {
    const startTime = new Date();
    logger.info("🔄 Iniciando ciclo de reordenação automática");

    try {
      // Buscar restaurantes ativos com playlists
      const restaurants = await this.restaurantRepository.find({
        where: { isActive: true },
        relations: ["playlists"],
      });

      const results: ReorderResult[] = [];

      for (const restaurant of restaurants) {
        try {
          const result = await this.reorderRestaurantPlaylists(restaurant.id);
          results.push(result);
        } catch (error) {
          logger.error(
            `❌ Erro ao reordenar playlists do restaurante ${restaurant.id}:`,
            error
          );
          results.push({
            success: false,
            restaurantId: restaurant.id,
            tracksReordered: 0,
            message: `Erro: ${
              error instanceof Error ? error.message : "Erro desconhecido"
            }`,
            timestamp: new Date(),
          });
        }
      }

      // Log do resultado do ciclo
      const successful = results.filter(
        (r) => r.success && r.status !== "skipped"
      ).length;
      const skipped = results.filter((r) => r.status === "skipped").length;
      const failed = results.filter((r) => !r.success).length;
      const totalTracks = results.reduce(
        (sum, r) => sum + r.tracksReordered,
        0
      );
      const duration = Date.now() - startTime.getTime();

      logger.info(`✅ Ciclo de reordenação concluído em ${duration}ms:`);
      logger.info(
        `   📊 ${successful} sucessos, ${skipped} ignorados, ${failed} falhas`
      );
      logger.info(`   🎵 ${totalTracks} faixas reordenadas no total`);
    } catch (error) {
      logger.error("❌ Erro no ciclo de reordenação:", error);
    }
  }

  /**
   * 🏪 REORDENAR PLAYLISTS DE UM RESTAURANTE
   */
  private async reorderRestaurantPlaylists(
    restaurantId: string
  ): Promise<ReorderResult> {
    try {
      // Verificar se restaurante está autenticado com YouTube
      const isAuthenticated = await youtubeOAuthService.isAuthenticated(
        restaurantId
      );

      // 1) Verificar agendamento e ativar playlist do slot atual (se houver)
      const schedules = await this.scheduleRepository.find({
        where: { restaurantId, isActive: true },
      });

  let activePlaylist = await this.playlistRepository.findOne({
        where: {
          restaurant: { id: restaurantId },
          status: PlaylistStatus.ACTIVE,
          youtubePlaylistId: Not(IsNull()),
        },
        order: { isDefault: "DESC", createdAt: "DESC" },
      });

      if (schedules.length) {
        for (const schedule of schedules) {
          const current = schedule.getCurrentActivePlaylist();
          if (current?.playlistId) {
            // Se a playlist agendada é diferente da ACTIVE atual, trocá-la
            if (!activePlaylist || activePlaylist.id !== current.playlistId) {
              const scheduledPlaylist = await this.playlistRepository.findOne({
                where: {
                  id: current.playlistId,
                  restaurant: { id: restaurantId },
                },
              });
              if (scheduledPlaylist) {
                // Desativar outras ACTIVE e ativar a agendada
                await this.playlistRepository.update(
                  { restaurant: { id: restaurantId } },
                  { status: PlaylistStatus.INACTIVE }
                );
                scheduledPlaylist.status = PlaylistStatus.ACTIVE;
                await this.playlistRepository.save(scheduledPlaylist);
                activePlaylist = scheduledPlaylist;
                logger.info(
                  `⏰ Playlist agendada ativada: ${scheduledPlaylist.name} (${scheduledPlaylist.id})`
                );
              }
            }
            break; // Um schedule ativo por vez
          }
        }
      }

      // Buscar playlist ativa principal (após considerar agendamento)
      if (!activePlaylist || !activePlaylist.youtubePlaylistId) {
        return {
          success: true,
          status: "skipped",
          restaurantId,
          tracksReordered: 0,
          message:
            "Ignorado: nenhum playlist ativa com youtubePlaylistId definida",
          timestamp: new Date(),
        };
      }

  // Cada restaurante pode ter sua própria playlist ativa; nenhuma restrição especial para demo

      // 🔁 Antes de reordenar: se houver snapshot de ordem original e ranking atual vier vazio,
      // tentar reverter para a ordem original (remove efeito do reordenamento anterior)
      try {
        const client = redisClient.getClient();
        const originalKey = `playlist:originalOrder:${restaurantId}:${activePlaylist.id}`;
        const originalJson = await client.get(originalKey);
        if (originalJson) {
          const rankingProbe = await collaborativePlaylistService.getVotingRanking(restaurantId, 5);
          const hasActiveRanking = !!(rankingProbe.success && rankingProbe.data && rankingProbe.data.length);
          if (!hasActiveRanking) {
            const originalOrder: Array<{ videoId: string; position: number }> = JSON.parse(originalJson);
            const revertOk = await youtubeOAuthService.reorderPlaylist(
              restaurantId,
              activePlaylist.youtubePlaylistId,
              originalOrder
            );
            if (revertOk) {
              // Atualizar ordem local conforme snapshot
              if (activePlaylist.tracks && Array.isArray(activePlaylist.tracks)) {
                const posMap = new Map(originalOrder.map((o) => [o.videoId, o.position]));
                activePlaylist.tracks = activePlaylist.tracks
                  .map((track) => ({
                    ...track,
                    position: posMap.has(track.youtubeVideoId)
                      ? (posMap.get(track.youtubeVideoId) as number)
                      : track.position,
                  }))
                  .sort((a, b) => a.position - b.position);
                await this.playlistRepository.save(activePlaylist);
              }
              await client.del(originalKey);
              await this.notifyPlaylistReorder(restaurantId, {
                playlistId: activePlaylist.id,
                playlistName: activePlaylist.name,
                tracksReordered: originalOrder.length,
                topTracks: [],
              });
              return {
                success: true,
                status: "success",
                restaurantId,
                playlistId: activePlaylist.id,
                tracksReordered: originalOrder.length,
                message: "Revertido para a ordem original por ausência de ranking ativo",
                timestamp: new Date(),
              };
            }
          }
        }
      } catch (e) {
        logger.warn("Falha ao tentar reverter para ordem original (não bloqueante)", e);
      }

      // Buscar ranking de votos (primeira leitura)
      let rankingResult = await collaborativePlaylistService.getVotingRanking(
        restaurantId,
        50
      );

      if (
        !rankingResult.success ||
        !rankingResult.data ||
        rankingResult.data.length === 0
      ) {
        return {
          success: true,
          status: "skipped",
          restaurantId,
          playlistId: activePlaylist.id,
          tracksReordered: 0,
          message: "Ignorado: nenhuma música com votos para reordenar",
          timestamp: new Date(),
        };
      }

  // Preparar nova ordem baseada no ranking, restrita às faixas da playlist
      const allowed = new Set(
        (activePlaylist.tracks ?? []).map((t) => t.youtubeVideoId)
      );
      // Respeitar cooldown ao reordenar: remove itens em cooldown
  const withAllowed = rankingResult.data.filter((it) =>
        allowed.has(it.youtubeVideoId)
      );
      let filtered = withAllowed;
      try {
        const { collaborativePlaylistService } = await import('./CollaborativePlaylistService');
        const tmp: typeof withAllowed = [];
        for (const it of withAllowed) {
          const inCd = await collaborativePlaylistService.isSongInCooldown(
            restaurantId,
            it.youtubeVideoId
          );
          if (!inCd) tmp.push(it);
        }
        filtered = tmp;
      } catch {}

      const newOrder: PlaylistItem[] = filtered.map((item, index) => ({
        videoId: item.youtubeVideoId,
        position: index,
        title: item.title,
        voteCount: item.voteCount,
        isPaid: item.isPaid,
        paymentAmount: item.paymentAmount,
      }));

      if (newOrder.length === 0) {
        return {
          success: true,
          status: "skipped",
          restaurantId,
          playlistId: activePlaylist.id,
          tracksReordered: 0,
          message:
            "Ignorado: nenhuma música elegível (todas fora da playlist)",
          timestamp: new Date(),
        };
      }

      // Anunciar janela de graça de 15s com snapshot do candidato a próximo
      try {
        const wsService = WebSocketService.getInstance();
        const nextCandidate = filtered && filtered.length ? filtered[0] : null;
        const graceSeconds = PlaylistReorderService.GRACE_WINDOW_SECONDS;
        const deadlineAt = new Date(Date.now() + graceSeconds * 1000);
        console.log(`[REORDER WS] Emitindo ranking-snapshot: ${nextCandidate?.title || 'None'} (${nextCandidate?.youtubeVideoId || 'N/A'})`);
        await wsService.emitToRestaurant(restaurantId, "ranking-snapshot", {
          type: "ranking-snapshot",
          restaurantId,
          graceSeconds,
          deadlineAt: deadlineAt.toISOString(),
          nextTrack: nextCandidate
            ? {
                youtubeVideoId: nextCandidate.youtubeVideoId,
                title: nextCandidate.title,
                artist: nextCandidate.artist,
                isPaid: nextCandidate.isPaid,
                paymentAmount: nextCandidate.paymentAmount,
                voteCount: nextCandidate.voteCount,
              }
            : null,
        });
      } catch (wsErr) {
        logger.warn("Falha ao emitir ranking-snapshot (não bloqueante)", wsErr);
      }

      // Aguardar a janela de graça e recomputar o ranking para capturar últimos votos
      await new Promise((resolve) => setTimeout(resolve, PlaylistReorderService.GRACE_WINDOW_SECONDS * 1000));
      rankingResult = await collaborativePlaylistService.getVotingRanking(
        restaurantId,
        50
      );
      const allowedAfter = new Set(
        (activePlaylist.tracks ?? []).map((t) => t.youtubeVideoId)
      );
      const withAllowedAfter = (rankingResult.data || []).filter((it) =>
        allowedAfter.has(it.youtubeVideoId)
      );
      let filteredAfter = withAllowedAfter;
      try {
        const { collaborativePlaylistService } = await import('./CollaborativePlaylistService');
        const tmpAfter: typeof withAllowedAfter = [];
        for (const item of withAllowedAfter) {
          try {
            const cd = await collaborativePlaylistService.isSongInCooldown(restaurantId, item.youtubeVideoId);
            if (!cd) tmpAfter.push(item);
          } catch {
            tmpAfter.push(item);
          }
        }
        filteredAfter = tmpAfter;
      } catch {}

      // Substituir lista filtrada pela versão pós-janela
      filtered = filteredAfter;

      // Antes de reordenar: armazenar snapshot da ordem original (para possível reversão)
      try {
        if (activePlaylist.tracks && Array.isArray(activePlaylist.tracks)) {
          const originalOrder = activePlaylist.tracks
            .map((t) => ({ videoId: t.youtubeVideoId, position: t.position }))
            .sort((a, b) => a.position - b.position);
          const client = redisClient.getClient();
          const originalKey = `playlist:originalOrder:${restaurantId}:${activePlaylist.id}`;
          // Manter por até 1 hora; será removido ao reverter com sucesso
          await client.setEx(originalKey, 3600, JSON.stringify(originalOrder));
        }
      } catch (e) {
        logger.warn("Não foi possível salvar snapshot da ordem original", e);
      }

      let reorderedOnYouTube = false;
      if (isAuthenticated) {
        // Reordenar playlist no YouTube
        const reorderSuccess = await youtubeOAuthService.reorderPlaylist(
          restaurantId,
          activePlaylist.youtubePlaylistId,
          newOrder
        );
        if (!reorderSuccess) {
          return {
            success: false,
            status: "failed",
            restaurantId,
            playlistId: activePlaylist.id,
            tracksReordered: 0,
            message: "Falha ao reordenar playlist no YouTube",
            timestamp: new Date(),
          };
        }
        reorderedOnYouTube = true;
      } else {
        // Fallback local: sem OAuth do YouTube, ainda aplicamos ordem no DB e emitimos eventos
        // para que o frontend obedeça a seleção a cada 5 minutos.
        logger.info("⚠️ Reordenação local (sem YouTube OAuth): aplicando nova ordem no DB/WebSocket");
      }

      // Atualizar ordem local na base de dados
      if (activePlaylist.tracks && Array.isArray(activePlaylist.tracks)) {
        activePlaylist.tracks = activePlaylist.tracks
          .map((track) => {
            const newOrderItem = newOrder.find(
              (item) => item.videoId === track.youtubeVideoId
            );
            return {
              ...track,
              position: newOrderItem ? newOrderItem.position : track.position,
            };
          })
          .sort((a, b) => a.position - b.position);

        await this.playlistRepository.save(activePlaylist);
      }

      logger.info(
        `🎵 Playlist reordenada${reorderedOnYouTube ? " (YouTube)" : " (local)"}: ${activePlaylist.name} (${newOrder.length} faixas)`
      );

      // ✅ Marcar snapshot do ranking como CONSUMIDO por um período (evita reaplicação/loop)
      try {
        const itemsForSignature = filtered.map((item) => ({
          youtubeVideoId: item.youtubeVideoId,
          voteCount: item.voteCount,
          paymentAmount: item.paymentAmount,
          isPaid: item.isPaid,
        }));
        await collaborativePlaylistService.markRankingAsConsumed(
          restaurantId,
          itemsForSignature,
          5 * 60 // 5 minutos
        );
      } catch (e) {
        logger.warn("Falha ao marcar ranking como consumido (não bloqueante)", e);
      }

  // 🔔 Notificar reordenação em tempo real
      await this.notifyPlaylistReorder(restaurantId, {
        playlistId: activePlaylist.id,
        playlistName: activePlaylist.name,
        tracksReordered: newOrder.length,
        topTracks: newOrder.slice(0, 5), // Top 5 músicas
      });

      return {
        success: true,
        status: "success",
        restaurantId,
        playlistId: activePlaylist.id,
        tracksReordered: newOrder.length,
        message: `Playlist reordenada com sucesso (${newOrder.length} faixas)`,
        timestamp: new Date(),
      };
    } catch (error) {
      logger.error(
        `❌ Erro ao reordenar playlists do restaurante ${restaurantId}:`,
        error
      );
      return {
        success: false,
        status: "failed",
        restaurantId,
        tracksReordered: 0,
        message: `Erro: ${
          error instanceof Error ? error.message : "Erro desconhecido"
        }`,
        timestamp: new Date(),
      };
    }
  }

  /**
   * 📊 OBTER STATUS DO SERVIÇO
   */
  getStatus(): {
    isRunning: boolean;
    nextExecution?: Date;
    uptime: number;
  } {
    const nextExecution =
      this.isRunning && this.reorderInterval
        ? new Date(Date.now() + 5 * 60 * 1000)
        : undefined;

    return {
      isRunning: this.isRunning,
      nextExecution,
      uptime: process.uptime(),
    };
  }

  /**
   * 🔔 NOTIFICAR REORDENAÇÃO DE PLAYLIST
   */
  private async notifyPlaylistReorder(
    restaurantId: string,
    reorderData: {
      playlistId: string;
      playlistName: string;
      tracksReordered: number;
      topTracks: any[];
    }
  ): Promise<void> {
    try {
      const notificationData = {
        type: "playlist_reordered",
        playlist: {
          id: reorderData.playlistId,
          name: reorderData.playlistName,
          tracksReordered: reorderData.tracksReordered,
        },
        topTracks: reorderData.topTracks.map((track, index) => ({
          position: index + 1,
          title: track.title,
          artist: track.artist,
          voteCount: track.voteCount,
          isPaid: track.isPaid,
          paymentAmount: track.paymentAmount,
        })),
        timestamp: new Date().toISOString(),
        message: `Playlist reordenada automaticamente - ${reorderData.tracksReordered} músicas`,
      };

      // Notificar todos os clientes do restaurante (se WebSocket disponível)
      try {
        const wsService = WebSocketService.getInstance();
        if (wsService) {
          await wsService.emitToRestaurant(
            restaurantId,
            "playlistReordered",
            notificationData
          );
        }
      } catch (wsError) {
        logger.warn("WebSocket não disponível para notificação:", wsError);
      }

      // Notificar administradores com dados detalhados (se WebSocket disponível)
      try {
        const wsService = WebSocketService.getInstance();
        if (wsService) {
          await wsService.emitToAdmins(
            restaurantId,
            "playlistReorderedAdmin",
            {
              ...notificationData,
              adminDetails: {
                autoReorderEnabled: true,
                nextReorderTime: new Date(Date.now() + 5 * 60 * 1000).toISOString(), // +5 min
              },
            }
          );
        }
      } catch (wsError) {
        logger.warn("WebSocket não disponível para notificação admin:", wsError);
      }

      logger.info(
        `🔔 Notificação de reordenação enviada: ${reorderData.playlistName}`
      );

      // Emitir também um evento 'reorderSelected' com o primeiro item da nova ordem
      try {
        const first = reorderData.topTracks?.[0];
        if (first) {
          const wsService = WebSocketService.getInstance();
          if (wsService) {
            await wsService.emitToRestaurant(restaurantId, "reorderSelected", {
              type: "reorderSelected",
              video: {
                youtubeVideoId: first.videoId || first.youtubeVideoId,
                title: first.title,
                artist: first.artist,
                position: 0,
              },
              timestamp: new Date().toISOString(),
            });
          }
        }
      } catch (wsError) {
        logger.warn("Falha ao emitir reorderSelected (não bloqueante)", wsError);
      }
    } catch (error) {
      logger.error("❌ Erro ao notificar reordenação:", error);
      // Não falhar o processo principal por erro de notificação
    }
  }

  /**
   * 🔧 REORDENAR MANUALMENTE (para testes)
   */
  async manualReorder(restaurantId: string): Promise<ReorderResult> {
    logger.info(
      `🔧 Reordenação manual solicitada para restaurante ${restaurantId}`
    );
    return await this.reorderRestaurantPlaylists(restaurantId);
  }
}

// Exportar instância singleton
export const playlistReorderService = PlaylistReorderService.getInstance();
