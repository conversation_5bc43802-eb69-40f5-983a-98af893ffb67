import React, { useCallback, useEffect, useMemo, useRef, useState } from "react";
import { useParams, useSearchParams } from "react-router-dom";
import { motion, AnimatePresence } from "framer-motion";
import { Music, Volume2, Maximize, Minimize, Timer } from "lucide-react";
import { buildApiUrl } from "@/config/api";
import { useWebSocket } from "@/services/websocket";
import { cooldownService } from "@/services/cooldownService";
import { usePersistentTimer } from "@/hooks/usePersistentTimer";
import type { WebSocketEvents } from "@/types";

// Utilitário: normaliza/valida um YouTube Video ID
const normalizeYouTubeId = (raw: any): string | null => {
  if (!raw) return null;
  let s = String(raw).trim();
  // Se vier uma URL, extrair o ID
  try {
    if (/^https?:\/\//i.test(s) || s.includes("youtu")) {
      // Tenta URL padrão com ?v=ID
      const vParam = /[?&#]v=([a-zA-Z0-9_-]{11})/.exec(s);
      if (vParam) return vParam[1];
      // Formatos /embed/ID, youtu.be/ID, /shorts/ID
      const pathId = /(?:embed|shorts)\/([a-zA-Z0-9_-]{11})/.exec(s);
      if (pathId) return pathId[1];
      const shortId = /youtu\.be\/([a-zA-Z0-9_-]{11})/.exec(s);
      if (shortId) return shortId[1];
    }
  } catch {}
  // Se a string tiver mais de 11 e contiver um provável ID, captura o primeiro token de 11 chars
  const token11 = /([a-zA-Z0-9_-]{11})/.exec(s)?.[1] || null;
  if (token11) return token11;
  // Se já vier com 11 válido
  if (/^[a-zA-Z0-9_-]{11}$/.test(s)) return s;
  return null;
};

interface Song {
  id: string;
  title: string;
  artist: string;
  duration: number;
  formattedDuration: string;
  thumbnailUrl: string;
  channelName: string;
  youtubeVideoId: string;
  upvotes?: number;
  downvotes?: number;
  voteCount?: number;
  isPaid?: boolean;
  paymentAmount?: number;
  clientName?: string;
  tableNumber?: number;
  createdAt?: string;
  score?: number;
  isInCooldown?: boolean;
  cooldownTimeLeft?: number; // em segundos
}

interface Restaurant {
  id: string;
  name: string;
  description: string;
  isActive: boolean;
  isOpen: boolean;
}

interface VoteActivity {
  id: string;
  type: 'vote' | 'supervote' | 'suggestion';
  songTitle: string;
  clientName?: string;
  tableNumber?: number;
  amount?: number;
  timestamp: number;
}

interface Popup {
  id: string;
  text: string;
  type: VoteActivity['type'];
}

const DisplayPage: React.FC = () => {
  const { restaurantId = "" } = useParams();
  const [searchParams] = useSearchParams();
  // Persistência local por restaurante
  const LS_KEY = useMemo(() => `display_state_${restaurantId}`, [restaurantId]);

  // Query params
  const theme = searchParams.get('theme') || 'client'; // 'client' ou 'dark'
  const popupsEnabled = (searchParams.get('popups') ?? 'on') !== 'off';
  // Limite opcional de duração (minutos) para preempção de músicas muito longas
  const maxTrackMinutes = useMemo(() => {
    const v = Number(searchParams.get('maxmin'));
    return Number.isFinite(v) && v > 0 ? v : 0; // 0 = sem limite
  }, [searchParams]);

  // Estados principais
  const [restaurant, setRestaurant] = useState<Restaurant | null>(null);
  const [currentlyPlaying, setCurrentlyPlaying] = useState<Song | null>(null);
  const [playlistSongs, setPlaylistSongs] = useState<Song[]>([]);
  const [queueVideoIds, setQueueVideoIds] = useState<string[]>([]);
  const [recentActivity, setRecentActivity] = useState<VoteActivity[]>([]);
  const [basePlaylist, setBasePlaylist] = useState<Song[]>([]);
  const [baseIndex, setBaseIndex] = useState<number>(0);
  // Próxima música agendada por snapshot (reavaliada a cada 5 min)
  const [scheduledNext, setScheduledNext] = useState<Song | null>(null);
  const lastSnapshotAtRef = useRef<number>(0);
  // Observação: sem mocks. Próxima música é decidida localmente a partir do ranking real e playlist base.

  // Pop-ups de eventos
  const [popups, setPopups] = useState<Popup[]>([]);

  // Estados de controle
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showOverlay, setShowOverlay] = useState(true);
  const [audioEnabled, setAudioEnabled] = useState(false);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const audioRequestedRef = useRef<boolean>(false);

  // WebSocket
  const { on, off, joinRestaurant, isConnected } = useWebSocket();

  // Timer persistente para reordenação
  const { countdown, isGracePeriod } = usePersistentTimer(restaurantId || '');

  // Inicializar timer de cooldown
  useEffect(() => {
    if (restaurantId) {
      cooldownService.startCooldownTimer();
    }
  }, [restaurantId]);

  // Refs
  const playerRef = useRef<any>(null);
  const playerContainerRef = useRef<HTMLDivElement | null>(null);
  const lastVideoIdRef = useRef<string | null>(null);
  const lastLoadTsRef = useRef<number>(0);
  const playerReadyRef = useRef<boolean>(false);
  const pendingVideoIdRef = useRef<string | null>(null);
  const endTriggeredRef = useRef<boolean>(false);
  const lastStateRef = useRef<number | null>(null);
  const joinedRef = useRef<string | null>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  // ID estável para evitar recriações do player
  const containerId = useMemo(() => 'yt_player_container', []);

  // Refs para usar em handlers sem recriar o player
  const playNextRef = useRef<() => void>(() => {});
  const currentIdRef = useRef<string | null>(null);
  const scheduledNextRef = useRef<Song | null>(null);
  const restoredOnceRef = useRef<boolean>(false);
  // Controle de idempotência para notificar fim ao backend
  const endReportTsRef = useRef<number>(0);
  const endReportVideoIdRef = useRef<string | null>(null);

  // Scheduler para coalescer recargas de ranking/fila e evitar bursts
  const refreshTimerRef = useRef<any>(null);
  const refreshFlagsRef = useRef<{ queue: boolean; ranking: boolean }>({ queue: false, ranking: false });
  // Ref para callback de refresh externo (definido mais abaixo)
  const refreshRef = useRef<() => void>(() => {});

  // Habilitar áudio após interação do usuário (políticas de autoplay)
  const forceUnmute = useCallback((attempt = 0) => {
    try {
      const player = playerRef.current;
      if (!player || !playerReadyRef.current) return;
      if (typeof player.unMute === 'function') player.unMute();
      if (typeof player.setVolume === 'function') player.setVolume(80);
      if (typeof player.playVideo === 'function') player.playVideo();
    } catch {}
    if (attempt < 2) {
      setTimeout(() => forceUnmute(attempt + 1), attempt === 0 ? 100 : 400);
    }
  }, []);

  const enableAudioNow = useCallback(() => {
    // Marca intenção do usuário; se o player ainda não estiver pronto, faremos no onReady/onStateChange
    audioRequestedRef.current = true;
    if (audioEnabled) return;
    try {
      const player = playerRef.current;
      const ready = playerReadyRef.current;
      if (player && ready) {
        forceUnmute();
        setAudioEnabled(true);
      }
    } catch {}
  }, [audioEnabled, forceUnmute]);

  // Funções de carregamento de dados
  const fetchData = useCallback(async (endpoint: string) => {
    const url = buildApiUrl(endpoint);
    try {
      const response = await fetch(url, {
        method: 'GET',
        headers: { 'Content-Type': 'application/json' },
      });
      if (!response.ok) {
        throw new Error(`Erro HTTP: ${response.status}`);
      }
      return await response.json();
    } catch (err) {
      console.warn(`Falha ao carregar ${endpoint}: ${err instanceof Error ? err.message : 'Erro desconhecido'}`);
      throw err;
    }
  }, []);

  const loadRestaurantInfo = useCallback(async () => {
    const data = await fetchData(`/restaurants/${restaurantId}`);
    if (data.success && data.restaurant) setRestaurant(data.restaurant);
  }, [restaurantId, fetchData]);

  // Removido: sem fallback local; confiamos no backend

  const loadCurrentlyPlaying = useCallback(async () => {
    try {
      const data = await fetchData(`/playback/${restaurantId}/state`);
      const current = data?.playbackState?.currentSong;
      if (data.success && current) {
        setCurrentlyPlaying(
          mapToSong({
            id: current.id || current.youtubeId,
            title: current.title,
            artist: current.artist,
            youtubeVideoId: current.youtubeId,
            thumbnailUrl: current.thumbnailUrl,
            duration: current.duration,
          })
        );
        return;
      } else {
  // Sem música atual informada pelo backend; seguimos com a lógica local
      }
    } catch (err) {
      console.warn("/playback/state indisponível", err);
    }
  }, [restaurantId, fetchData]);

  const loadPlaylistRanking = useCallback(async () => {
    try {
      // USAR API REAL DE VOTOS para ranking
      const data = await fetchData(`/playback-queue/${restaurantId}`);
      if (data.queue && Array.isArray(data.queue)) {
        // Usar dados da fila real (já ordenados por votos)
        const songsWithCooldown = await Promise.all(
          data.queue.slice(0, 20).map(async (item: any) => {
            // Converter item da fila para formato Song
            const safeId = normalizeYouTubeId(item.youtubeVideoId || item.suggestionId || item.id) || '';
            const song = {
              id: item.suggestionId || `auto_${safeId}`,
              title: item.title,
              artist: item.artist,
              youtubeVideoId: safeId,
              duration: item.duration || 0,
              thumbnailUrl: item.thumbnailUrl,
              upvotes: item.likeCount || 0,
              downvotes: 0,
              score: item.likeCount || 0,
              suggestedBy: item.clientName || 'Sistema',
              createdAt: new Date(item.addedAt || Date.now()),
              isPaid: item.isPaid || false,
              position: item.position
            };
            const mappedSong = song;
            try {
              const cooldownStatus = await cooldownService.checkSongCooldown(
                restaurantId!,
                mappedSong.youtubeVideoId
              );
              return {
                ...mappedSong,
                isInCooldown: cooldownStatus.isInCooldown,
                cooldownTimeLeft: cooldownStatus.cooldownTimeLeft,
              };
            } catch (error) {
              console.warn(`Erro ao verificar cooldown para ${mappedSong.youtubeVideoId}:`, error);
              return mappedSong;
            }
          })
        );
        setPlaylistSongs(songsWithCooldown);
      } else {
        throw new Error('Formato inválido da API real');
      }
    } catch (err) {
      // FALLBACK PARA API ANTIGA
      console.warn("API real falhou, tentando fallback para ranking colaborativo", err);

      try {
        const data = await fetchData(`/collaborative-playlist/${restaurantId}/ranking?limit=20`);

        if (data.success && data.data) {
          console.log(`[Display] Usando API antiga (fallback) para ranking`);
          const songsWithCooldown = await Promise.all(
            data.data.map(async (song: any) => {
              const mappedSong = mapToSong(song);
              try {
                const cooldownStatus = await cooldownService.checkSongCooldown(
                  restaurantId!,
                  mappedSong.youtubeVideoId
                );
                return {
                  ...mappedSong,
                  isInCooldown: cooldownStatus.isInCooldown,
                  cooldownTimeLeft: cooldownStatus.cooldownTimeLeft,
                };
              } catch (error) {
                console.warn(`Erro ao verificar cooldown para ${mappedSong.youtubeVideoId}:`, error);
                return mappedSong;
              }
            })
          );
          setPlaylistSongs(songsWithCooldown);
        }
      } catch (fallbackErr) {
        console.error("Ambas APIs falharam para ranking:", fallbackErr);
      }
    }
  }, [restaurantId, fetchData]);

  // Carrega a fila autoritativa do servidor (primeiro item = atual)
  const loadPlaybackQueue = useCallback(async () => {
    try {
      const data = await fetchData(`/playback-queue/${restaurantId}`);
      if (data && (Array.isArray(data.queue) || data.currentlyPlaying)) {
        // Atual atual e próximo da fila
        const q = (data.queue || []).map(mapToSong);
        try {
          const ids = (data.queue || [])
            .map((it: any) => normalizeYouTubeId(it.youtubeVideoId || it.suggestionId || it.id))
            .filter((id: any): id is string => !!id);
          setQueueVideoIds(ids);
        } catch {}
        // Mapear atual vindo do backend (não preemptivo)
        const backendCurrent = data.currentlyPlaying
          ? mapToSong({
              id: data.currentlyPlaying.youtubeVideoId || data.currentlyPlaying.id,
              title: data.currentlyPlaying.title,
              artist: data.currentlyPlaying.artist,
              youtubeVideoId: data.currentlyPlaying.youtubeVideoId || data.currentlyPlaying.suggestionId,
              thumbnailUrl: data.currentlyPlaying.thumbnailUrl,
              duration: data.currentlyPlaying.duration,
              isPaid: data.currentlyPlaying.isPaid,
              paymentAmount: data.currentlyPlaying.paymentAmount,
            })
          : q[0] || null;
        // Só definir current se ainda não temos um, ou se é o mesmo vídeo (atualiza metadados)
        if (backendCurrent) {
          setCurrentlyPlaying((prev) => {
            if (!prev) return backendCurrent;
            if (prev.youtubeVideoId === backendCurrent.youtubeVideoId) return backendCurrent;
            return prev; // manter o atual local; sem preempção
          });
        }
        // Agendar próxima: primeiro item diferente do atual
        const curId = currentIdRef.current;
        const next = q.find((s) => s.youtubeVideoId && s.youtubeVideoId !== curId) || null;
        setScheduledNext(next);
      }
    } catch (e) {
      console.warn("Falha ao carregar playback-queue:", e);
    }
  }, [restaurantId, fetchData]);

  // Scheduler de recarga coalescida (definido após loaders para evitar TDZ)
  const scheduleRefresh = useCallback((opts?: { queue?: boolean; ranking?: boolean; delayMs?: number }) => {
    const { queue = false, ranking = false, delayMs } = opts || {};
    // Marcar flags solicitadas
    refreshFlagsRef.current = {
      queue: refreshFlagsRef.current.queue || queue,
      ranking: refreshFlagsRef.current.ranking || ranking,
    };
    // Reagendar timer (debounce)
    if (refreshTimerRef.current) clearTimeout(refreshTimerRef.current);
    const delay = typeof delayMs === 'number' ? delayMs : 400; // padrão 400ms
    refreshTimerRef.current = setTimeout(async () => {
      const { queue: doQueue, ranking: doRanking } = refreshFlagsRef.current;
      // Reset antes de executar
      refreshFlagsRef.current = { queue: false, ranking: false };
      try {
        if (doQueue) await loadPlaybackQueue();
        if (doRanking) await loadPlaylistRanking();
      } finally {
        try { refreshRef.current?.(); } catch {}
      }
    }, delay);
  }, [loadPlaybackQueue, loadPlaylistRanking]);

  const loadBasePlaylist = useCallback(async (): Promise<Song[]> => {
    const params = new URLSearchParams({ page: "1", limit: "50" });
    let songs: Song[] = [];

    try {
      // TENTAR API REAL DE VOTOS PRIMEIRO
      const data = await fetchData(`/playback-queue/${restaurantId}`);

      if (data.queue && Array.isArray(data.queue)) {
        // PROCESSAR DADOS DA API REAL (fila de votos)
        console.log(`[Display] Usando API real - ${data.queue.length} músicas na fila`);
        songs = data.queue.map((item: any) => {
          const safeId = normalizeYouTubeId(item.youtubeVideoId || item.suggestionId || item.id) || '';
          return ({
          id: item.suggestionId || `auto_${safeId}`,
          title: item.title,
          artist: item.artist,
          youtubeVideoId: safeId,
          duration: item.duration || 0,
          thumbnailUrl: item.thumbnailUrl,
          upvotes: item.likeCount || 0,
          downvotes: item.dislikeCount || 0,
          score: item.likeCount || 0,
          suggestedBy: item.clientName || 'Sistema',
          createdAt: new Date(item.addedAt || Date.now()),
          isPaid: item.isPaid || false,
          position: item.position,
          estimatedPlayTime: item.estimatedPlayTime
        })}).filter((s: Song) => !!s.youtubeVideoId);

        setBasePlaylist(songs);
      } else {
        throw new Error('Formato inválido da API real');
      }
    } catch (error) {
      // FALLBACK PARA API ANTIGA
      console.warn('[Display] API real falhou, usando fallback:', error);

      try {
        const params = new URLSearchParams({ page: "1", limit: "50" });
        const data = await fetchData(`/restaurants/${restaurantId}/playlist?${params.toString()}`);

        if (Array.isArray(data.results)) {
          console.log(`[Display] Usando API antiga (fallback) - ${data.results.length} músicas`);
          songs = data.results.map(mapToSong).filter((s: Song) => !!s.youtubeVideoId);
          setBasePlaylist(songs);
        }
      } catch (fallbackError) {
        console.error('[Display] Ambas APIs falharam:', fallbackError);
      }
    }
    return songs;
  }, [restaurantId, fetchData]);

  // Mapeador de song
  const mapToSong = (track: any): Song => {
    const yid = normalizeYouTubeId(
      track.youtubeVideoId || track.youtubeId || track.videoId || track.id || track.url || track.link
    );
    const safeId = yid || '';
    return {
      id: safeId,
      title: track.title || 'Música Sem Título',
      artist: track.artist || 'Artista Desconhecido',
      duration: track.duration || 0,
      formattedDuration: track.formattedDuration || '0:00',
      thumbnailUrl:
        track.thumbnailUrl || (safeId ? `https://img.youtube.com/vi/${safeId}/mqdefault.jpg` : ''),
      channelName: track.channelName || track.artist || 'Canal Desconhecido',
      youtubeVideoId: safeId,
      upvotes: track.upvotes || 0,
      downvotes: track.downvotes || 0,
      voteCount: track.voteCount || 0,
      isPaid: track.isPaid || false,
      paymentAmount: track.paymentAmount || 0,
      score: track.score || 0,
    };
  };

  // Tocar vídeo atual no player
  const playVideoById = useCallback((videoId: string) => {
    const yid = normalizeYouTubeId(videoId);
    if (!yid) {
      console.warn('ID de vídeo inválido, ignorando:', videoId);
      return;
    }
    const player = playerRef.current;
    // se player ainda não está pronto, guardar para tocar no onReady
    if (!player || !playerReadyRef.current) {
      pendingVideoIdRef.current = yid;
      return;
    }
    if (player) {
      // Evitar recarregar o mesmo vídeo em curto intervalo
      try {
        const now = Date.now();
        const currentData = typeof player.getVideoData === 'function' ? player.getVideoData() : null;
        const currentId = currentData?.video_id || lastVideoIdRef.current;
        const state = typeof player.getPlayerState === 'function' ? player.getPlayerState() : undefined;
        // 1 = PLAYING, 3 = BUFFERING
        if (currentId === yid && (now - lastLoadTsRef.current < 2000 || state === 1 || state === 3)) {
          return; // já está tocando/armazenado recentemente
        }
      } catch {}
      console.log("🎵 Tocando vídeo:", yid);
      if (typeof player.loadVideoById === "function") {
        player.loadVideoById({ videoId: yid });
      } else if (typeof player.cueVideoById === "function") {
        player.cueVideoById({ videoId: yid });
        if (typeof player.playVideo === "function") player.playVideo();
      }
      lastVideoIdRef.current = yid;
      lastLoadTsRef.current = Date.now();
    }
  }, []);

  // Escolha da próxima música baseada no ranking real e playlist base
  const pickNextSong = useCallback((): Song | null => {
    // Se existir uma próxima música agendada pelo snapshot, use-a
  if (scheduledNext && scheduledNext.youtubeVideoId !== currentlyPlaying?.youtubeVideoId && !scheduledNext.isInCooldown) {
      return scheduledNext;
    }
    // 1) Top votada (score > 0), que não seja a atual e que não esteja em cooldown
    const voted = [...playlistSongs]
      .filter((s) => s.youtubeVideoId !== currentlyPlaying?.youtubeVideoId && !s.isInCooldown)
      .sort((a, b) => (b.score || 0) - (a.score || 0));
    const topVoted = voted.find((s) => (s.score || 0) > 0);
    if (topVoted) return topVoted;

    // 2) Seguinte da playlist base (real). Se ainda não há atual, começa do índice 0
    if (basePlaylist.length > 0) {
      if (!currentlyPlaying) return basePlaylist[0];
      // pular músicas em cooldown
      for (let i = 1; i <= basePlaylist.length; i++) {
        const nextIdx = (baseIndex + i) % basePlaylist.length;
        if (!basePlaylist[nextIdx]?.isInCooldown) return basePlaylist[nextIdx];
      }
      return basePlaylist[(baseIndex + 1) % basePlaylist.length];
    }

    // 3) Ciclar pelo ranking se não houver votos, ignorando músicas em cooldown
    if (playlistSongs.length > 0) {
      const playable = playlistSongs.filter((s) => !s.isInCooldown);
      const currentIndex = playable.findIndex(
        (s) => s.youtubeVideoId === currentlyPlaying?.youtubeVideoId
      );
      const nextIndex = currentIndex >= 0 ? (currentIndex + 1) % playable.length : 0;
      return playable[nextIndex] || null;
    }
    return null;
  }, [playlistSongs, currentlyPlaying?.youtubeVideoId, basePlaylist, baseIndex, scheduledNext]);

  // Debounce para evitar múltiplos avanços simultâneos
  const lastAdvanceTsRef = useRef<number>(0);

  const playNextSong = useCallback(() => {
    const now = Date.now();
    if (now - lastAdvanceTsRef.current < 1500) {
      console.log('⏭️ Ignorando avanço duplicado (debounce)');
      return;
    }
    lastAdvanceTsRef.current = now;

    const next = pickNextSong();
    if (next) {
      setCurrentlyPlaying(next);
      // atualizar índice da base se a próxima veio da base
      const basePos = basePlaylist.findIndex((s) => s.youtubeVideoId === next.youtubeVideoId);
      if (basePos >= 0) setBaseIndex(basePos);
      // consumir scheduledNext, caso tenha sido usado
      setScheduledNext((prev) => (prev && prev.youtubeVideoId === next.youtubeVideoId ? null : prev));
      playVideoById(next.youtubeVideoId);

      // Recarregar ranking/fila após escolher próxima para refletir cooldowns
      scheduleRefresh({ ranking: true, queue: true, delayMs: 1000 });
    } else {
      console.warn("Nenhuma próxima música disponível");
      // Mesmo sem próxima música, recarregar ranking/fila para refletir mudanças
      scheduleRefresh({ ranking: true, queue: true, delayMs: 1000 });
    }
  }, [pickNextSong, basePlaylist, playVideoById, scheduleRefresh]);

  // Notificar backend que a música atual terminou (idempotente)
  const notifySongEnded = useCallback(async () => {
    try {
      const vid = currentIdRef.current;
      if (!restaurantId || !vid) return;
      const safeId = normalizeYouTubeId(vid);
      const now = Date.now();
      if (endReportVideoIdRef.current === (safeId || vid) && now - endReportTsRef.current < 3000) {
        // Evita notificar múltiplas vezes o mesmo fim em janela curta
        return;
      }
      endReportVideoIdRef.current = safeId || vid;
      endReportTsRef.current = now;
      await fetch(buildApiUrl(`/playback-queue/${restaurantId}/song-ended`), {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ youtubeVideoId: safeId || vid }),
      });
    } catch (e) {
      console.warn('Falha ao notificar término da música ao backend:', e);
    }
  }, [restaurantId]);

  // Snapshot de votos a cada 5 minutos para definir a próxima música após a atual
  const refreshVoteSnapshot = useCallback(() => {
    const now = Date.now();
    // Janela de votação: 5 minutos entre snapshots
    if (now - lastSnapshotAtRef.current < 5 * 60 * 1000) return;
    // top votada com score > 0 e diferente da atual
    const voted = [...playlistSongs]
      .filter((s) => s.youtubeVideoId !== currentlyPlaying?.youtubeVideoId)
      .sort((a, b) => (b.score || 0) - (a.score || 0));
    const topVoted = voted.find((s) => (s.score || 0) > 0) || null;

    let candidate: Song | null = topVoted;
    if (!candidate) {
      // sem votos, seguir a playlist base normalmente
      if (basePlaylist.length > 0) {
        const nextIdx = (baseIndex + 1) % basePlaylist.length;
        candidate = basePlaylist[nextIdx] || null;
      } else if (playlistSongs.length > 0) {
        // fallback: ciclar ranking
        const currentIndex = playlistSongs.findIndex(
          (s) => s.youtubeVideoId === currentlyPlaying?.youtubeVideoId
        );
        const nextIndex = currentIndex >= 0 ? (currentIndex + 1) % playlistSongs.length : 0;
        candidate = playlistSongs[nextIndex] || null;
      }
    }

    // Evitar agendar a mesma da atual
    if (candidate && candidate.youtubeVideoId === currentlyPlaying?.youtubeVideoId) {
      candidate = null;
    }
    // Evitar reemitir/agendar a mesma próxima já definida (idempotência)
    const prevNextId = scheduledNextRef.current?.youtubeVideoId || null;
    const newNextId = candidate?.youtubeVideoId || null;
    if (prevNextId === newNextId) {
      lastSnapshotAtRef.current = now;
      return;
    }

    setScheduledNext(candidate || null);
    lastSnapshotAtRef.current = now;
    if (candidate) {
      console.log("⏭️ Próxima música agendada pelo snapshot:", candidate.title);
    }
  }, [playlistSongs, currentlyPlaying?.youtubeVideoId, basePlaylist, baseIndex]);

  // ...existing code...

  // Atualizar refs
  useEffect(() => { playNextRef.current = () => playNextSong(); }, [playNextSong]);
  useEffect(() => { currentIdRef.current = currentlyPlaying?.youtubeVideoId || null; }, [currentlyPlaying?.youtubeVideoId]);
  useEffect(() => { scheduledNextRef.current = scheduledNext; }, [scheduledNext]);
  useEffect(() => {
    refreshRef.current = refreshVoteSnapshot;
  }, [refreshVoteSnapshot]);

  useEffect(() => {
    const handler = () => enableAudioNow();
    // Mantém ouvintes até que o áudio seja realmente habilitado
    if (!audioEnabled) {
      window.addEventListener('click', handler);
      window.addEventListener('keydown', handler);
      window.addEventListener('touchstart', handler, { passive: true } as any);
    }
    return () => {
      window.removeEventListener('click', handler as any);
      window.removeEventListener('keydown', handler as any);
      window.removeEventListener('touchstart', handler as any);
    };
  }, [enableAudioNow, audioEnabled]);

  // Carregamento inicial
  useEffect(() => {
    if (!restaurantId) return;
    const loadInitialData = async () => {
      setLoading(true);
      try {
        // 1) Restaura estado salvo (se recente)
        if (!restoredOnceRef.current) {
          try {
            const raw = localStorage.getItem(LS_KEY);
            if (raw) {
              const saved = JSON.parse(raw);
              const age = Date.now() - (saved?.ts || 0);
              // Aceita estado salvo se tiver até 30 minutos
              if (age >= 0 && age <= 30 * 60 * 1000) {
                if (saved.currentlyPlaying?.youtubeVideoId) {
                  setCurrentlyPlaying(mapToSong(saved.currentlyPlaying));
                }
                if (Number.isInteger(saved.baseIndex)) setBaseIndex(saved.baseIndex);
                if (saved.scheduledNext?.youtubeVideoId) {
                  setScheduledNext(mapToSong(saved.scheduledNext));
                }
                restoredOnceRef.current = true;
              }
            }
          } catch {}
        }

  const [_, __, ___, baseSongs] = await Promise.all([
          loadRestaurantInfo(),
          loadCurrentlyPlaying(),
          loadPlaylistRanking(),
          loadBasePlaylist(),
        ]);
  // Carregar fila autoritativa (atual/próxima)
  await loadPlaybackQueue();
        // Se backend não informou currentSong, iniciar pela playlist do sistema (primeira faixa válida)
        const hadRestored = restoredOnceRef.current && !!currentIdRef.current;
        if (!hadRestored && !currentlyPlaying) {
          const firstValid = (baseSongs && baseSongs[0]) || null;
          const start = firstValid || pickNextSong();
          if (start && start.youtubeVideoId) {
            setCurrentlyPlaying(start);
            setBaseIndex(() => {
              const idx = baseSongs.findIndex(s => s.youtubeVideoId === start.youtubeVideoId);
              return idx >= 0 ? idx : 0;
            });
            playVideoById(start.youtubeVideoId);
          }
        }
      } catch {
        setError("Erro ao carregar dados do restaurante");
      } finally {
        setLoading(false);
      }
    };
    loadInitialData();

    // Snapshot inicial após bootstrap
    refreshVoteSnapshot();

    // Atualizar ranking e snapshot periodicamente (mantém ordem por votos atualizada)
    const snapshotInterval = setInterval(() => {
      loadPlaylistRanking().finally(() => {
        refreshRef.current?.();
      });
    }, 300000);
    return () => clearInterval(snapshotInterval);
  }, [restaurantId]);

  // Atualiza ranking ao focar a janela ou quando a aba ficar visível novamente
  useEffect(() => {
    if (!restaurantId) return;
    const handleRefocus = () => {
      loadPlaylistRanking().finally(() => {
        refreshRef.current?.();
      });
    };
    const visHandler = () => {
      if (document.visibilityState === 'visible') handleRefocus();
    };
    window.addEventListener('focus', handleRefocus);
    document.addEventListener('visibilitychange', visHandler);
    return () => {
      window.removeEventListener('focus', handleRefocus);
      document.removeEventListener('visibilitychange', visHandler);
    };
  }, [restaurantId, loadPlaylistRanking]);

  // WebSocket
  useEffect(() => {
    if (!restaurantId) return;

    const handleQueueUpdate = (payload?: any) => {
      try {
        const arr = Array.isArray(payload?.queue) ? payload.queue : null;
        if (arr && arr.length > 0) {
          const mapped = arr.map(mapToSong);
          // Não preemptivo: só definimos current se ainda não houver nada tocando
          setCurrentlyPlaying((prev) => prev || mapped[0] || null);
          // Agenda como próxima a primeira da fila que seja diferente da atual
          const curId = currentIdRef.current;
          const next = mapped.find((m) => m.youtubeVideoId && m.youtubeVideoId !== curId) || null;
          setScheduledNext(next || null);
          try {
            const ids = arr
              .map((it: any) => normalizeYouTubeId(it.youtubeVideoId || it.suggestionId || it.id))
              .filter((id: any): id is string => !!id);
            setQueueVideoIds(ids);
          } catch {}
        } else {
          // Sem payload estruturado: recarregar autoritativamente
          scheduleRefresh({ queue: true });
        }
      } catch (e) {
        console.warn("Falha ao aplicar queue-update no Display:", e);
      } finally {
        // Atualizar ranking de forma coalescida
        scheduleRefresh({ ranking: true });
      }
    };
    // Evento autoritativo: servidor anuncia o próximo vídeo após reordenação
    const handleReorderSelected = (payload: any) => {
      try {
        const vid = normalizeYouTubeId(payload?.video?.youtubeVideoId);
        if (!vid) return;

        // Interpretar reorderSelected como "próxima selecionada pelo servidor",
        // não como preempção imediata da faixa atual.
        const mappedNext = mapToSong({
          id: payload?.video?.suggestionId || vid,
          title: payload?.video?.title,
          artist: payload?.video?.artist,
          youtubeVideoId: vid,
          thumbnailUrl: payload?.video?.thumbnailUrl,
          duration: payload?.video?.duration,
          isPaid: payload?.video?.isPaid,
          paymentAmount: payload?.video?.paymentAmount,
        });

        // Se nada estiver tocando, podemos usá-la como atual; caso contrário, apenas agenda
        setCurrentlyPlaying((prev) => (prev ? prev : mappedNext));
        setScheduledNext(mappedNext);

        // Se este vídeo é parte da base, alinhar baseIndex para quando tocar
        const basePos = basePlaylist.findIndex((s) => s.youtubeVideoId === vid);
        if (basePos >= 0) setBaseIndex(basePos);
      } catch (e) {
        console.warn("Falha ao processar reorderSelected:", e);
      }
    };
    const handleVoteUpdate = (data: any) => {
      const activity: VoteActivity = {
        id: `${Date.now()}-${Math.random()}`,
        type: data.isPaid ? 'supervote' : 'vote',
        songTitle: data.title || 'Música',
        clientName: data.clientName,
        tableNumber: data.tableNumber,
        amount: data.amount,
        timestamp: Date.now()
      };
      setRecentActivity(prev => [activity, ...prev.slice(0, 9)]);
      if (popupsEnabled) {
        addPopup(`Novo ${data.isPaid ? 'SuperVoto' : 'voto'} em "${data.title}"`, activity.type);
      }
  scheduleRefresh({ ranking: true });
    };
  const handleSuggestionUpdate = (data: WebSocketEvents["new-suggestion"]) => {
      const activity: VoteActivity = {
        id: `${Date.now()}-${Math.random()}`,
        type: 'suggestion',
        songTitle: data.title || 'Nova música',
        timestamp: Date.now()
      };
      setRecentActivity(prev => [activity, ...prev.slice(0, 9)]);
      if (popupsEnabled) {
        addPopup(`Nova sugestão: "${data.title}"`, 'suggestion');
      }
  scheduleRefresh({ ranking: true });
    };
  const handleNowPlaying = (data: any) => {
      const announced = mapToSong({
        id: data?.id || data?.youtubeId,
        title: data?.title,
        artist: data?.artist,
        youtubeVideoId: data?.youtubeId,
        thumbnailUrl: data?.thumbnailUrl,
        duration: data?.duration,
      });
      // Não preemptivo: se já estamos tocando outra música, apenas agende a anunciada
      setCurrentlyPlaying((prev) => {
        if (!prev) return announced; // sem atual, pode iniciar pela anunciada
        if (prev.youtubeVideoId === announced.youtubeVideoId) {
          // mesma faixa: atualiza metadados (thumb/duração) sem reiniciar
          return { ...prev, ...announced };
        }
        // diferente da atual: manter atual e agendar a próxima
        setScheduledNext((cur) => (cur?.youtubeVideoId === announced.youtubeVideoId ? cur : announced));
        return prev;
      });
    };

    const handlePlaylistReordered = (_data: any) => {
      scheduleRefresh({ queue: true, ranking: true });
    };

    const handleRankingSnapshot = (snap: any) => {
      try {
        const next = snap?.nextTrack ? mapToSong(snap.nextTrack) : null;
        if (next && next.youtubeVideoId !== currentIdRef.current) {
          setScheduledNext(next);
        }
      } catch {}
    };

    // Handler para quando uma música termina (via WebSocket)
  const handleSongEnded = (data: any) => {
      console.log('🎵 Música terminou (WebSocket):', data);
      // Recarregar ranking e fila para refletir cooldowns atualizados
  scheduleRefresh({ ranking: true, queue: true, delayMs: 500 });
    };

    if (joinedRef.current !== restaurantId) {
      joinRestaurant(restaurantId);
      joinedRef.current = restaurantId;
    }
  on('queue-update', handleQueueUpdate as any);
    on('reorderSelected' as any, handleReorderSelected as any);
    on('vote-update', handleVoteUpdate);
    on('new-suggestion', handleSuggestionUpdate);
    on('now-playing', handleNowPlaying as any);
  on('playlistReordered' as any, handlePlaylistReordered as any);
  on('ranking-snapshot' as any, handleRankingSnapshot as any);
  on('song-ended' as any, handleSongEnded as any);
  // Compatibilidade com backend que emite 'track_ended'
  on('track_ended' as any, handleSongEnded as any);
  // Quando o ranking muda (votos), recarregar fila/ranking para refletir a nova ordem
  on('ranking-update' as any, handlePlaylistReordered as any);
  // Compat: voto normal também deve disparar recarga
  on('normalVoteReceived' as any, handlePlaylistReordered as any);

    return () => {
    off('queue-update', handleQueueUpdate as any);
      off('reorderSelected' as any, handleReorderSelected as any);
      off('vote-update', handleVoteUpdate);
      off('new-suggestion', handleSuggestionUpdate);
      off('now-playing', handleNowPlaying as any);
    off('playlistReordered' as any, handlePlaylistReordered as any);
    off('ranking-snapshot' as any, handleRankingSnapshot as any);
  off('song-ended' as any, handleSongEnded as any);
  off('track_ended' as any, handleSongEnded as any);
  off('ranking-update' as any, handlePlaylistReordered as any);
  off('normalVoteReceived' as any, handlePlaylistReordered as any);
    };
  }, [restaurantId, joinRestaurant, on, off, loadPlaylistRanking, loadPlaybackQueue, popupsEnabled, playVideoById, refreshVoteSnapshot, notifySongEnded]);

  // Inicializar YouTube Player de forma robusta
  let ytApiPromise: Promise<void> | null = (window as any).__ytApiPromise || null;
  const ensureYouTubeAPI = () => {
    if (typeof window !== 'undefined' && (window as any).YT && (window as any).YT.Player) {
      return Promise.resolve();
    }
    if (!ytApiPromise) {
      ytApiPromise = new Promise<void>((resolve) => {
        const prev = (window as any).onYouTubeIframeAPIReady;
        (window as any).onYouTubeIframeAPIReady = () => {
          try { if (typeof prev === 'function') prev(); } catch {}
          resolve();
        };
        const tag = document.createElement('script');
        tag.src = 'https://www.youtube.com/iframe_api';
        document.body.appendChild(tag);
      });
      (window as any).__ytApiPromise = ytApiPromise;
    }
    return ytApiPromise;
  };

  // Instanciar player uma única vez (com reintentos + observação do DOM caso o container ainda não esteja disponível)
  useEffect(() => {
    let destroyed = false;
    let retryTimer: any;
    let domObserver: MutationObserver | null = null;

    const scheduleRetry = (attempt: number) => {
      // Backoff: até 20 tentativas a cada 250ms (~5s); depois, 1000ms contínuo
      const delay = attempt < 20 ? 250 : 1000;
      retryTimer = setTimeout(() => tryCreatePlayer(attempt + 1), delay);
    };

    const watchForMount = (attempt: number) => {
      if (domObserver || destroyed || playerRef.current) return;
      try {
        domObserver = new MutationObserver(() => {
          const el = playerContainerRef.current || document.getElementById(containerId);
          if (el && (window as any).YT?.Player && !playerRef.current) {
            tryCreatePlayer(attempt + 1);
          }
        });
        domObserver.observe(document.body, { childList: true, subtree: true });
      } catch {}
    };

    const tryCreatePlayer = async (attempt = 0) => {
      if (destroyed || playerRef.current) return;
      await ensureYouTubeAPI();
      if (destroyed || playerRef.current) return;

      const YT = (window as any).YT;
      if (!YT || !YT.Player) {
        scheduleRetry(attempt);
        return;
      }

      const mountEl = playerContainerRef.current || document.getElementById(containerId);
      if (!mountEl) {
        if (attempt === 0) {
          console.log('🔄 Aguardando container do player...');
        } else if (attempt < 5) {
          console.log(`🔄 Tentativa ${attempt + 1}/10 - Aguardando container...`);
        }
        // Continuar reintentando e observar o DOM até o container existir
        watchForMount(attempt);
        scheduleRetry(attempt);
        return;
      }

      if (destroyed || playerRef.current) return;
      playerRef.current = new YT.Player(mountEl, {
        height: '100%',
        width: '100%',
        host: 'https://www.youtube-nocookie.com',
        playerVars: {
          autoplay: 1,
          controls: 0,
          rel: 0,
          modestbranding: 1,
          fs: 1,
          enablejsapi: 1,
          origin: window.location.origin,
          playsinline: 1
        },
        events: {
          onReady: () => {
            console.log('🎵 Player pronto');
            try { playerReadyRef.current = true; } catch {}
            try {
              // Só muta automaticamente se o usuário ainda não solicitou áudio
              if (!audioRequestedRef.current && !audioEnabled) {
                playerRef.current?.mute?.();
              }
              const iframe: HTMLIFrameElement | null = playerRef.current?.getIframe?.() || null;
              if (iframe) {
                iframe.setAttribute('allow', 'autoplay; fullscreen; encrypted-media; picture-in-picture');
                // manter apenas 'allow'; não mexer em allowfullscreen para evitar avisos de precedência
              }
              // Se o usuário já sinalizou intenção de áudio, liberar som
              if (audioRequestedRef.current || audioEnabled) {
                forceUnmute();
              }
            } catch {}
            // Se havia um vídeo pendente, tocar agora
            const pending = pendingVideoIdRef.current && normalizeYouTubeId(pendingVideoIdRef.current);
            if (pending) {
              pendingVideoIdRef.current = null;
              setTimeout(() => playVideoById(pending), 0);
              return;
            }
            const curId = currentIdRef.current;
            const sched = scheduledNextRef.current;
            if (!curId && sched) {
              playNextRef.current?.();
            } else if (curId) {
              const safe = normalizeYouTubeId(curId);
              if (safe) setTimeout(() => playVideoById(safe), 0);
            }
          },
          onStateChange: (event: any) => {
            lastStateRef.current = event?.data ?? null;
            // 0 = ended
            if (event.data === 0) {
              // Evita duplicidade com o watcher de progresso
              if (endTriggeredRef.current) {
                console.log('⏭️ onStateChange: fim já tratado; ignorando');
                return;
              }
              console.log('🎵 Música terminou, escolhendo próxima pelo ranking/base');
              endTriggeredRef.current = true;
              try {
                // marcar cooldown localmente para a atual
                const curId = currentIdRef.current;
                if (restaurantId && curId) {
                  cooldownService.markSongInCooldown(restaurantId, curId);
                }
              } catch {}
              // Notificar o backend para marcar como concluída (idempotente)
              notifySongEnded();
              playNextRef.current?.();
            }
            // Se começar a tocar (1), e o usuário já clicou para liberar áudio, garantir unMute
      if (event.data === 1 && (audioRequestedRef.current || audioEnabled)) {
              try {
        forceUnmute();
              } catch {}
              if (!audioEnabled) setAudioEnabled(true);
            }
          },
          onError: (event: any) => {
            const code = event?.data;
            const info =
              code === 2 ? 'Parâmetro inválido (verifique videoId ou formato)'
              : code === 5 ? 'Erro HTML5 (tente recarregar)'
              : code === 100 ? 'Vídeo removido/privado'
              : code === 101 || code === 150 ? 'Embed desativado pelo proprietário'
              : 'Erro desconhecido';
            console.error('❌ Erro no player:', code, '-', info);
            // Códigos comuns: 101/150 (embed disabled), 100 (removido), 5 (HTML5), 2 (param inválido)
            // Tentar próxima música para evitar travar o display
            playNextRef.current?.();
          }
        }
      });
    };

    tryCreatePlayer();
    return () => {
      destroyed = true;
      if (retryTimer) clearTimeout(retryTimer);
      try { domObserver?.disconnect(); } catch {}
      domObserver = null;
      try { playerRef.current?.destroy?.(); } catch {}
    };
  }, [containerId]);

  // Reproduzir quando a música atual muda
  useEffect(() => {
    if (!currentlyPlaying?.youtubeVideoId) return;
  // nova música -> reset de fim
  endTriggeredRef.current = false;
    const ready = playerReadyRef.current;
    if (!ready) {
      // onReady tocará o pendente
      pendingVideoIdRef.current = currentlyPlaying.youtubeVideoId;
      return;
    }
    const t = setTimeout(() => playVideoById(currentlyPlaying.youtubeVideoId), 50);
    return () => clearTimeout(t);
  }, [currentlyPlaying?.youtubeVideoId, playVideoById]);

  // Persistir estado principal no localStorage (idempotente)
  useEffect(() => {
    if (!restaurantId) return;
    try {
      const payload = {
        ts: Date.now(),
        currentlyPlaying: currentlyPlaying ? {
          id: currentlyPlaying.id,
          title: currentlyPlaying.title,
          artist: currentlyPlaying.artist,
          duration: currentlyPlaying.duration,
          formattedDuration: currentlyPlaying.formattedDuration,
          thumbnailUrl: currentlyPlaying.thumbnailUrl,
          channelName: currentlyPlaying.channelName,
          youtubeVideoId: currentlyPlaying.youtubeVideoId,
          upvotes: currentlyPlaying.upvotes,
          downvotes: currentlyPlaying.downvotes,
          voteCount: currentlyPlaying.voteCount,
          isPaid: currentlyPlaying.isPaid,
          paymentAmount: currentlyPlaying.paymentAmount,
          clientName: currentlyPlaying.clientName,
          tableNumber: currentlyPlaying.tableNumber,
          createdAt: currentlyPlaying.createdAt,
          score: currentlyPlaying.score,
        } : null,
        baseIndex,
        scheduledNext: scheduledNext ? {
          id: scheduledNext.id,
          title: scheduledNext.title,
          artist: scheduledNext.artist,
          duration: scheduledNext.duration,
          formattedDuration: scheduledNext.formattedDuration,
          thumbnailUrl: scheduledNext.thumbnailUrl,
          channelName: scheduledNext.channelName,
          youtubeVideoId: scheduledNext.youtubeVideoId,
          upvotes: scheduledNext.upvotes,
          downvotes: scheduledNext.downvotes,
          voteCount: scheduledNext.voteCount,
          isPaid: scheduledNext.isPaid,
          paymentAmount: scheduledNext.paymentAmount,
          clientName: scheduledNext.clientName,
          tableNumber: scheduledNext.tableNumber,
          createdAt: scheduledNext.createdAt,
          score: scheduledNext.score,
        } : null,
      };
      localStorage.setItem(LS_KEY, JSON.stringify(payload));
    } catch {}
  }, [restaurantId, currentlyPlaying?.youtubeVideoId, baseIndex, scheduledNext?.youtubeVideoId]);

  // Iniciar quando há próxima agendada e nada tocando
  useEffect(() => {
    const ready = playerReadyRef.current;
    if (ready && !currentlyPlaying && scheduledNext) {
      // aplica cooldown preventivo na música agendada, ela será reproduzida e removida, sem poluir a base
      try {
        if (restaurantId && scheduledNext.youtubeVideoId) {
          cooldownService.markSongInCooldown(restaurantId, scheduledNext.youtubeVideoId);
        }
      } catch {}
      playNextSong();
    }
  }, [scheduledNext, currentlyPlaying, playNextSong]);

  // Watcher de progresso: avança quando estiver no fim, mesmo se onStateChange falhar
  useEffect(() => {
    let timer: any;
  const tick = () => {
      try {
        const player = playerRef.current;
        if (!player || !playerReadyRef.current) return;
        const state = typeof player.getPlayerState === 'function' ? player.getPlayerState() : undefined;
        if (state !== 1 && state !== 2 && state !== 3) return; // playing/paused/buffering
        // Se o usuário solicitou áudio, garantir que não esteja mutado
        if (audioRequestedRef.current) {
          try {
            const muted = typeof player.isMuted === 'function' ? player.isMuted() : false;
            if (muted) {
              player.unMute?.();
              player.setVolume?.(80);
            }
          } catch {}
        }
        const dur = typeof player.getDuration === 'function' ? player.getDuration() : 0;
        const cur = typeof player.getCurrentTime === 'function' ? player.getCurrentTime() : 0;
        // Cap de duração opcional (preempção por música longa)
        if (maxTrackMinutes > 0) {
          const capSec = maxTrackMinutes * 60;
          if (cur >= capSec - 0.25 && (dur === 0 || dur >= capSec)) {
            if (!endTriggeredRef.current) {
              endTriggeredRef.current = true;
              console.log(`⏭️ Preempção por cap de duração (${maxTrackMinutes}min) atingido`);
              try {
                notifySongEnded();
              } catch {}
              playNextSong();
              return;
            }
          }
        }
        if (dur && cur && dur - cur <= 1.0) {
          if (!endTriggeredRef.current) {
            endTriggeredRef.current = true;
            console.log('⏭️ Avançando pelo watcher (fim detectado)');
            // Notificar backend por segurança (idempotente)
            notifySongEnded();
            playNextSong();
          }
        }
      } catch {}
    };
    timer = setInterval(tick, 5000); // Reduzido de 500ms para 5s
    return () => clearInterval(timer);
  }, [playNextSong]);

  // Fullscreen: alterna usando a Fullscreen API no container principal
  const toggleFullscreen = useCallback(() => {
    try {
      if (!document.fullscreenElement) {
        containerRef.current?.requestFullscreen?.();
      } else {
        document.exitFullscreen?.();
      }
    } catch {}
  }, []);

  // Sincroniza estado quando o fullscreen muda (ESC, F11, etc.)
  useEffect(() => {
    const handler = () => setIsFullscreen(!!document.fullscreenElement);
    document.addEventListener('fullscreenchange', handler);
    return () => document.removeEventListener('fullscreenchange', handler);
  }, []);

  // Auto-hide overlay
  useEffect(() => {
    let timeout: NodeJS.Timeout;
    const resetTimer = () => {
      setShowOverlay(true);
      clearTimeout(timeout);
      timeout = setTimeout(() => setShowOverlay(false), 8000);
    };
    document.addEventListener('mousemove', resetTimer);
    document.addEventListener('keypress', resetTimer);
    resetTimer();
    return () => {
      document.removeEventListener('mousemove', resetTimer);
      document.removeEventListener('keypress', resetTimer);
      clearTimeout(timeout);
    };
  }, []);

  // Adicionar popup
  const addPopup = (text: string, type: Popup['type']) => {
    const id = `${Date.now()}-${Math.random()}`;
    setPopups(prev => [...prev, { id, text, type }]);
    setTimeout(() => setPopups(prev => prev.filter(p => p.id !== id)), 5000);
  };

  // Formatação de tempo
  const formatTimeAgo = (timestamp: number) => {
    const diff = Date.now() - timestamp;
    const minutes = Math.floor(diff / 60000);
    if (minutes < 1) return 'agora';
    if (minutes < 60) return `${minutes}min`;
    return `${Math.floor(minutes / 60)}h`;
  };

  if (loading) return <LoadingScreen />;
  if (error && playlistSongs.length === 0 && basePlaylist.length === 0) return <ErrorScreen error={error} />;

  return (
    <div
      ref={containerRef}
      className={`relative w-screen h-screen ${theme === 'client' ? 'bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900' : 'bg-black'} text-white overflow-hidden`}
      style={{ cursor: showOverlay ? 'default' : 'none' }}
    >
  <div id={containerId} ref={playerContainerRef} className="absolute inset-0 z-10" />

      {/* Botão para habilitar áudio quando necessário */}
      {!audioEnabled && (
        <div className="absolute inset-0 z-30 flex items-center justify-center pointer-events-none">
          <button
            onClick={enableAudioNow}
            className="pointer-events-auto inline-flex items-center gap-2 bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 text-white px-6 py-3 rounded-xl shadow-xl border border-white/20 backdrop-blur-md focus:outline-none focus-visible:ring-4 focus-visible:ring-emerald-300/40"
          >
            <Volume2 className="w-5 h-5" />
            <span className="font-semibold">Ativar áudio</span>
          </button>
        </div>
      )}

      <AnimatePresence>
        {showOverlay && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="absolute inset-0 z-20 pointer-events-none"
          >
            <Footer currentlyPlaying={currentlyPlaying} />
          </motion.div>
        )}
      </AnimatePresence>

      {/* Cronômetro de reordenação - sempre visível */}
      <div className="absolute top-4 left-4 z-30 pointer-events-none">
        <motion.div
          initial={{ scale: 0.9, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          className={`inline-flex items-center gap-2 text-sm backdrop-blur-md border rounded-lg px-3 py-2 ${
            isGracePeriod
              ? 'bg-orange-500/20 border-orange-400/30'
              : 'bg-indigo-500/20 border-indigo-400/30'
          }`}
        >
          <Timer className={`w-4 h-4 ${isGracePeriod ? 'text-orange-300 animate-bounce' : 'text-indigo-300 animate-pulse'}`}/>
          <span className={`font-mono font-bold ${isGracePeriod ? 'text-orange-100' : 'text-indigo-100'}`}>
            {countdown}
          </span>
        </motion.div>
      </div>

      {/* Botão de Fullscreen */}
      {showOverlay && (
        <div className="absolute top-4 right-4 z-30 pointer-events-auto">
          <button
            onClick={toggleFullscreen}
            className="inline-flex items-center gap-2 bg-white/15 hover:bg-white/25 text-white px-3 py-2 rounded-lg border border-white/30 backdrop-blur-md focus:outline-none focus-visible:ring-4 focus-visible:ring-white/30"
            aria-label={isFullscreen ? 'Sair da tela cheia' : 'Entrar em tela cheia'}
            title={isFullscreen ? 'Sair da tela cheia' : 'Tela cheia'}
          >
            {isFullscreen ? <Minimize size={18} /> : <Maximize size={18} />}
            <span className="text-sm font-medium hidden sm:inline">{isFullscreen ? 'Sair' : 'Tela cheia'}</span>
          </button>
        </div>
      )}

      {/* Popups */}
      <div className="absolute top-20 right-4 z-30 space-y-2">
        <AnimatePresence>
          {popups.map((popup) => (
            <motion.div
              key={popup.id}
              initial={{ opacity: 0, x: 100 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: 100 }}
              className={`bg-white/20 backdrop-blur-md rounded-lg p-3 border border-white/30 flex items-center space-x-3 text-sm ${
                popup.type === 'supervote' ? 'text-yellow-300' : popup.type === 'vote' ? 'text-green-300' : 'text-blue-300'
              }`}
            >
              <span>{popup.text}</span>
            </motion.div>
          ))}
        </AnimatePresence>
      </div>
    </div>
  );
};

// Sub-componentes para melhor organização
const LoadingScreen: React.FC = () => (
  <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 flex items-center justify-center relative overflow-hidden">
    {/* Background Pattern */}
    <div className="absolute inset-0 opacity-10">
      <div className="absolute top-1/4 left-1/4 w-64 h-64 bg-white rounded-full blur-3xl animate-pulse"></div>
      <div className="absolute bottom-1/4 right-1/4 w-48 h-48 bg-purple-300 rounded-full blur-2xl animate-pulse delay-1000"></div>
    </div>

    <div className="text-center z-10 max-w-md mx-auto px-6">
      {/* Logo/Icon */}
      <div className="mb-8">
        <div className="relative">
          <div className="animate-spin rounded-full h-24 w-24 border-4 border-white/20 border-t-white mx-auto"></div>
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="w-8 h-8 bg-white rounded-full animate-pulse"></div>
          </div>
        </div>
      </div>

      {/* Loading Text */}
      <div className="space-y-4">
        <h2 className="text-3xl font-bold text-white animate-fade-in">
          🎵 Preparando Experiência Musical
        </h2>
        <p className="text-white/80 text-lg animate-fade-in delay-500">
          Carregando playlist e configurações...
        </p>

        {/* Progress Dots */}
        <div className="flex justify-center space-x-2 mt-6">
          <div className="w-3 h-3 bg-white/60 rounded-full animate-bounce"></div>
          <div className="w-3 h-3 bg-white/60 rounded-full animate-bounce delay-100"></div>
          <div className="w-3 h-3 bg-white/60 rounded-full animate-bounce delay-200"></div>
        </div>
      </div>

      {/* Status Message */}
      <div className="mt-8 text-white/60 text-sm">
        Conectando com o sistema...
      </div>
    </div>

    <style jsx>{`
      @keyframes fade-in {
        from { opacity: 0; transform: translateY(20px); }
        to { opacity: 1; transform: translateY(0); }
      }
      .animate-fade-in {
        animation: fade-in 0.8s ease-out forwards;
      }
      .delay-500 {
        animation-delay: 0.5s;
      }
    `}</style>
  </div>
);

const ErrorScreen: React.FC<{ error: string }> = ({ error }) => (
  <div className="min-h-screen bg-gradient-to-br from-red-900 via-purple-900 to-indigo-900 flex items-center justify-center">
    <div className="text-center">
      <div className="text-red-400 text-6xl mb-4">⚠️</div>
      <p className="text-white text-xl mb-4">{error}</p>
      <button
        onClick={() => window.location.reload()}
        className="bg-red-600 hover:bg-red-700 text-white px-6 py-3 rounded-lg transition-colors"
      >
        Tentar Novamente
      </button>
    </div>
  </div>
);

// Header removido conforme especificação (sem restaurante, emoji, AO VIVO, contagens)

const Footer: React.FC<{ currentlyPlaying: Song | null }> = React.memo(({ currentlyPlaying }) => (
  <motion.div
    initial={{ y: 100 }}
    animate={{ y: 0 }}
    exit={{ y: 100 }}
    className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/90 to-transparent p-6 pointer-events-auto"
  >
    <div className="grid grid-cols-1 gap-6">
      <CurrentSong song={currentlyPlaying} />
    </div>
  </motion.div>
));

const CurrentSong: React.FC<{ song: Song | null }> = ({ song }) => {
  const label = useMemo(() => {
    if (!song) return null;
    // Se veio por SuperVoto (pago) OU tem votos/score, considerar "Votação"; caso contrário, "Playlist"
    const isVotacao = Boolean(song.isPaid) || Number(song.voteCount || 0) > 0 || Number(song.score || 0) > 0;
    return isVotacao ? 'Votação' : 'Playlist';
  }, [song]);
  return (
    <div className="lg:col-span-1">
      <h3 className="text-lg font-semibold text-white mb-4 flex items-center gap-2">
        <Music className="" size={20} />
        <span>Tocando Agora</span>
        {label && (
          <span className={`text-xs px-2 py-1 rounded font-bold ${label === 'Votação' ? 'bg-green-500 text-black' : 'bg-blue-500 text-white'}`}>
            {label}
          </span>
        )}
      </h3>
      {song ? (
        <div className="bg-white/10 backdrop-blur-sm rounded-xl p-4 border border-white/20">
          <div className="flex items-center space-x-4">
            <img src={song.thumbnailUrl} alt={song.title} className="w-16 h-16 rounded-lg object-cover" />
            <div className="flex-1 min-w-0">
              <h4 className="text-white font-semibold truncate">{song.title}</h4>
              <p className="text-gray-300 text-sm truncate">{song.artist}</p>
            </div>
          </div>
        </div>
      ) : (
        <div className="bg-white/5 backdrop-blur-sm rounded-xl p-4 border border-white/10 text-center text-gray-400">
          <Music size={32} className="mx-auto mb-2 opacity-50" />
          <p>Aguardando próxima música...</p>
        </div>
      )}
    </div>
  );
};

// Ranking e Atividade Recente removidos

export default DisplayPage;