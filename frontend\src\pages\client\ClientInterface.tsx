import React, { useState, useEffect, useCallback, useRef, useMemo } from "react";
import { useParams, useSearchParams } from "react-router-dom";
import { motion, AnimatePresence } from "framer-motion";
import {
  Music,
  ThumbsUp,
  ThumbsDown,
  Search,
  Play,
  Users,
  Heart,
  Star,
  TrendingUp,
  Headphones,
  RefreshCw,
  User,
  CreditCard,
  Mic,
  Trophy,
  X,
  ChevronDown,
  ChevronUp,
  Settings,
  Maximize,
  Clock,
  MessageSquare,
  Zap,
  Eye,
} from "lucide-react";
import { toast } from "react-hot-toast";
import { buildApiUrl } from "@/config/api";
import sessionService, { ClientSession, UserStats } from "@/services/sessionService";
import { cooldownService } from "@/services/cooldownService";
import type { Suggestion as SuggestionType } from "@/types";
import { useWebSocket } from "@/services/websocket";
import MusicFilters from "@/components/music/MusicFilters";
import PaymentModal from "@/components/client/PaymentModal";
import KaraokePlayer from "@/components/client/KaraokePlayer";
import PlaybackQueue from "@/components/client/PlaybackQueue";
import ClientProfile from "@/components/client/ClientProfile";
// NewSuggestionAlert removido (fluxo legado de sugestões descontinuado)
import { TableLeaderboard } from "@/components/client/TableLeaderboard";

interface Song {
  id: string;
  title: string;
  artist: string;
  duration: number;
  formattedDuration: string;
  thumbnailUrl: string;
  channelName: string;
  viewCount: number;
  publishedAt: string;
  youtubeVideoId: string;
  genre?: string;
  isInCooldown?: boolean;
  cooldownTimeLeft?: number; // em segundos
}

type QueueItem = {
  id: string;
  suggestionId: string;
  youtubeVideoId?: string;
  title: string;
  artist: string;
  duration: number;
  thumbnailUrl?: string;
  isPaid: boolean;
  paymentAmount?: number;
  position: number;
  isInCooldown?: boolean;
  cooldownTimeLeft?: number; // em segundos
};



type SuperNote = {
  id: string;
  at: string; // ISO
  amount: number;
  voteWeight: number;
  title?: string;
  artist?: string;
  youtubeVideoId?: string;
  clientName?: string;
  tableNumber?: number;
  message?: string;
};

type PlaylistTrack = {
  id?: string;
  title: string;
  artist: string;
  duration: number;
  thumbnailUrl?: string;
  youtubeVideoId: string;
  formattedDuration?: string;
  position?: number;
  isAvailable?: boolean;
};

const fmtTime = (seconds: number) => {
  const m = Math.floor(seconds / 60);
  const s = seconds % 60;
  return `${m}:${String(s).padStart(2, "0")}`;
};

interface Suggestion {
  id: string;
  title: string;
  artist: string;
  youtubeVideoId: string;
  status: "pending" | "approved" | "rejected";
  upvotes: number;
  downvotes: number;
  score: number;
  createdAt: string;
  isPaid?: boolean;
  clientSessionId?: string;
  duration?: number;
  thumbnailUrl?: string;
}

interface Restaurant {
  id: string;
  name: string;
  description: string;
  isActive: boolean;
  isOpen: boolean;
}

/**
 * Hook personalizado para gerenciar requisições com retry
 */
const useFetchWithRetry = () => {
  return useCallback(
    async (
      url: string,
      options: RequestInit = {},
      retries = 3,
      delay = 1000
    ): Promise<Response> => {
      let lastError: Error;

      for (let attempt = 0; attempt < retries; attempt++) {
        try {
          const response = await fetch(url, options);
          if (response.ok) return response;

          // Tentar extrair mensagem de erro do corpo da resposta
          let errorMessage = `Erro na requisição: ${response.status} ${response.statusText}`;
          try {
            const errorData = await response.json();
            if (errorData.error) {
              errorMessage = errorData.error;
            } else if (errorData.message) {
              errorMessage = errorData.message;
            }
          } catch {
            // Se não conseguir parsear JSON, usar mensagem padrão
          }

          lastError = new Error(errorMessage);

          if (response.status === 401 || response.status === 403) {
            // Não tente novamente para erros de autorização
            throw lastError;
          }

          if (response.status === 409) {
            // Não tente novamente para conflitos (música já sugerida)
            throw lastError;
          }
        } catch (error) {
          lastError = error instanceof Error ? error : new Error(String(error));

          if (attempt === retries - 1) break;

          console.warn(
            `Tentativa ${
              attempt + 1
            } falhou, tentando novamente em ${delay}ms...`
          );
          await new Promise((resolve) => setTimeout(resolve, delay));
        }
      }

      throw lastError!;
    },
    []
  );
};

/**
 * Hook para gerenciar os badges do usuário
 */
const useBadgeManager = () => {
  const getBadges = useCallback((stats: UserStats): string[] => {
    const badges = [];
    if (stats.suggestionsCount >= 1) badges.push("🎵 Primeira Sugestão");
    if (stats.suggestionsCount >= 5) badges.push("🎶 Melomaníaco");
    if (stats.suggestionsCount >= 10) badges.push("🎸 DJ Amador");
    if (stats.votesCount >= 10) badges.push("👍 Crítico Musical");
    if (stats.votesCount >= 25) badges.push("⭐ Especialista");
    if (stats.streak >= 3) badges.push("🔥 Em Chamas");
    if (stats.points >= 500) badges.push("🏆 Lenda");
    return badges;
  }, []);

  return { getBadges };
};

const ClientInterface: React.FC = () => {
  const { restaurantId } = useParams<{ restaurantId: string }>();
  const [searchParams] = useSearchParams();
  const tableNumber = searchParams.get("table");
  const fetchWithRetry = useFetchWithRetry();
  const { getBadges } = useBadgeManager();

  // Agrupando estados relacionados
  // 1. Estado do restaurante
  const [restaurant, setRestaurant] = useState<Restaurant | null>(null);

  // 2. Estados de busca e playlist
  const [searchQuery, setSearchQuery] = useState("");
  const [searchResults, setSearchResults] = useState<Song[]>([]);
  const [searchLoading, setSearchLoading] = useState(false);
  const [activeFilters, setActiveFilters] = useState<string[]>([]);
  const [playlistPage, setPlaylistPage] = useState(1);
  const [playlistLoading, setPlaylistLoading] = useState(false);
  const [hasMorePlaylist, setHasMorePlaylist] = useState(true);
  const [totalPlaylistSongs, setTotalPlaylistSongs] = useState(0);

  // 3. Estado de reprodução atual (fila unificada via PlaybackQueue)
  const [currentlyPlaying, setCurrentlyPlaying] = useState<Suggestion | null>(
    null
  );
  const [showQueue, setShowQueue] = useState(true);
  const [queueCollapsed, setQueueCollapsed] = useState(false);

  // Estados adicionais para estrutura do CoverPage
  const [queue, setQueue] = useState<QueueItem[]>([]);
  const [notes, setNotes] = useState<SuperNote[]>([]);
  const [fullPlaylist, setFullPlaylist] = useState<PlaylistTrack[]>([]);
  const [playlistError, setPlaylistError] = useState<string | null>(null);
  const NOTE_TTL_MS = 2 * 60 * 1000; // Notas de supervoto expiram após 2 minutos

  // 4. Estados de sessão e usuário
  const [session, setSession] = useState<ClientSession | null>(null);
  const [userStats, setUserStats] = useState<UserStats>({
    points: 0,
    level: 1,
    badges: [],
    suggestionsCount: 0,
    votesCount: 0,
    streak: 0,
  });
  const [clientName, setClientName] = useState("");

  // 5. Estados de UI e modais
  const [loading, setLoading] = useState(true);
  const [showLevelUp, setShowLevelUp] = useState(false);
  const [showBadgeEarned, setShowBadgeEarned] = useState<string | null>(null);
  const [showNameInput, setShowNameInput] = useState(false);
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [selectedSongForPayment, setSelectedSongForPayment] =
    useState<Song | null>(null);
  const [showKaraoke, setShowKaraoke] = useState(false);
  const [karaokeData, setKaraokeData] = useState<Suggestion | null>(null);
  const [showProfile, setShowProfile] = useState(false);
  const [showLeaderboard, setShowLeaderboard] = useState(false);

  // Seções retráteis
  const [searchCollapsed, setSearchCollapsed] = useState(false);
  const [likeCollapsed, setLikeCollapsed] = useState(false);
  const [karaokeCollapsed, setKaraokeCollapsed] = useState(false);

  // Estado de cooldown
  const [songsWithCooldown, setSongsWithCooldown] = useState<Map<string, boolean>>(new Map());

  // Referência para controle de snapshot
  const lastSnapshotAtRef = useRef<number>(0);


  const { isConnected, joinRestaurant, on, off, reconnect } = useWebSocket();

  // Caso exiba ranking futuramente: mantenha um snapshot filtrado que exclui a atual e itens na fila (PlaybackQueue lida com exibição)
  const filteredRankingForUI = useMemo(() => {
    // Atualmente, ClientInterface não exibe ranking; este memo é defensivo
    // Se adicionarmos uma seção de ranking, usar este filtro como fonte.
    return [] as any[];
  }, [/* no deps until ranking state is introduced here */]);


  // Persistir restaurante atual para handshake do WebSocket e tentar reconectar
  useEffect(() => {
    if (restaurantId) {
      try {
        localStorage.setItem("currentRestaurantId", restaurantId);
        // Garante que futuras conexões WS usem o restaurante correto no auth/handshake
        reconnect();
      } catch {}
    }
  }, [restaurantId, reconnect]);

  // Entrar automaticamente na sala WS quando conectado
  useEffect(() => {
    if (isConnected && restaurantId) {
      joinRestaurant(restaurantId);
    }
  }, [isConnected, restaurantId, joinRestaurant]);

  // Inicializar timer de cooldown
  useEffect(() => {
    if (restaurantId) {
      cooldownService.startCooldownTimer();
    }
  }, [restaurantId]);

  // Likes globais agora estão integrados na PlaybackQueue; seção antiga removida

  // Funções para gerenciamento de estado e interações com API

  /**
   * Carrega informações do restaurante atual
   */
  const loadRestaurantInfo = useCallback(async () => {
    if (!restaurantId) {
      toast.error("ID do restaurante não encontrado");
      return;
    }

    try {
      const response = await fetchWithRetry(
        buildApiUrl(`/restaurants/${restaurantId}`),
        {
          headers: { "Content-Type": "application/json" },
        }
      );

      const data = await response.json();

      setRestaurant(
        data.restaurant || {
          id: restaurantId,
          name: "Restaurante Demo",
          description: "Ambiente acolhedor com música interativa",
          isActive: true,
          isOpen: true,
        }
      );

      console.log(
        `🏪 Entrou na sala do restaurante: ${
          data.restaurant?.name || "Restaurante Demo"
        }`
      );
    } catch (error) {
      console.error("Erro ao carregar restaurante:", error);

      // Dados de fallback para desenvolvimento
      setRestaurant({
        id: restaurantId,
        name: "Restaurante Demo",
        description: "Ambiente acolhedor com música interativa",
        isActive: true,
        isOpen: true,
      });

      toast.error("Erro ao carregar dados do restaurante", {
        duration: 4000,
        icon: "⚠️",
      });
    }
  }, [restaurantId, fetchWithRetry]);


  /**
   * Carrega a música que está tocando atualmente
   */
  const loadCurrentlyPlaying = useCallback(async () => {
    if (!restaurantId) {
      toast.error("ID do restaurante não encontrado");
      return;
    }

    try {
      const response = await fetchWithRetry(
        buildApiUrl(`/playback/${restaurantId}/state`),
        {
          headers: { "Content-Type": "application/json" },
        }
      );

      const data = await response.json();

      if (data.success === false) {
        throw new Error(data.message || "Erro ao carregar música atual");
      }

      setCurrentlyPlaying(data.state?.currentTrack || null);
    } catch (error) {
      console.error("Erro ao carregar música atual:", error);
      setCurrentlyPlaying(null);
    }
  }, [restaurantId, fetchWithRetry]);

  /**
   * Carrega a fila de reprodução atual
   */
  const loadPlaybackQueue = useCallback(async () => {
    if (!restaurantId) return;

    try {
      const response = await fetchWithRetry(
        buildApiUrl(`/playback-queue/${restaurantId}`),
        {
          headers: { "Content-Type": "application/json" },
        }
      );

      const data = await response.json();

      if (data.success === false) {
        throw new Error(data.message || "Erro ao carregar fila");
      }

      // Processar e ordenar a fila
      if (data?.queue) {
        const sorted = Array.isArray(data.queue)
          ? [...data.queue].sort((a: any, b: any) => {
              const hasPosA = typeof a.position === "number" && a.position > 0;
              const hasPosB = typeof b.position === "number" && b.position > 0;
              if (hasPosA && hasPosB) return a.position - b.position;
              if (hasPosA) return -1;
              if (hasPosB) return 1;
              const ta = a.createdAt ? new Date(a.createdAt).getTime() : 0;
              const tb = b.createdAt ? new Date(b.createdAt).getTime() : 0;
              return ta - tb;
            })
          : [];
        setQueue(sorted);
      }

      // Atualizar música atual se disponível
      if (data?.currentlyPlaying) {
        setCurrentlyPlaying(data.currentlyPlaying);
      }
    } catch (error) {
      console.error("Erro ao carregar fila de reprodução:", error);
      // Não mostrar toast de erro para não poluir a UI
    }
  }, [restaurantId, fetchWithRetry]);

  /**
   * Carrega a playlist disponível do restaurante
   */
  const loadAvailableMusic = useCallback(
    async (page: number = 1, loadMore: boolean = false) => {
      if (!restaurantId) {
        toast.error("ID do restaurante não encontrado");
        return;
      }

      setPlaylistLoading(true);

      try {
        const limit = 24;
        const params = new URLSearchParams({
          page: page.toString(),
          limit: limit.toString(),
        });

        // Aplicar filtros
        if (activeFilters.length) {
          const genreFilters = activeFilters.filter((f) =>
            ["rock", "pop", "sertanejo", "mpb", "eletronica", "funk"].includes(
              f
            )
          );

          if (genreFilters.length) {
            params.append("genres", genreFilters.join(","));
          }

          const moodFilters = activeFilters.filter((f) =>
            ["happy", "sad", "energetic", "calm"].includes(f)
          );

          if (moodFilters.length) {
            params.append("moods", moodFilters.join(","));
          }
        }

        const response = await fetchWithRetry(
          buildApiUrl(
            `/restaurants/${restaurantId}/playlist?${params.toString()}`
          ),
          { headers: { "Content-Type": "application/json" } }
        );

        const data = await response.json();

        if (data.success && data.results) {
          const transformedResultsBase = data.results.map((track: any) => ({
            id: track.youtubeVideoId || track.id,
            title: track.title,
            artist: track.artist,
            duration: track.duration || 0,
            formattedDuration: track.formattedDuration || "0:00",
            thumbnailUrl:
              track.thumbnailUrl ||
              `https://img.youtube.com/vi/${
                track.youtubeVideoId || track.id
              }/mqdefault.jpg`,
            youtubeVideoId: track.youtubeVideoId || track.id,
            channelName: track.artist,
            viewCount: track.viewCount || 0,
            publishedAt: track.addedAt || new Date().toISOString(),
            // Backend retorna 'genres' (array). Usar o primeiro para exibição.
            genre: Array.isArray(track.genres) && track.genres.length
              ? track.genres[0]
              : track.genre,
          }));

          // Enriquecer com status de cooldown
          let transformedResults = transformedResultsBase as Song[];
          try {
            const withCooldown = await cooldownService.checkMultipleSongsCooldown(
              restaurantId,
              transformedResultsBase.map((t: any) => ({ youtubeVideoId: t.youtubeVideoId }))
            );
            const mapCooldown = new Map<string, { isInCooldown?: boolean; cooldownTimeLeft?: number }>();
            withCooldown.forEach((s) => mapCooldown.set(s.youtubeVideoId, { isInCooldown: s.isInCooldown, cooldownTimeLeft: s.cooldownTimeLeft }));
            transformedResults = transformedResultsBase.map((t: any) => ({
              ...t,
              isInCooldown: mapCooldown.get(t.youtubeVideoId)?.isInCooldown,
              cooldownTimeLeft: mapCooldown.get(t.youtubeVideoId)?.cooldownTimeLeft,
            }));
          } catch (err) {
            // Se falhar, seguimos sem cooldown enriquecido
            console.warn("Falha ao enriquecer playlist com cooldown:", err);
          }

          setSearchResults((prev) =>
            loadMore ? [...prev, ...transformedResults] : transformedResults
          );
          setTotalPlaylistSongs(data.total || transformedResults.length);
          setHasMorePlaylist(transformedResults.length === limit);
          setPlaylistPage(page);
        } else {
          if (!loadMore) {
            setSearchResults([]);
            toast("Nenhuma música encontrada na playlist", { icon: "ℹ️" });
          }
        }
      } catch (error) {
        console.error("Erro ao carregar playlist:", error);
        toast.error("Erro ao carregar playlist do restaurante");

        if (!loadMore) {
          setSearchResults([]);
        }
      } finally {
        setPlaylistLoading(false);
      }
    },
    [restaurantId, activeFilters, fetchWithRetry]
  );

  // Buscar playlist completa para o catálogo completo
  const refetchFullPlaylist = useCallback(async () => {
    if (!restaurantId) return;
    setPlaylistLoading(true);
    setPlaylistError(null);

    let attempts = 0;
    const maxAttempts = 3;
    while (attempts < maxAttempts) {
      try {
        const res = await fetch(
          buildApiUrl(`/restaurants/${restaurantId}/playlist`),
          { headers: { "Content-Type": "application/json" } }
        );
        if (!res.ok) {
          // 502/500 podem ocorrer se o upstream ainda está iniciando
          if ((res.status === 502 || res.status === 500) && attempts < maxAttempts - 1) {
            attempts++;
            await new Promise((r) => setTimeout(r, 500 * attempts));
            continue;
          }
          throw new Error(`HTTP ${res.status}`);
        }
        const json = await res.json();
        const items: PlaylistTrack[] = Array.isArray(json?.results) ? json.results : [];

        // Ordenar por playlist e position para manter ordem correta
        items.sort((a, b) => {
          // Primeiro por playlist (se diferentes)
          if (a.playlistId !== b.playlistId) {
            return (a.playlistId || '').localeCompare(b.playlistId || '');
          }
          // Depois por position dentro da mesma playlist
          const posA = a.position && a.position > 0 ? a.position : Number.MAX_SAFE_INTEGER;
          const posB = b.position && b.position > 0 ? b.position : Number.MAX_SAFE_INTEGER;
          return posA - posB;
        });

        setFullPlaylist(items);
        setPlaylistLoading(false);
        return;
      } catch (err: any) {
        if (attempts < maxAttempts - 1) {
          attempts++;
          await new Promise((r) => setTimeout(r, 500 * attempts));
          continue;
        }
        setPlaylistError(err?.message || "Falha ao carregar playlist");
        setPlaylistLoading(false);
        return;
      }
    }
  }, [restaurantId]);

  /**
   * Busca músicas com base na query de pesquisa
   */
  const searchSongs = useCallback(async () => {
    if (!searchQuery.trim()) {
      toast.error("Digite uma busca válida");
      return;
    }

    if (!restaurantId) {
      toast.error("ID do restaurante não encontrado");
      return;
    }

    setSearchLoading(true);

    try {
      const params = new URLSearchParams({
        q: encodeURIComponent(searchQuery.trim()),
      });

      // Aplicar filtros
      if (activeFilters.length) {
        const genreFilters = activeFilters.filter((f) =>
          ["rock", "pop", "sertanejo", "mpb", "eletronica", "funk"].includes(f)
        );

        if (genreFilters.length) {
          params.append("genres", genreFilters.join(","));
        }

        const moodFilters = activeFilters.filter((f) =>
          ["happy", "sad", "energetic", "calm"].includes(f)
        );

        if (moodFilters.length) {
          params.append("moods", moodFilters.join(","));
        }
      }

      const response = await fetchWithRetry(
        buildApiUrl(`/search/music?${params.toString()}`),
        { headers: { "Content-Type": "application/json" } }
      );

      const data = await response.json();

      if (data.success && data.results) {
        const transformedResultsBase = data.results.map((track: any) => ({
          id: track.youtubeVideoId || track.id,
          title: track.title,
          artist: track.artist,
          duration: track.duration || 0,
          formattedDuration: track.formattedDuration || "0:00",
          thumbnailUrl:
            track.thumbnailUrl ||
            `https://img.youtube.com/vi/${
              track.youtubeVideoId || track.id
            }/mqdefault.jpg`,
          youtubeVideoId: track.youtubeVideoId || track.id,
          channelName: track.artist,
          viewCount: track.viewCount || 0,
          publishedAt: track.addedAt || new Date().toISOString(),
          genre: Array.isArray(track.genres) && track.genres.length
            ? track.genres[0]
            : track.genre,
        }));
        // Enriquecer com status de cooldown
        let transformedResults = transformedResultsBase as Song[];
        try {
          const withCooldown = await cooldownService.checkMultipleSongsCooldown(
            restaurantId,
            transformedResultsBase.map((t: any) => ({ youtubeVideoId: t.youtubeVideoId }))
          );
          const mapCooldown = new Map<string, { isInCooldown?: boolean; cooldownTimeLeft?: number }>();
          withCooldown.forEach((s) => mapCooldown.set(s.youtubeVideoId, { isInCooldown: s.isInCooldown, cooldownTimeLeft: s.cooldownTimeLeft }));
          transformedResults = transformedResultsBase.map((t: any) => ({
            ...t,
            isInCooldown: mapCooldown.get(t.youtubeVideoId)?.isInCooldown,
            cooldownTimeLeft: mapCooldown.get(t.youtubeVideoId)?.cooldownTimeLeft,
          }));
        } catch (err) {
          console.warn("Falha ao enriquecer busca com cooldown:", err);
        }

        setSearchResults(transformedResults);
        setTotalPlaylistSongs(data.total || transformedResults.length);
        setHasMorePlaylist(false);
        setPlaylistPage(1);

        if (transformedResults.length > 0) {
          toast.success(
            `${transformedResults.length} música(s) encontrada(s)`,
            {
              icon: "🔍",
            }
          );
        } else {
          toast(`Nenhuma música encontrada para "${searchQuery}"`, {
            icon: "🔍",
          });
        }
      } else {
        setSearchResults([]);
        toast(`Nenhuma música encontrada para "${searchQuery}"`, {
          icon: "🔍",
        });
      }
    } catch (error) {
      console.error("Erro ao buscar músicas:", error);
      toast.error("Erro ao buscar músicas");
      setSearchResults([]);
    } finally {
      setSearchLoading(false);
    }
  }, [searchQuery, restaurantId, activeFilters, fetchWithRetry]);

  /**
   * Inicializa a sessão do cliente
   */
  const initializeSession = useCallback(async () => {
    if (!restaurantId) {
      toast.error("ID do restaurante não encontrado");
      return;
    }

    setLoading(true);

    try {
      const newSession = await sessionService.forceNewSession(
        restaurantId,
        tableNumber || undefined,
        clientName || undefined
      );

      setSession(newSession);
      setUserStats({
        points: newSession.points || 0,
        level: newSession.level || 1,
        badges: newSession.badges || [],
        suggestionsCount: newSession.suggestionsCount || 0,
        votesCount: newSession.votesCount || 0,
        streak: newSession.streak || 0,
      });

      // Se não tiver nome definido, mostrar input
      if (!newSession.clientName && !clientName) {
        setShowNameInput(true);
      }
    } catch (error) {
      console.error("Erro ao inicializar sessão:", error);
      toast.error("Erro ao inicializar sessão", {
        duration: 4000,
        icon: "⚠️",
      });
    } finally {
      setLoading(false);
    }
  }, [restaurantId, tableNumber, clientName]);

  /**
   * Concede pontos ao usuário com base em ações
   */
  const awardPoints = useCallback(
    (action: "suggest" | "vote", amount: number = 10) => {
      setUserStats((prev) => {
        // Atualizar estatísticas
        const newStats = {
          ...prev,
          points: prev.points + amount,
          suggestionsCount:
            action === "suggest"
              ? prev.suggestionsCount + 1
              : prev.suggestionsCount,
          votesCount: action === "vote" ? prev.votesCount + 1 : prev.votesCount,
          streak: prev.streak + 1,
        };

        // Calcular novo nível
        const newLevel = Math.floor(newStats.points / 100) + 1;

        // Obter badges atualizadas
        const newBadges = getBadges(newStats);

        // Verificar se subiu de nível
        if (newLevel > prev.level) {
          setShowLevelUp(true);
          setTimeout(() => setShowLevelUp(false), 3000);

          toast.success(`🎉 Level Up! Agora você é nível ${newLevel}!`, {
            duration: 5000,
            icon: "🏆",
          });
        }

        // Verificar se ganhou nova badge
        const earnedBadge = newBadges.find(
          (badge) => !prev.badges.includes(badge)
        );
        if (earnedBadge) {
          sessionService.awardBadge(earnedBadge);
          setShowBadgeEarned(earnedBadge);
          setTimeout(() => setShowBadgeEarned(null), 3000);

          toast.success(`🏆 Nova conquista: ${earnedBadge}!`, {
            duration: 5000,
            icon: "🎖️",
          });
        }

        return {
          ...newStats,
          level: newLevel,
          badges: newBadges,
        };
      });
    },
    [getBadges]
  );


  /**
   * Prepara uma música para pagamento e prioridade
   */
  const suggestSongWithPayment = useCallback((song: Song) => {
    setSelectedSongForPayment(song);
    setShowPaymentModal(true);
  }, []);

  // Registrar voto normal (gratuito) na playlist colaborativa
  const voteOnSong = useCallback(
    async (song: Song) => {
      if (!session || !restaurantId) {
        toast.error("Sessão ou restaurante não encontrado");
        return;
      }

      try {
        // Bloquear voto se a música estiver em cooldown
        const cooldown = await cooldownService.checkSongCooldown(restaurantId, song.youtubeVideoId);
        if (cooldown.isInCooldown) {
          toast.error("Música em cooldown no momento. Tente outra ou aguarde.");
          return;
        }
        const res = await fetchWithRetry(
          buildApiUrl(`/collaborative-playlist/${restaurantId}/vote`),
          {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({
              youtubeVideoId: song.youtubeVideoId,
              tableNumber: session.tableNumber || undefined,
              clientSessionId: session.id,
            }),
          }
        );
        const data = await res.json();
        if (data?.success === false) {
          throw new Error(data?.message || "Falha ao registrar voto");
        }
        toast.success(`Voto registrado para "${song.title}" ✅`);
        awardPoints("vote", 10);
      } catch (e) {
        console.error("Erro ao registrar voto:", e);
        toast.error(
          e instanceof Error ? e.message : "Erro ao registrar voto"
        );
      }
    },
    [session, restaurantId, fetchWithRetry, awardPoints]
  );

  // Registrar curtida (métrica) — não afeta ranking/votação
  const likeSong = useCallback(
    async (song: Song) => {
      if (!restaurantId) {
        toast.error("Restaurante não encontrado");
        return;
      }
      try {
        const res = await fetch(buildApiUrl(`/playback-queue/${restaurantId}/like`), {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ youtubeVideoId: song.youtubeVideoId, action: "like" }),
        });
        const data = await res.json();
        if (!res.ok || data?.success === false) {
          throw new Error(data?.message || "Falha ao registrar curtida");
        }
        toast.success(`Curtida registrada para "${song.title}" ❤`);
      } catch (e) {
        console.error("Erro ao registrar curtida:", e);
        toast.error(e instanceof Error ? e.message : "Erro ao registrar curtida");
      }
    },
    [restaurantId]
  );

  /**
   * Processa o pagamento bem-sucedido de uma música
   */
  const handlePaymentSuccess = useCallback(
    async ({ paymentId, amountCents, clientMessage, clientName: paidClientName }: { paymentId: string; amountCents: number; clientMessage?: string; clientName?: string }) => {
      if (!selectedSongForPayment || !session || !restaurantId) {
        toast.error("Dados insuficientes para processar pagamento");
        return;
      }

      try {
        const response = await fetchWithRetry(
          buildApiUrl(`/collaborative-playlist/${restaurantId}/supervote`),
          {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({
              youtubeVideoId: selectedSongForPayment.youtubeVideoId,
              paymentAmount: (amountCents || 0) / 100,
              paymentId,
              tableNumber: session.tableNumber || undefined,
              clientSessionId: session.id,
              clientMessage: clientMessage || undefined,
              clientName: paidClientName || clientName || undefined,
            }),
          }
        );

        const data = await response.json();

        if (data.success === false) {
          throw new Error(data.message || "Erro ao processar pagamento");
        }

  toast.success(`SuperVoto aplicado em "${selectedSongForPayment.title}"! ⭐`);
  awardPoints("vote", 25);
        setSearchQuery("");

        if (searchQuery.trim()) {
          loadAvailableMusic(1);
        }

        // Preparar dados para karaokê
        setKaraokeData({
          id: selectedSongForPayment.id,
          title: selectedSongForPayment.title,
          artist: selectedSongForPayment.artist,
          thumbnailUrl: selectedSongForPayment.thumbnailUrl,
          duration: selectedSongForPayment.duration,
          youtubeVideoId: selectedSongForPayment.youtubeVideoId,
          status: "approved",
          upvotes: 0,
          downvotes: 0,
          score: 0,
          createdAt: new Date().toISOString(),
          isPaid: true,
          clientSessionId: session.id,
        });

        setShowPaymentModal(false);

        // Perguntar sobre karaokê após um breve delay
        setTimeout(() => {
          toast(
            (t) => (
              <div className="flex flex-col gap-2">
                <span>🎤 Quer cantar junto? Ative o "Cante Comigo"!</span>
                <div className="flex gap-2">
                  <button
                    onClick={() => {
                      setShowKaraoke(true);
                      toast.dismiss(t.id);
                    }}
                    className="px-3 py-1 bg-blue-600 text-white rounded text-sm"
                  >
                    Sim, vamos cantar!
                  </button>
                  <button
                    onClick={() => toast.dismiss(t.id)}
                    className="px-3 py-1 bg-gray-600 text-white rounded text-sm"
                  >
                    Não, obrigado
                  </button>
                </div>
              </div>
            ),
            { duration: 8000 }
          );
        }, 2000);
      } catch (error) {
        console.error("Erro após pagamento:", error);
        toast.error("Erro ao processar pagamento", {
          duration: 4000,
          icon: "❌",
        });
      } finally {
        setSelectedSongForPayment(null);
      }
    },
    [
      selectedSongForPayment,
      session,
      restaurantId,
      loadAvailableMusic,
      searchQuery,
      fetchWithRetry,
      awardPoints,
    ]
  );


  /**
   * Salva o nome do cliente na sessão
   */
  const saveClientName = useCallback(async () => {
    if (!clientName.trim()) {
      toast.error("Por favor, digite seu nome");
      return;
    }

    if (!restaurantId) {
      toast.error("ID do restaurante não encontrado");
      return;
    }

    try {
      const updatedSession = await sessionService.createSession(
        restaurantId,
        tableNumber || undefined,
        clientName
      );

      setSession(updatedSession);
      setShowNameInput(false);

      // Sincroniza nome no perfil local
      const profileKey = `clientProfile_${updatedSession.id}`;
      const savedProfile = localStorage.getItem(profileKey);
      if (savedProfile) {
        const parsed = JSON.parse(savedProfile);
        parsed.name = clientName;
        localStorage.setItem(profileKey, JSON.stringify(parsed));
      } else {
        localStorage.setItem(profileKey, JSON.stringify({
          name: clientName,
          avatar: "",
          joinedAt: new Date().toISOString(),
          level: 1,
          experience: 0,
          nextLevelExp: 100,
          title: "Novo Ouvinte",
          preferences: { favoriteGenres: [], notifications: true, autoShare: false },
        }));
      }

      toast.success(`Bem-vindo, ${clientName}! 🎵`, {
        duration: 4000,
        icon: "👋",
      });
    } catch (error) {
      console.error("Erro ao salvar nome:", error);
      toast.error("Erro ao salvar seu nome");
    }
  }, [clientName, restaurantId, tableNumber]);

  /**
   * Configura listeners para eventos WebSocket
   */
  const setupWebSocketListeners = useCallback(() => {
    // Playlist reordenada por votos: atualizar tocando agora e fila
    const handlePlaylistReordered = () => {
      loadCurrentlyPlaying();
      loadPlaybackQueue();
      toast.success("Fila atualizada pela votação!", { icon: "🔄" });
    };

    // Música tocando agora (evento autoritativo)
    const handleNowPlaying = (data: { suggestion: SuggestionType }) => {
      setCurrentlyPlaying(data.suggestion as any);
      toast(`🎵 Tocando agora: ${data.suggestion.title}`, {
        duration: 5000,
        icon: "🎧",
      });
      // Marcar música como cooldown localmente (evita repetição por alguns minutos)
      try {
        if (restaurantId && (data.suggestion as any)?.youtubeVideoId) {
          cooldownService.markSongInCooldown(restaurantId, (data.suggestion as any).youtubeVideoId);
          // Refletir na lista atual
          setSearchResults((prev) => prev.map((s) => s.youtubeVideoId === (data.suggestion as any).youtubeVideoId ? { ...s, isInCooldown: true } : s));
        }
      } catch {}
    };

    // Próxima selecionada pelo servidor (evento autoritativo)
  const handleReorderSelected = (payload: any) => {
      try {
        const v = payload?.video;
        if (!v?.youtubeVideoId) return;
        setCurrentlyPlaying((prev) => {
          // evita sobrescrever se for o mesmo id e já está exibindo
          if (prev?.youtubeVideoId === v.youtubeVideoId) return prev as any;
          return {
            id: v.suggestionId || v.youtubeVideoId,
            title: v.title,
            artist: v.artist,
            youtubeVideoId: v.youtubeVideoId,
            thumbnailUrl: v.thumbnailUrl,
            duration: v.duration,
            status: "approved",
            upvotes: 0,
            downvotes: 0,
            score: 0,
            createdAt: new Date().toISOString(),
          } as any;
        });
        toast(`🎯 Próxima selecionada: ${v.title}`, { icon: "⏭️" });
    // Não iniciamos cooldown aqui; somente quando tocar (now-playing)
      } catch {}
    };

    // Atualizações da fila: atualizar estado atual de forma leve
    const handleQueueUpdate = (data: any) => {
      try {
        if (data?.queue) {
          const sorted = Array.isArray(data.queue)
            ? [...data.queue].sort((a: any, b: any) => {
                const hasPosA = typeof a.position === "number" && a.position > 0;
                const hasPosB = typeof b.position === "number" && b.position > 0;
                if (hasPosA && hasPosB) return a.position - b.position;
                if (hasPosA) return -1;
                if (hasPosB) return 1;
                // Fallback: pagas primeiro
                if (a.isPaid !== b.isPaid) return a.isPaid ? -1 : 1;
                // Fallback: por data de adição crescente
                const ta = a.addedAt ? new Date(a.addedAt).getTime() : 0;
                const tb = b.addedAt ? new Date(b.addedAt).getTime() : 0;
                return ta - tb;
              })
            : [];
          setQueue(sorted);
        }
        if (data?.currentlyPlaying) setCurrentlyPlaying(data.currentlyPlaying);
      } catch (err) {
        console.warn("Erro ao processar atualização da fila:", err);
      }
      // Recarregar dados completos para garantir sincronização
      loadCurrentlyPlaying();
      loadPlaybackQueue();
    };

    // Snapshot periódico de ranking: calcular próxima janela (5min)
    const handleRankingSnapshot = (snapshot: any) => {
      try {
        const ts = snapshot?.timestamp ? new Date(snapshot.timestamp).getTime() : Date.now();
        if (!Number.isFinite(ts)) return;
        if (ts <= lastSnapshotAtRef.current) return; // evita regressão/jitter
        lastSnapshotAtRef.current = ts;
      } catch {}
    };

    // Handler para SuperVotos
    const handleSuperVote = (raw: any) => {
      const data = (raw && typeof raw === "object" && "data" in raw) ? (raw as any).data : raw;

      const msg = data?.payment?.message ?? data?.payment?.clientMessage ?? data?.suggestion?.clientMessage ?? data?.message ?? null;
      const name = data?.payment?.clientName ?? data?.clientName ?? data?.suggestion?.clientName ?? null;
      const table = data?.payment?.tableNumber ?? data?.tableNumber ?? data?.suggestion?.tableNumber ?? null;

      const n: SuperNote = {
        id: `${Date.now()}_${Math.random()}`,
        at: data?.timestamp || raw?.timestamp || new Date().toISOString(),
        amount: Number(data?.payment?.amount) || 0,
        voteWeight: Number(data?.payment?.voteWeight) || 0,
        title: data?.suggestion?.title,
        artist: data?.suggestion?.artist,
        youtubeVideoId: data?.suggestion?.youtubeVideoId,
        clientName: name || undefined,
        tableNumber: typeof table === "number" ? table : undefined,
        message: typeof msg === "string" ? msg : undefined,
      };

      setNotes((prev) => {
        const now = Date.now();
        const pruned = prev.filter((x) => {
          const atMs = x.at ? new Date(x.at).getTime() : now;
          return now - atMs < NOTE_TTL_MS;
        });
        return [n, ...pruned].slice(0, 50);
      });

      // agenda a remoção desta nota após o TTL restante
      try {
        const atMs = n.at ? new Date(n.at).getTime() : Date.now();
        const delay = Math.max(0, NOTE_TTL_MS - (Date.now() - atMs));
        setTimeout(() => {
          setNotes((prev) => prev.filter((x) => x.id !== n.id));
        }, delay);
      } catch {}
    };

    // Handler para playlist reordenada
    const handlePlaylistReorderedNew = (data: any) => {
      refetchFullPlaylist();
    };

    // GC periódico para garantir limpeza mesmo se timers forem perdidos
    const gcId = setInterval(() => {
      const now = Date.now();
      setNotes((prev) => prev.filter((x) => {
        const atMs = x.at ? new Date(x.at).getTime() : now;
        return now - atMs < NOTE_TTL_MS;
      }));
    }, 15000);

    on("now-playing", handleNowPlaying);
    on("playlistReordered", handlePlaylistReordered);
    on("reorderSelected" as any, handleReorderSelected as any);
    on("queue-update" as any, handleQueueUpdate as any);
    on("ranking-snapshot" as any, handleRankingSnapshot as any);
    on("superVoteReceived" as any, handleSuperVote as any);
    on("playlistReordered" as any, handlePlaylistReorderedNew as any);

    return () => {
      off("now-playing", handleNowPlaying);
      off("playlistReordered", handlePlaylistReordered);
      off("reorderSelected" as any, handleReorderSelected as any);
      off("queue-update" as any, handleQueueUpdate as any);
      off("ranking-snapshot" as any, handleRankingSnapshot as any);
      off("superVoteReceived" as any, handleSuperVote as any);
      off("playlistReordered" as any, handlePlaylistReorderedNew as any);
      clearInterval(gcId);
    };
  }, [on, off, loadCurrentlyPlaying, loadPlaybackQueue, restaurantId]);



  // Efeito para inicialização da aplicação
  useEffect(() => {
    if (!restaurantId) {
      toast.error("ID do restaurante não encontrado");
      return;
    }

    // Inicializar dados
    const initialize = async () => {
      setLoading(true);

      try {
        await Promise.all([
          initializeSession(),
          loadRestaurantInfo(),
          loadCurrentlyPlaying(),
          loadPlaybackQueue(),
          loadAvailableMusic(),
          refetchFullPlaylist(),
        ]);

        // Conectar ao WebSocket
        joinRestaurant(restaurantId);
      } catch (error) {
        console.error("Erro na inicialização:", error);
        toast.error("Erro ao carregar dados iniciais");
      } finally {
        setLoading(false);
      }
    };

    initialize();

    // Configurar WebSocket listeners
    const cleanupListeners = setupWebSocketListeners();

    // Limpar tudo ao desmontar
    return () => {
      cleanupListeners();
    };
  }, [restaurantId]); // Removidas as dependências que causam o erro

  // Efeito para carregar dados quando os filtros mudam
  useEffect(() => {
    if (restaurantId) {
      setPlaylistPage(1);
      loadAvailableMusic(1);
  }
  }, [activeFilters, restaurantId]); // Removidas dependências problemáticas

  // Tela de carregamento inicial
  if (loading && !restaurant) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 flex items-center justify-center">
        <div className="text-center text-white">
          <RefreshCw
            className="w-12 h-12 animate-spin mx-auto mb-4"
            aria-hidden="true"
          />
          <h2 className="text-xl font-semibold">Carregando...</h2>
          <p className="text-sm text-purple-300 mt-2">
            Conectando ao restaurante
          </p>
          <button
            onClick={() => {
              loadRestaurantInfo();
              loadCurrentlyPlaying();
              loadPlaybackQueue();
              loadAvailableMusic();
            }}
            className="mt-4 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            aria-label="Tentar novamente"
          >
            Tentar Novamente
          </button>
        </div>
      </div>
    );
  }

  // Componente principal da interface do cliente
  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 text-white">
      {/* Cabeçalho */}
      <header className="bg-black/30 backdrop-blur-md border-b border-white/10 p-4 sm:p-6 sticky top-0 z-10">
        <div className="max-w-4xl mx-auto text-center">
          <div className="flex items-center justify-center gap-3 mb-3">
            <div className="w-12 h-12 bg-gradient-to-r from-pink-500 to-violet-500 rounded-full flex items-center justify-center">
              <Music className="w-6 h-6" aria-hidden="true" />
            </div>
            <div>
              <h1 className="text-2xl sm:text-3xl font-bold">
                {restaurant?.name}
              </h1>
            </div>
          </div>
          <p className="text-sm sm:text-base text-purple-200 mb-4 px-2 text-center">
            Escolha e vote nas músicas que vão animar seu momento!
          </p>

          {/* Status do usuário */}
          <div className="flex flex-wrap justify-center gap-3 sm:gap-4 text-sm sm:text-base">
            {tableNumber && (
              <div className="flex items-center gap-1 px-2 py-1 bg-white/10 rounded-full" title={`Mesa ${tableNumber}`}>
                <span className="text-lg">🪑</span>
                <span className="font-medium">Mesa {tableNumber}</span>
              </div>
            )}
            <div className="flex items-center gap-1 px-2 py-1 bg-white/10 rounded-full" title="Votos realizados">
              <ThumbsUp className="w-4 h-4 text-blue-400" aria-hidden="true" />
              <span className="font-medium">{userStats.votesCount}</span>
            </div>

            {/* Botões de ação */}
            <button
              onClick={() => setShowProfile(true)}
              className="flex items-center gap-1 px-3 py-1.5 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-full hover:from-blue-600 hover:to-indigo-600 transition-all duration-200 hover:scale-105 font-medium"
              aria-label="Ver perfil"
            >
              <User className="w-4 h-4" aria-hidden="true" />
              <span>Perfil</span>
            </button>
            <button
              onClick={() => setShowLeaderboard(true)}
              className="flex items-center gap-1 px-3 py-1 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full hover:from-purple-600 hover:to-pink-600 transition-transform hover:scale-105"
              aria-label="Ver ranking"
            >
              <Trophy className="w-4 h-4" aria-hidden="true" />
              <span>Ranking</span>
            </button>
          </div>

          {/* Badges do usuário */}
          {userStats.badges.length > 0 && (
            <div className="flex flex-wrap justify-center gap-2 mt-3">
              {userStats.badges.slice(0, 3).map((badge, index) => (
                <span
                  key={index}
                  className="px-2 py-1 bg-white/10 rounded-full text-xs border border-white/20"
                  title={badge}
                >
                  {badge}
                </span>
              ))}
              {userStats.badges.length > 3 && (
                <span
                  className="px-2 py-1 bg-white/10 rounded-full text-xs text-purple-200 border border-white/20"
                  title={`${userStats.badges.length - 3} badges adicionais`}
                >
                  +{userStats.badges.length - 3} mais
                </span>
              )}
            </div>
          )}

          {/* Indicador de status de conexão */}
          <div className="mt-4 pt-3 border-t border-white/10 flex items-center justify-center gap-2">
            <div
              className={`w-2 h-2 rounded-full ${
                isConnected ? "bg-green-400 animate-pulse" : "bg-red-400"
              }`}
            ></div>
            <span className="text-xs sm:text-sm text-purple-200">
              {isConnected ? "Conectado ao restaurante" : "Reconectando..."}
            </span>
          </div>
        </div>
      </header>

      <main className="max-w-4xl mx-auto px-3 sm:px-4 lg:px-6 py-4 sm:py-6 space-y-4 sm:space-y-6">
        {/* Tocando agora */}
        {currentlyPlaying && (
          <motion.section
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-white/10 backdrop-blur-md rounded-xl p-4 sm:p-6 border border-white/20"
            aria-labelledby="now-playing"
          >
            <h2
              id="now-playing"
              className="flex items-center gap-2 text-lg sm:text-xl font-bold mb-3 sm:mb-4"
            >
              <Play className="w-5 h-5 text-green-400" aria-hidden="true" />
              Tocando Agora
            </h2>
            <div className="flex items-center gap-3 sm:gap-4">
              <img
                src={
                  currentlyPlaying.thumbnailUrl ||
                  `https://img.youtube.com/vi/${currentlyPlaying.youtubeVideoId}/mqdefault.jpg`
                }
                alt={`Capa de ${currentlyPlaying.title}`}
                className="w-14 h-14 sm:w-16 sm:h-16 rounded-lg object-cover flex-shrink-0"
                loading="lazy"
              />
              <div className="flex-1 min-w-0">
                <h3 className="text-sm sm:text-base font-semibold truncate">
                  {currentlyPlaying.title}
                </h3>
                <p className="text-xs sm:text-sm text-purple-200 truncate">
                  {currentlyPlaying.artist}
                </p>
              </div>
              <div className="text-right">
                <div className="text-xl font-bold text-green-400">
                  {currentlyPlaying.score > 0 ? "+" : ""}
                  {currentlyPlaying.score}
                </div>
                <div className="text-xs text-purple-200">votos</div>
              </div>
            </div>
          </motion.section>
        )}

        {/* Busca de músicas */}
        <section
          className="bg-white/10 backdrop-blur-md rounded-xl p-6 border border-white/20"
          aria-labelledby="search"
        >
          <div className="flex items-center justify-between mb-4">
            <h2 id="search" className="flex items-center gap-2 text-xl font-bold">
              <Search className="w-5 h-5" aria-hidden="true" />
              Buscar Músicas
            </h2>
            <button
              onClick={() => setSearchCollapsed(!searchCollapsed)}
              className="px-3 py-2 bg-white/10 rounded-lg border border-white/20 text-xs hover:bg-white/20 inline-flex items-center gap-1"
              aria-label="Alternar seção de busca"
            >
              {searchCollapsed ? (
                <>
                  <ChevronDown className="w-4 h-4" />
                  <span>Expandir</span>
                </>
              ) : (
                <>
                  <ChevronUp className="w-4 h-4" />
                  <span>Recolher</span>
                </>
              )}
            </button>
          </div>
          {!searchCollapsed && (
            <div className="flex gap-3 mb-4">

            <div className="relative flex-1">
              <input
                type="text"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                onKeyPress={(e) => e.key === "Enter" && searchSongs()}
                placeholder="Busque por música ou artista..."
                className="w-full px-4 py-3 bg-white/20 border border-white/30 rounded-lg text-white placeholder-purple-200 focus:outline-none focus:ring-2 focus:ring-purple-500"
                aria-label="Buscar músicas por título ou artista"
              />
              {searchQuery && (
                <button
                  onClick={() => setSearchQuery("")}
                  className="absolute right-12 top-1/2 -translate-y-1/2 text-white/60 hover:text-white"
                  aria-label="Limpar busca"
                >
                  <X className="w-4 h-4" />
                </button>
              )}
            </div>
            <button
              onClick={searchSongs}
              disabled={searchLoading || !searchQuery.trim()}
              className="px-4 sm:px-6 py-2.5 sm:py-3 bg-gradient-to-r from-purple-600 to-pink-600 rounded-lg hover:from-purple-700 hover:to-pink-700 disabled:opacity-50 flex items-center gap-2 whitespace-nowrap text-sm sm:text-base font-medium"
              aria-label="Buscar músicas"
            >
              {searchLoading ? (
                <RefreshCw
                  className="w-4 h-4 animate-spin"
                  aria-hidden="true"
                />
              ) : (
                <Search className="w-4 h-4" aria-hidden="true" />
              )}
              <span>Buscar</span>
            </button>
          </div>
          )}
        </section>

        {/* Karaoke (cartão melhorado) */}
        <section
          className="bg-gradient-to-br from-purple-500/10 via-blue-500/10 to-indigo-500/10 backdrop-blur-md rounded-xl p-4 sm:p-6 border border-purple-400/30 shadow-lg"
          aria-labelledby="karaoke-section"
       >
          <div className="flex items-center justify-between mb-4 sm:mb-6">
            <div className="flex items-center gap-2 sm:gap-3">
              <div className="p-1.5 sm:p-2 bg-gradient-to-r from-purple-600 to-blue-600 rounded-lg">
                <Mic className="w-5 h-5 sm:w-6 sm:h-6 text-white" aria-hidden="true" />
              </div>
              <div>
                <h2 id="karaoke-section" className="text-lg sm:text-xl font-bold text-white">
                  Cante Comigo (Karaokê)
                </h2>
                <p className="text-purple-200 text-xs sm:text-sm">
                  Letras sincronizadas em tempo real
                </p>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <div className="flex items-center gap-1 px-2 py-1 bg-green-500/20 rounded-full border border-green-400/30">
                <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                <span className="text-green-300 text-xs font-medium">Melhorado</span>
              </div>
              <button
                onClick={() => setKaraokeCollapsed(!karaokeCollapsed)}
                className="px-3 py-2 bg-white/10 rounded-lg border border-white/20 text-xs hover:bg-white/20 inline-flex items-center gap-1 transition-colors"
                aria-label="Alternar seção de karaokê"
              >
                {karaokeCollapsed ? (
                  <>
                    <ChevronDown className="w-4 h-4" />
                    <span>Expandir</span>
                  </>
                ) : (
                  <>
                    <ChevronUp className="w-4 h-4" />
                    <span>Recolher</span>
                  </>
                )}
              </button>
            </div>
          </div>
          {!karaokeCollapsed && (
            <div className="space-y-4">
              {/* Música atual */}
              <div className="bg-white/5 rounded-lg p-4 border border-white/10">
                <div className="flex items-center gap-4">
                  <div className="flex-1 min-w-0">
                    {currentlyPlaying ? (
                      <div className="flex items-center gap-3">
                        <div className="relative">
                          <img
                            src={
                              (currentlyPlaying as any).thumbnailUrl ||
                              `https://img.youtube.com/vi/${currentlyPlaying.youtubeVideoId}/mqdefault.jpg`
                            }
                            alt={`Capa de ${currentlyPlaying.title}`}
                            className="w-16 h-16 rounded-lg object-cover shadow-md"
                            loading="lazy"
                          />
                          <div className="absolute -top-1 -right-1 w-4 h-4 bg-green-500 rounded-full border-2 border-white animate-pulse"></div>
                        </div>
                        <div className="min-w-0 flex-1">
                          <p className="font-semibold truncate text-white">{currentlyPlaying.title}</p>
                          <p className="text-purple-200 text-sm truncate">{currentlyPlaying.artist}</p>
                          <div className="flex items-center gap-2 mt-1">
                            <div className="flex items-center gap-1 text-xs text-green-300">
                              <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                              <span>Tocando agora</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    ) : (
                      <div className="flex items-center gap-3">
                        <div className="w-16 h-16 bg-white/10 rounded-lg flex items-center justify-center">
                          <Music className="w-6 h-6 text-white/50" />
                        </div>
                        <div>
                          <p className="text-white/70 font-medium">Aguardando música</p>
                          <p className="text-purple-200 text-sm">Nenhuma música tocando no momento</p>
                        </div>
                      </div>
                    )}
                  </div>
                  <div className="flex flex-col gap-2">
                    <button
                      onClick={() => {
                        console.log("🎤 Botão karaokê clicado", { currentlyPlaying });

                        if (currentlyPlaying) {
                          // Usar música atual
                          setKaraokeData({
                            id: currentlyPlaying.id,
                            title: currentlyPlaying.title,
                            artist: currentlyPlaying.artist,
                            thumbnailUrl: (currentlyPlaying as any).thumbnailUrl,
                            duration: (currentlyPlaying as any).duration,
                            youtubeVideoId: currentlyPlaying.youtubeVideoId,
                            status: "approved",
                            upvotes: 0,
                            downvotes: 0,
                            score: 0,
                            createdAt: new Date().toISOString(),
                          });
                        } else {
                          // Música de demonstração
                          setKaraokeData({
                            id: "demo-karaoke",
                            title: "Música de Demonstração",
                            artist: "Artista Demo",
                            thumbnailUrl: "https://img.youtube.com/vi/dQw4w9WgXcQ/mqdefault.jpg",
                            duration: 180,
                            youtubeVideoId: "dQw4w9WgXcQ",
                            status: "approved",
                            upvotes: 0,
                            downvotes: 0,
                            score: 0,
                            createdAt: new Date().toISOString(),
                          });
                        }
                        setShowKaraoke(true);
                        toast.success("🎤 Abrindo karaokê!");
                      }}
                      className="px-6 py-3 bg-gradient-to-r from-purple-600 to-blue-600 rounded-lg hover:from-purple-700 hover:to-blue-700 inline-flex items-center gap-2 font-medium transition-all transform hover:scale-105"
                      aria-label="Abrir karaokê"
                    >
                      <Mic className="w-5 h-5" aria-hidden="true" />
                      <span>{currentlyPlaying ? "Iniciar Karaokê" : "Demo Karaokê"}</span>
                    </button>

                    {!currentlyPlaying && (
                      <p className="text-xs text-purple-300 text-center">
                        Clique para testar o karaokê com música demo
                      </p>
                    )}
                  </div>
                </div>
              </div>

              {/* Funcionalidades do karaokê */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                <div className="bg-white/5 rounded-lg p-3 border border-white/10">
                  <div className="flex items-center gap-2 mb-2">
                    <div className="w-8 h-8 bg-blue-500/20 rounded-lg flex items-center justify-center">
                      <Music className="w-4 h-4 text-blue-400" />
                    </div>
                    <span className="text-sm font-medium text-white">Sincronização</span>
                  </div>
                  <p className="text-xs text-purple-200">
                    Letras sincronizadas em tempo real com a música
                  </p>
                </div>

                <div className="bg-white/5 rounded-lg p-3 border border-white/10">
                  <div className="flex items-center gap-2 mb-2">
                    <div className="w-8 h-8 bg-green-500/20 rounded-lg flex items-center justify-center">
                      <Settings className="w-4 h-4 text-green-400" />
                    </div>
                    <span className="text-sm font-medium text-white">Personalização</span>
                  </div>
                  <p className="text-xs text-purple-200">
                    Ajuste tamanho da fonte e cores do destaque
                  </p>
                </div>

                <div className="bg-white/5 rounded-lg p-3 border border-white/10">
                  <div className="flex items-center gap-2 mb-2">
                    <div className="w-8 h-8 bg-purple-500/20 rounded-lg flex items-center justify-center">
                      <Maximize className="w-4 h-4 text-purple-400" />
                    </div>
                    <span className="text-sm font-medium text-white">Tela Cheia</span>
                  </div>
                  <p className="text-xs text-purple-200">
                    Modo fullscreen para melhor experiência
                  </p>
                </div>
              </div>
            </div>
          )}
        </section>

        {/* Playlist do restaurante */}
        <section
          className="bg-white/10 backdrop-blur-md rounded-xl p-6 border border-white/20"
          aria-labelledby="playlist"
        >
          <div className="flex items-center justify-between mb-4">
            <h2
              id="playlist"
              className="flex items-center gap-2 text-xl font-bold"
            >
              <Music className="w-5 h-5" aria-hidden="true" />
              {searchQuery ? "Resultados da Busca" : "Playlist do Restaurante"}
            </h2>
            {totalPlaylistSongs > 0 && (
              <div className="text-sm text-purple-200">
                {searchResults.length} de {totalPlaylistSongs} músicas
              </div>
            )}
          </div>

          {/* Lista de músicas */}
          {playlistLoading && searchResults.length === 0 ? (
            <div className="flex justify-center items-center py-12">
              <RefreshCw
                className="w-8 h-8 text-purple-400 animate-spin"
                aria-hidden="true"
              />
              <span className="ml-3 text-purple-200">
                Carregando músicas...
              </span>
            </div>
          ) : searchResults.length === 0 ? (
            <div className="text-center py-8">
              <Headphones
                className="w-12 h-12 text-purple-400 mx-auto mb-3"
                aria-hidden="true"
              />
              <p className="text-purple-200">Nenhuma música encontrada.</p>
              <p className="text-sm text-purple-300">
                Use a busca ou filtros para encontrar músicas.
              </p>
            </div>
          ) : (
            <>
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
                {searchResults.map((song) => (
                  <motion.div
                    key={song.id}
                    initial={{ opacity: 0, scale: 0.95 }}
                    animate={{ opacity: 1, scale: 1 }}
                    whileHover={{ scale: song.isInCooldown ? 1 : 1.03 }}
                    className={`bg-white/5 rounded-lg p-4 border border-white/10 hover:bg-white/10 transition-all ${
                      song.isInCooldown ? 'opacity-60 border-orange-400/30 bg-orange-500/5' : ''
                    }`}
                    role="article"
                    aria-labelledby={`song-${song.id}`}
                  >
                    <div className="relative">
                      <img
                        src={song.thumbnailUrl}
                        alt={`Capa de ${song.title}`}
                        className={`w-full h-28 sm:h-32 object-cover rounded-lg mb-2 sm:mb-3 ${
                          song.isInCooldown ? 'grayscale' : ''
                        }`}
                        loading="lazy"
                      />
                      {song.isInCooldown && (
                        <div className="absolute inset-0 bg-orange-500/20 rounded-lg flex items-center justify-center">
                          <div className="bg-orange-500 text-white px-2 py-1 rounded-full text-xs font-bold">
                            ⏰ Cooldown
                          </div>
                        </div>
                      )}
                    </div>
                    <h3
                      id={`song-${song.id}`}
                      className={`font-semibold text-xs sm:text-sm truncate ${
                        song.isInCooldown ? 'text-orange-300' : ''
                      }`}
                    >
                      {song.title}
                    </h3>
                    <p className={`text-xs truncate mb-1 sm:mb-2 ${
                      song.isInCooldown ? 'text-orange-400' : 'text-purple-200'
                    }`}>
                      {song.artist}
                    </p>
                    {song.isInCooldown && song.cooldownTimeLeft && (
                      <p className="text-orange-400 text-xs mb-2 font-medium">
                        🚫 Disponível em {Math.ceil(song.cooldownTimeLeft / 60)} min
                      </p>
                    )}
          <div className="flex justify-between items-center text-xs text-purple-300 mb-2 sm:mb-3">
                      <span className="text-xs sm:text-sm">{song.formattedDuration}</span>
                      <button
            onClick={() => likeSong(song)}
                        disabled={loading}
                        className="inline-flex items-center gap-1 text-pink-300 hover:text-pink-400 disabled:opacity-50 transition-colors p-1"
                        aria-label={`Curtir ${song.title}`}
                        title="Curtir"
                      >
                        <Heart className="w-3 h-3 sm:w-4 sm:h-4" aria-hidden="true" />
                        <span className="sr-only">Curtir</span>
                      </button>
                    </div>
                    <div className="flex gap-1 sm:gap-2">
                      {song.isInCooldown ? (
                        <div className="px-3 py-2 bg-orange-500/20 border border-orange-400/30 rounded-lg flex items-center justify-center gap-1 text-xs text-orange-300">
                          <span>⏰ Em Cooldown</span>
                        </div>
                      ) : (
                        <>
                          <button
                            onClick={() => voteOnSong(song)}
                            disabled={loading}
                            className="flex-1 px-2 sm:px-3 py-1.5 sm:py-2 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg hover:from-blue-700 hover:to-purple-700 disabled:opacity-50 flex items-center justify-center gap-1 text-xs sm:text-sm font-medium"
                            aria-label={`Registrar voto para ${song.title}`}
                          >
                            <Music className="w-3 h-3 sm:w-4 sm:h-4" aria-hidden="true" />
                            <span className="hidden xs:inline">Voto</span>
                            <span className="xs:hidden">👍</span>
                          </button>
                          <button
                            onClick={() => suggestSongWithPayment(song)}
                            disabled={loading}
                            className="flex-1 px-2 sm:px-3 py-1.5 sm:py-2 bg-gradient-to-r from-green-600 to-emerald-600 rounded-lg hover:from-green-700 hover:to-emerald-700 disabled:opacity-50 flex items-center justify-center gap-1 text-xs sm:text-sm font-medium"
                            aria-label={`Adicionar ${song.title} com SuperVoto`}
                          >
                            <CreditCard className="w-3 h-3 sm:w-4 sm:h-4" aria-hidden="true" />
                            <span className="hidden xs:inline">SuperVoto</span>
                            <span className="xs:hidden">💳</span>
                          </button>
                        </>
                      )}
                    </div>
                  </motion.div>
                ))}
              </div>

              {/* Botão para carregar mais */}
              {hasMorePlaylist && (
                <div className="text-center">
                  <button
                    onClick={() => loadAvailableMusic(playlistPage + 1, true)}
                    disabled={playlistLoading}
                    className="px-6 py-3 bg-gradient-to-r from-purple-600 to-pink-600 rounded-lg hover:from-purple-700 hover:to-pink-700 disabled:opacity-50 flex items-center gap-2 mx-auto"
                    aria-label="Carregar mais músicas da playlist"
                  >
                    {playlistLoading ? (
                      <RefreshCw
                        className="w-4 h-4 animate-spin"
                        aria-hidden="true"
                      />
                    ) : (
                      <Music className="w-4 h-4" aria-hidden="true" />
                    )}
                    <span>
                      {playlistLoading
                        ? "Carregando..."
                        : "Carregar Mais Músicas"}
                    </span>
                  </button>
                </div>
              )}

              {/* Mensagem quando todas as músicas foram carregadas */}
              {!hasMorePlaylist && totalPlaylistSongs > 24 && (
                <p className="text-center text-sm text-purple-300">
                  🎵 Todas as {totalPlaylistSongs} músicas foram carregadas!
                </p>
              )}
            </>
          )}
        </section>

        {/* Sequência (Fila) - Layout original */}
        {showQueue && (
          <section
            className="bg-white/10 backdrop-blur-md rounded-xl p-6 border border-white/20"
            aria-labelledby="playback-queue"
          >
            <div
              className="p-6 bg-gradient-to-r from-purple-500/20 to-indigo-500/20 cursor-pointer hover:from-purple-500/30 hover:to-indigo-500/30 transition-all rounded-lg mb-4"
              onClick={() => setQueueCollapsed(!queueCollapsed)}
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-gradient-to-r from-purple-500 to-indigo-500 rounded-full flex items-center justify-center shadow-lg">
                    <Music className="w-6 h-6 text-white" />
                  </div>
                  <div>
                    <h3 className="text-xl font-bold text-white">Sequência (Fila)</h3>
                    <div className="flex items-center gap-4 mt-1 text-sm">
                      <span className="text-purple-200">
                        {queue.length} música{queue.length !== 1 ? 's' : ''}
                      </span>
                    </div>
                  </div>
                </div>

                <div className="flex items-center space-x-3">
                  <button className="p-2 bg-white/10 rounded-lg text-purple-300 hover:text-white hover:bg-white/20 transition-all">
                    {queueCollapsed ? (
                      <ChevronDown className="w-5 h-5" />
                    ) : (
                      <ChevronUp className="w-5 h-5" />
                    )}
                  </button>
                </div>
              </div>
            </div>

            <AnimatePresence>
              {!queueCollapsed && (
                <motion.div
                  initial={{ height: 0 }}
                  animate={{ height: "auto" }}
                  exit={{ height: 0 }}
                  className="overflow-hidden"
                >
                  {queue.length === 0 ? (
                    <div className="p-8 text-center">
                      <Music className="w-16 h-16 text-gray-500 mx-auto mb-4" />
                      <p className="text-gray-400 text-lg font-medium">Fila vazia</p>
                      <p className="text-gray-500 text-sm">
                        Seja o primeiro a sugerir uma música!
                      </p>
                    </div>
                  ) : (
                    <div className="p-4 space-y-3 max-h-96 overflow-y-auto">
                      {queue.map((item, index) => (
                        <motion.div
                          key={item.id}
                          initial={{ opacity: 0, x: -20 }}
                          animate={{ opacity: 1, x: 0 }}
                          transition={{ delay: index * 0.05 }}
                          className="p-4 rounded-lg border transition-all hover:shadow-lg bg-white/5 border-white/10 hover:bg-white/10"
                        >
                          <div className="flex items-center space-x-4">
                            {/* Position */}
                            <div className="flex-shrink-0">
                              <div className={`w-10 h-10 rounded-full flex items-center justify-center font-bold ${
                                item.isPaid
                                  ? "bg-yellow-500 text-black"
                                  : "bg-purple-500/30 text-purple-300"
                              }`}>
                                #{item.position}
                              </div>
                            </div>

                            {/* Thumbnail */}
                            <img
                              src={item.thumbnailUrl || `https://img.youtube.com/vi/${item.youtubeVideoId}/mqdefault.jpg`}
                              alt={item.title}
                              className="w-16 h-12 rounded-lg object-cover shadow-md"
                            />

                            {/* Info */}
                            <div className="flex-1 min-w-0">
                              <h4 className="text-white font-semibold truncate">{item.title}</h4>
                              <p className="text-gray-300 text-sm truncate">{item.artist}</p>
                              <div className="flex items-center gap-3 mt-2 text-xs">
                                <span className="flex items-center gap-1 text-purple-300">
                                  <Clock className="w-3 h-3" />
                                  {fmtTime(item.duration)}
                                </span>
                                {item.isPaid && item.paymentAmount && (
                                  <span className="flex items-center gap-1 text-yellow-300 bg-yellow-500/20 px-2 py-1 rounded">
                                    <CreditCard className="w-3 h-3" />
                                    R$ {Number(item.paymentAmount).toFixed(2)}
                                  </span>
                                )}
                              </div>
                            </div>
                          </div>
                        </motion.div>
                      ))}
                    </div>
                  )}
                </motion.div>
              )}
            </AnimatePresence>
          </section>
        )}





        {/* Rodapé */}
        <footer className="text-center py-6">
          <p className="text-sm text-purple-300">
            🎵 Powered by Sistema de Playlist Interativa
          </p>
          <p className="text-xs text-purple-400 mt-1">
            Sugestões são moderadas e podem levar alguns minutos para aparecer
            na fila
          </p>
        </footer>
      </main>

      {/* Modais e Componentes Flutuantes */}
      <AnimatePresence>
        {/* Level Up Notification */}
        {showLevelUp && (
          <motion.div
            initial={{ opacity: 0, scale: 0.5, y: 50 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.5, y: -50 }}
            className="fixed top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 z-50"
            role="alert"
            aria-live="polite"
          >
            <div className="bg-gradient-to-r from-yellow-500 to-orange-500 text-white px-8 py-6 rounded-xl shadow-2xl text-center">
              <div className="text-4xl mb-2">🎉</div>
              <h3 className="text-xl font-bold mb-1">LEVEL UP!</h3>
              <p className="text-sm">
                Você alcançou o nível {userStats.level}!
              </p>
            </div>
          </motion.div>
        )}

        {/* Badge Earned Notification */}
        {showBadgeEarned && (
          <motion.div
            initial={{ opacity: 0, scale: 0.5, y: 50 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.5, y: -50 }}
            className="fixed top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 z-50"
            role="alert"
            aria-live="polite"
          >
            <div className="bg-gradient-to-r from-purple-500 to-pink-500 text-white px-8 py-6 rounded-xl shadow-2xl text-center">
              <div className="text-4xl mb-2">🏆</div>
              <h3 className="text-xl font-bold mb-1">NOVA CONQUISTA!</h3>
              <p className="text-sm">{showBadgeEarned}</p>
            </div>
          </motion.div>
        )}

        {/* Name Input Modal */}
        {showNameInput && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4"
            role="dialog"
            aria-modal="true"
            aria-labelledby="name-modal-title"
          >
            <motion.div
              initial={{ opacity: 0, scale: 0.9, y: 20 }}
              animate={{ opacity: 1, scale: 1, y: 0 }}
              exit={{ opacity: 0, scale: 0.9, y: 20 }}
              className="bg-white dark:bg-gray-900 rounded-xl p-6 max-w-md w-full shadow-2xl"
            >
              <div className="text-center mb-6">
                <User
                  className="w-12 h-12 text-purple-500 mx-auto mb-3"
                  aria-hidden="true"
                />
                <h3
                  id="name-modal-title"
                  className="text-xl font-bold text-gray-800 dark:text-white mb-2"
                >
                  Bem-vindo! 🎵
                </h3>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Como podemos te chamar? (Opcional)
                </p>
              </div>
              <div className="space-y-4">
                <input
                  type="text"
                  value={clientName}
                  onChange={(e) => setClientName(e.target.value)}
                  placeholder="Digite seu nome..."
                  className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent outline-none bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100"
                  onKeyPress={(e) => e.key === "Enter" && saveClientName()}
                  autoFocus
                  aria-label="Digite seu nome"
                />
                <div className="flex gap-3">
                  <button
                    onClick={saveClientName}
                    disabled={!clientName.trim()}
                    className="flex-1 bg-purple-500 text-white py-3 rounded-lg hover:bg-purple-600 transition-colors disabled:opacity-50"
                    aria-label="Confirmar nome"
                  >
                    Continuar
                  </button>
                  <button
                    onClick={() => setShowNameInput(false)}
                    className="flex-1 bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 py-3 rounded-lg hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors"
                    aria-label="Pular entrada de nome"
                  >
                    Pular
                  </button>
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Payment Modal */}
      {showPaymentModal && selectedSongForPayment && (
        <PaymentModal
          isOpen={showPaymentModal}
          onClose={() => {
            setShowPaymentModal(false);
            setSelectedSongForPayment(null);
          }}
          suggestion={{
            id: selectedSongForPayment.id,
            title: selectedSongForPayment.title,
            artist: selectedSongForPayment.artist,
            thumbnailUrl: selectedSongForPayment.thumbnailUrl,
            duration: selectedSongForPayment.duration,
            youtubeVideoId: selectedSongForPayment.youtubeVideoId,
          }}
          sessionId={session?.sessionToken || ""}
          restaurantId={restaurantId || ""}
          onPaymentSuccess={handlePaymentSuccess}
        />
      )}

      {/* Karaoke Player */}
      {karaokeData && (
        <KaraokePlayer
          isOpen={showKaraoke}
          onClose={() => {
            setShowKaraoke(false);
            setKaraokeData(null);
          }}
          suggestion={karaokeData}
          sessionId={session?.id || ""}
          onVoteRequest={() =>
            toast.success("Votação solicitada! 🗳️", { duration: 4000 })
          }
        />
      )}

  {/* NewSuggestionAlert removido: fluxo de fila/votos unificado não usa mais este componente */}

      {/* Client Profile Modal */}
      {showProfile && session && (
        <ClientProfile
          isOpen={showProfile}
          onClose={() => setShowProfile(false)}
          restaurantId={restaurantId || ""}
          sessionId={session.id}
        />
      )}

      {/* Table Leaderboard */}
      <TableLeaderboard
  restaurantId={restaurantId || ""}
  currentTableNumber={tableNumber || undefined}
        isVisible={showLeaderboard}
        onClose={() => setShowLeaderboard(false)}
      />
    </div>
  );
};

export default ClientInterface;
