import { useEffect, useCallback, useRef } from 'react';
import { useWebSocket } from '../services/websocket';

interface SyncOptions {
  restaurantId: string;
  onQueueUpdate?: (data: any) => void;
  onPlaybackUpdate?: (data: any) => void;
  onVoteUpdate?: (data: any) => void;
  onSuggestionUpdate?: (data: any) => void;
  autoJoin?: boolean;
  syncInterval?: number;
}

/**
 * Hook para sincronização global entre todos os componentes
 * Garante que Display, Dashboard, Client Interface e Cover estejam sincronizados
 */
export const useGlobalSync = (options: SyncOptions) => {
  const {
    restaurantId,
    onQueueUpdate,
    onPlaybackUpdate,
    onVoteUpdate,
    onSuggestionUpdate,
    autoJoin = true,
    syncInterval = 120000 // 2 minutos - reduzido para evitar spam
  } = options;

  const { isConnected, on, off, joinRestaurant, emit } = useWebSocket();
  const syncIntervalRef = useRef<NodeJS.Timeout>();
  const lastSyncRef = useRef<number>(0);

  // Função para forçar sincronização
  const forceSync = useCallback(() => {
    if (!isConnected || !restaurantId) return;
    
    const now = Date.now();
    // Evitar sync muito frequente
    if (now - lastSyncRef.current < 5000) return;
    
    lastSyncRef.current = now;
    
    console.log(`🔄 [GlobalSync] Forçando sincronização para ${restaurantId}`);
    
    // Emitir eventos de sincronização
    emit('request-sync', { restaurantId, timestamp: now });
    emit('request-queue-update', { restaurantId });
    emit('request-playback-state', { restaurantId });
  }, [isConnected, restaurantId, emit]);

  // Auto-join na sala do restaurante
  useEffect(() => {
    if (autoJoin && isConnected && restaurantId) {
      console.log(`🏪 [GlobalSync] Entrando na sala: ${restaurantId}`);
      joinRestaurant(restaurantId);
    }
  }, [autoJoin, isConnected, restaurantId, joinRestaurant]);

  // Configurar listeners de eventos
  useEffect(() => {
    if (!isConnected || !restaurantId) return;

    console.log(`📡 [GlobalSync] Configurando listeners para ${restaurantId}`);

    // Handler para atualizações da fila
    const handleQueueUpdate = (data: any) => {
      console.log(`📋 [GlobalSync] Queue update recebido:`, data);
      onQueueUpdate?.(data);
    };

    // Handler para atualizações de playback
    const handlePlaybackUpdate = (data: any) => {
      console.log(`🎵 [GlobalSync] Playback update recebido:`, data);
      onPlaybackUpdate?.(data);
    };

    // Handler para atualizações de votos
    const handleVoteUpdate = (data: any) => {
      console.log(`👍 [GlobalSync] Vote update recebido:`, data);
      onVoteUpdate?.(data);
    };

    // Handler para novas sugestões
    const handleSuggestionUpdate = (data: any) => {
      console.log(`💡 [GlobalSync] Suggestion update recebido:`, data);
      onSuggestionUpdate?.(data);
    };

    // Handler para sincronização forçada
    const handleSyncRequest = (data: any) => {
      console.log(`🔄 [GlobalSync] Sync request recebido:`, data);
      // Aguardar um pouco para evitar conflitos
      setTimeout(forceSync, 1000);
    };

    // Registrar listeners
    on('queue-update', handleQueueUpdate);
    on('playback-state-update', handlePlaybackUpdate);
    on('vote-update', handleVoteUpdate);
    on('suggestion-added', handleSuggestionUpdate);
    on('sync-request', handleSyncRequest);

    // Cleanup
    return () => {
      off('queue-update', handleQueueUpdate);
      off('playback-state-update', handlePlaybackUpdate);
      off('vote-update', handleVoteUpdate);
      off('suggestion-added', handleSuggestionUpdate);
      off('sync-request', handleSyncRequest);
    };
  }, [isConnected, restaurantId, on, off, onQueueUpdate, onPlaybackUpdate, onVoteUpdate, onSuggestionUpdate, forceSync]);

  // Sincronização periódica
  useEffect(() => {
    if (!isConnected || !restaurantId || syncInterval <= 0) return;

    console.log(`⏰ [GlobalSync] Configurando sync periódico a cada ${syncInterval}ms`);

    syncIntervalRef.current = setInterval(() => {
      forceSync();
    }, syncInterval);

    return () => {
      if (syncIntervalRef.current) {
        clearInterval(syncIntervalRef.current);
      }
    };
  }, [isConnected, restaurantId, syncInterval, forceSync]);

  // Sincronização inicial
  useEffect(() => {
    if (isConnected && restaurantId) {
      // Aguardar um pouco para garantir que a conexão está estável
      const timer = setTimeout(() => {
        forceSync();
      }, 2000);

      return () => clearTimeout(timer);
    }
  }, [isConnected, restaurantId, forceSync]);

  return {
    isConnected,
    forceSync,
    lastSync: lastSyncRef.current
  };
};

export default useGlobalSync;
