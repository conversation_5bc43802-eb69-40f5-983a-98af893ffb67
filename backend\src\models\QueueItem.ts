import {
  <PERSON>tity,
  PrimaryGeneratedColumn,
  Column,
  ManyTo<PERSON>ne,
  <PERSON><PERSON><PERSON><PERSON><PERSON>n,
  CreateDateColumn,
  UpdateDateColumn,
  Index,
} from "typeorm";
import { Restaurant } from "./Restaurant";
import { Suggestion } from "./Suggestion";
import { ClientSession } from "./ClientSession";
import { QueueItemDTO } from "../services/PlaybackQueueService";

/**
 * Entidade QueueItem - Representa um item na fila de reprodução
 * 
 * Esta entidade armazena os itens que estão na fila de reprodução de cada restaurante,
 * permitindo controle preciso da ordem, prioridade e status de reprodução.
 */
@Entity("queue_items")
@Index(["restaurant", "position"], { unique: true })
@Index(["restaurant", "isPlaying"])
@Index(["restaurant", "priority", "position"])
export class QueueItem {
  @PrimaryGeneratedColumn("uuid")
  id: string;

  // Relacionamentos
  @ManyToOne(() => Restaurant, { eager: true, onDelete: "CASCADE" })
  @JoinColumn({ name: "restaurant_id" })
  restaurant: Restaurant;

  @ManyToOne(() => Suggestion, { eager: true, onDelete: "CASCADE" })
  @JoinColumn({ name: "suggestion_id" })
  suggestion: Suggestion;

  @ManyToOne(() => ClientSession, { nullable: true, onDelete: "SET NULL" })
  @JoinColumn({ name: "session_id" })
  clientSession?: ClientSession;

  // Dados da música (desnormalizados para performance)
  @Column({ name: "youtube_video_id", type: "varchar" })
  youtubeVideoId: string;

  @Column({ type: "varchar" })
  title: string;

  @Column({ type: "varchar", nullable: true })
  artist: string;

  @Column({ type: "integer", default: 180 })
  duration: number; // em segundos

  @Column({ name: "thumbnail_url", type: "varchar", nullable: true })
  thumbnailUrl?: string;

  // Controle da fila
  @Column({ type: "integer" })
  position: number;

  @Column({ type: "integer", default: 2 })
  priority: number; // 1 = alta (pago), 2 = normal (gratuito)

  @Column({ name: "is_playing", type: "boolean", default: false })
  isPlaying: boolean;

  @Column({ name: "is_paid", type: "boolean", default: false })
  isPaid: boolean;

  @Column({
    name: "payment_amount",
    type: "decimal",
    precision: 10,
    scale: 2,
    nullable: true,
  })
  paymentAmount?: number;

  // Dados do cliente (desnormalizados)
  @Column({ name: "client_name", type: "varchar", nullable: true })
  clientName?: string;

  @Column({ name: "table_number", type: "integer", nullable: true })
  tableNumber?: number;

  @Column({ name: "session_token", type: "varchar", nullable: true })
  sessionToken?: string;

  // Timestamps de controle
  @Column({ name: "added_at", type: "timestamp", default: () => "CURRENT_TIMESTAMP" })
  addedAt: Date;

  @Column({ name: "played_at", type: "timestamp", nullable: true })
  playedAt?: Date;

  @Column({ name: "completed_at", type: "timestamp", nullable: true })
  completedAt?: Date;

  @Column({ name: "estimated_play_time", type: "timestamp", nullable: true })
  estimatedPlayTime?: Date;

  // Status e controle
  @Column({
    type: "enum",
    enum: ["pending", "playing", "completed", "skipped", "removed"],
    default: "pending",
  })
  status: "pending" | "playing" | "completed" | "skipped" | "removed";

  @Column({ name: "skip_reason", type: "varchar", nullable: true })
  skipReason?: string;

  // Metadados adicionais
  @Column({ type: "json", nullable: true })
  metadata?: {
    addedBy?: string;
    originalSuggestionId?: string;
    promotedFromFree?: boolean;
    autoAdded?: boolean;
    playbackSettings?: {
      volume?: number;
      fadeIn?: boolean;
      fadeOut?: boolean;
    };
  };

  @CreateDateColumn({ name: "created_at" })
  createdAt: Date;

  @UpdateDateColumn({ name: "updated_at" })
  updatedAt: Date;

  // Métodos auxiliares

  /**
   * Verifica se o item pode ser reproduzido
   */
  canPlay(): boolean {
    return this.status === "pending" && !this.isPlaying;
  }

  /**
   * Marca o item como tocando
   */
  markAsPlaying(): void {
    this.isPlaying = true;
    this.status = "playing";
    this.playedAt = new Date();
  }

  /**
   * Marca o item como completado
   */
  markAsCompleted(): void {
    this.isPlaying = false;
    this.status = "completed";
    this.completedAt = new Date();
  }

  /**
   * Marca o item como pulado
   */
  markAsSkipped(reason?: string): void {
    this.isPlaying = false;
    this.status = "skipped";
    this.skipReason = reason;
    this.completedAt = new Date();
  }

  /**
   * Calcula o tempo estimado de reprodução baseado na posição na fila
   */
  calculateEstimatedPlayTime(averageSongDuration: number = 180): Date {
    const estimatedWaitTime = (this.position - 1) * averageSongDuration * 1000;
    return new Date(Date.now() + estimatedWaitTime);
  }

  /**
   * Retorna o nome da mesa formatado
   */
  getTableName(): string {
    return `Mesa ${this.tableNumber || "?"}`;
  }

  /**
   * Retorna a duração formatada (mm:ss)
   */
  getFormattedDuration(): string {
    const minutes = Math.floor(this.duration / 60);
    const seconds = this.duration % 60;
    return `${minutes}:${seconds.toString().padStart(2, "0")}`;
  }

  /**
   * Verifica se é um item prioritário (pago)
   */
  isPriority(): boolean {
    return this.isPaid && this.priority === 1;
  }

  /**
   * Retorna o valor do pagamento formatado
   */
  getFormattedPaymentAmount(): string {
    if (!this.paymentAmount) return "Gratuito";
    return `R$ ${(this.paymentAmount / 100).toFixed(2)}`;
  }

  /**
   * Converte para o formato da interface QueueItemDTO
   */
  toInterface(): QueueItemDTO {
    return {
      id: this.id,
      suggestionId: this.suggestion.id,
      title: this.title,
      artist: this.artist,
      duration: this.duration,
      thumbnailUrl: this.thumbnailUrl,
      youtubeVideoId: this.youtubeVideoId,
      isPaid: this.isPaid,
      paymentAmount: this.paymentAmount ? this.paymentAmount / 100 : undefined,
      clientName: this.clientName,
      tableName: this.getTableName(),
      sessionId: this.sessionToken || "",
      addedAt: this.addedAt,
      estimatedPlayTime: this.estimatedPlayTime,
      position: this.position,
      priority: this.priority,
      likeCount: 0, // Será preenchido pelo serviço com dados reais dos votos
    };
  }
}
