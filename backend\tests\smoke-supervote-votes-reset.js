/* eslint-disable no-console */
// Verifica votos de uma música (por youtubeVideoId) e se zeram após conclusão

const BASE = process.env.BASE_URL || 'http://localhost:8001/api/v1';
const RESTAURANT_ID = process.env.RESTAURANT_ID || 'demo-restaurant';
const VIDEO_ID = process.env.VIDEO_ID || '10XarNSkw0s';

async function getRanking() {
  const url = `${BASE}/collaborative-playlist/${RESTAURANT_ID}/ranking?limit=200`;
  const res = await fetch(url);
  if (!res.ok) throw new Error(`ranking HTTP ${res.status}`);
  const json = await res.json();
  const items = Array.isArray(json?.data) ? json.data : [];
  return items.map((x) => ({
    youtubeVideoId: x.youtubeVideoId || x.youtubeId || x.id,
    voteCount: Number(x.voteCount || x.votes || 0),
    upvotes: Number(x.upvotes || 0),
    downvotes: Number(x.downvotes || 0),
    score: Number(x.score || 0),
    status: x.status || null,
    title: x.title,
  }));
}

async function getSuggestions() {
  const url = `${BASE}/suggestions/${RESTAURANT_ID}`;
  const res = await fetch(url);
  if (!res.ok) throw new Error(`suggestions HTTP ${res.status}`);
  const json = await res.json();
  const arr = Array.isArray(json?.suggestions) ? json.suggestions : (Array.isArray(json) ? json : []);
  return arr.map((x) => ({
    youtubeVideoId: x.youtubeVideoId || x.youtubeId || x.id,
    voteCount: Number(x.voteCount || x.votes || 0),
    upvotes: Number(x.upvotes || 0),
    downvotes: Number(x.downvotes || 0),
    score: Number(x.score || 0),
    status: x.status || null,
    title: x.title,
  }));
}

async function getVotes(videoId) {
  // tenta ranking primeiro, depois suggestions
  try {
    const r = await getRanking();
    const f = r.find((s) => (s.youtubeVideoId || '').includes(videoId));
    if (f) return { source: 'ranking', ...f };
  } catch {}
  try {
    const s = await getSuggestions();
    const f2 = s.find((sug) => (sug.youtubeVideoId || '').includes(videoId));
    if (f2) return { source: 'suggestions', ...f2 };
  } catch {}
  return null;
}

async function completeByVideo(videoId) {
  const url = `${BASE}/playback-queue/${RESTAURANT_ID}/complete-by-video`;
  const res = await fetch(url, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ youtubeVideoId: videoId }),
  });
  const json = await res.json().catch(() => ({}));
  return { status: res.status, json };
}

async function main() {
  console.log('🎯 Verificando votos antes/depois de completar:', { RESTAURANT_ID, VIDEO_ID });
  const before = await getVotes(VIDEO_ID);
  if (!before) {
    console.log('⚠️ Música não encontrada em ranking/sugestões. Ainda assim vou tentar completar e reconsultar.');
  } else {
    console.log('Antes:', before);
  }

  const done = await completeByVideo(VIDEO_ID);
  console.log('Complete-by-video:', done.status, done.json?.message || done.json?.error || done.json?.status);

  // aguardar pequeno intervalo para a persistência refletir
  await new Promise((r) => setTimeout(r, 1000));

  const after = await getVotes(VIDEO_ID);
  console.log('Depois:', after);

  if (!after) {
    console.log('✅ PASS: Música não aparece mais (pode ter sido removida/ocultada após completar).');
    process.exit(0);
  }
  const zeroed = Number(after.voteCount) === 0 && Number(after.upvotes) === 0 && Number(after.downvotes) === 0;
  if (zeroed) {
    console.log('✅ PASS: Votos zerados após conclusão.');
    process.exit(0);
  } else {
    console.log('❌ FAIL: Votos não zeraram.', { after });
    process.exit(2);
  }
}

if (typeof fetch !== 'function') {
  console.error('Node fetch indisponível. Use Node 18+');
  process.exit(3);
}

main().catch((e) => {
  console.error('Erro:', e?.message || e);
  process.exit(1);
});
