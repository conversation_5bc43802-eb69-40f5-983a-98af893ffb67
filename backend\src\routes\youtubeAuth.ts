import { Router, Request, Response } from "express";
import { param, body, validationResult } from "../utils/validation";
import asyncHandler from "../middleware/asyncHandler";
import { authMiddleware } from "../middleware/auth";
import { youtubeOAuthService } from "../services/YouTubeOAuthService";
// VotingPlaylistService removido — use LivePlaylistService e o VotingOrchestratorService
import { ValidationError } from "../middleware/errorHandler";

const router = Router();

/**
 * @swagger
 * /api/v1/youtube-auth/{restaurantId}/authorize:
 *   get:
 *     summary: Iniciar processo de autorização OAuth com YouTube
 *     tags: [YouTube Auth]
 *     parameters:
 *       - in: path
 *         name: restaurantId
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: URL de autorização gerada
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 authUrl:
 *                   type: string
 *                 message:
 *                   type: string
 */
router.get(
  "/:restaurantId/authorize",
  [
    param("restaurantId")
      .notEmpty()
      .withMessage("ID do restaurante é obrigatório"),
  ],
  authMiddleware,
  asyncHandler(async (req: Request, res: Response) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new ValidationError("Dados inválidos", errors.array());
    }

    const { restaurantId } = req.params;

    try {
      const authUrl = youtubeOAuthService.generateAuthUrl(restaurantId);

      res.json({
        success: true,
        authUrl,
        message: "Acesse a URL para autorizar o acesso ao YouTube",
        instructions: [
          "1. Clique na URL de autorização",
          "2. Faça login na sua conta YouTube Premium",
          "3. Autorize o acesso às suas playlists",
          "4. Você será redirecionado de volta automaticamente",
        ],
      });
    } catch (error) {
      console.error("Erro ao gerar URL de autorização:", error);
      res.status(500).json({
        success: false,
        message: "Erro ao gerar URL de autorização",
      });
    }
  })
);

/**
 * @swagger
 * /api/v1/youtube-auth/callback:
 *   get:
 *     summary: Callback OAuth do YouTube
 *     tags: [YouTube Auth]
 *     parameters:
 *       - in: query
 *         name: code
 *         required: true
 *         schema:
 *           type: string
 *       - in: query
 *         name: state
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Autorização concluída com sucesso
 */
router.get(
  "/callback",
  asyncHandler(async (req: Request, res: Response) => {
    const { code, state: restaurantId, error } = req.query;

    if (error) {
      return res.redirect(
        `${process.env.FRONTEND_URL}/restaurant/demo-restaurant/dashboard?auth_error=${error}`
      );
    }

    if (!code || !restaurantId) {
      return res.redirect(
        `${process.env.FRONTEND_URL}/restaurant/demo-restaurant/dashboard?auth_error=missing_params`
      );
    }

    try {
      await youtubeOAuthService.exchangeCodeForTokens(
        code as string,
        restaurantId as string
      );

      // Redirecionar para o dashboard com sucesso
      res.redirect(
        `${process.env.FRONTEND_URL}/restaurant/${restaurantId}/dashboard?auth_success=true`
      );
    } catch (error) {
      console.error("Erro no callback OAuth:", error);
      res.redirect(
        `${process.env.FRONTEND_URL}/restaurant/demo-restaurant/dashboard?auth_error=token_exchange_failed`
      );
    }
  })
);

/**
 * @swagger
 * /api/v1/youtube-auth/{restaurantId}/status:
 *   get:
 *     summary: Verificar status da autenticação YouTube
 *     tags: [YouTube Auth]
 *     parameters:
 *       - in: path
 *         name: restaurantId
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Status da autenticação
 */
router.get(
  "/:restaurantId/status",
  [
    param("restaurantId")
      .notEmpty()
      .withMessage("ID do restaurante é obrigatório"),
  ],
  authMiddleware,
  asyncHandler(async (req: Request, res: Response) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new ValidationError("Dados inválidos", errors.array());
    }

    const { restaurantId } = req.params;

    try {
      const isAuthenticated = await youtubeOAuthService.isAuthenticated(
        restaurantId
      );

      res.json({
        success: true,
        isAuthenticated,
        message: isAuthenticated
          ? "Restaurante autenticado com YouTube"
          : "Restaurante não autenticado com YouTube",
        capabilities: isAuthenticated
          ? [
              "Criar playlists próprias",
              "Reordenar playlists em tempo real",
              "Controle total sobre ordem das músicas",
              "Sincronização automática com votações",
            ]
          : [
              "Apenas visualização de playlists públicas",
              "Sem controle de ordem das músicas",
            ],
      });
    } catch (error) {
      console.error("Erro ao verificar status:", error);
      res.status(500).json({
        success: false,
        message: "Erro ao verificar status da autenticação",
      });
    }
  })
);

/**
 * @swagger
 * /api/v1/youtube-auth/{restaurantId}/create-playlist:
 *   post:
 *     summary: Criar nova playlist no YouTube
 *     tags: [YouTube Auth]
 *     parameters:
 *       - in: path
 *         name: restaurantId
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - title
 *             properties:
 *               title:
 *                 type: string
 *               description:
 *                 type: string
 *     responses:
 *       200:
 *         description: Playlist criada com sucesso
 */
router.post(
  "/:restaurantId/create-playlist",
  [
    param("restaurantId")
      .notEmpty()
      .withMessage("ID do restaurante é obrigatório"),
    body("title").notEmpty().withMessage("Título da playlist é obrigatório"),
    body("description")
      .optional()
      .isString()
      .withMessage("Descrição deve ser uma string"),
  ],
  authMiddleware,
  asyncHandler(async (req: Request, res: Response) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new ValidationError("Dados inválidos", errors.array());
    }

    const { restaurantId } = req.params;
    const { title, description } = req.body;

    try {
      const playlistId = await youtubeOAuthService.createPlaylist(
        restaurantId,
        title,
        description ||
          `Playlist interativa do restaurante - ${new Date().toLocaleDateString()}`
      );

      res.json({
        success: true,
        playlistId,
        playlistUrl: `https://www.youtube.com/playlist?list=${playlistId}`,
        message: "Playlist criada com sucesso no YouTube!",
        nextSteps: [
          "Adicione músicas à playlist no YouTube",
          "Importe a playlist no sistema",
          "Ative a votação dos clientes",
          "A ordem será atualizada automaticamente",
        ],
      });
    } catch (error) {
      console.error("Erro ao criar playlist:", error);
      res.status(500).json({
        success: false,
        message:
          error instanceof Error ? error.message : "Erro ao criar playlist",
      });
    }
  })
);

/**
 * @swagger
 * /api/v1/youtube-auth/{restaurantId}/playlists/{playlistId}/reorder:
 *   post:
 *     summary: Reordenar playlist baseada em votações
 *     tags: [YouTube Auth]
 *     parameters:
 *       - in: path
 *         name: restaurantId
 *         required: true
 *         schema:
 *           type: string
 *       - in: path
 *         name: playlistId
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Playlist reordenada com sucesso
 */
router.post(
  "/:restaurantId/playlists/:playlistId/reorder",
  [
    param("restaurantId")
      .notEmpty()
      .withMessage("ID do restaurante é obrigatório"),
    param("playlistId").notEmpty().withMessage("ID da playlist é obrigatório"),
  ],
  authMiddleware,
  asyncHandler(async (req: Request, res: Response) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new ValidationError("Dados inválidos", errors.array());
    }

    const { restaurantId, playlistId } = req.params;

    try {
      // DESABILITADO - Usar LivePlaylistService
      throw new ValidationError(
        "Esta funcionalidade foi migrada para o LivePlaylistService. Use /api/v1/live-playlist/ endpoints."
      );

      // Código removido - funcionalidade migrada
    } catch (error) {
      console.error("Erro ao reordenar playlist:", error);
      res.status(500).json({
        success: false,
        message: "Erro ao reordenar playlist",
      });
    }
  })
);

/**
 * @swagger
 * /api/v1/youtube-auth/{restaurantId}/playlists/{playlistId}/auto-reorder:
 *   post:
 *     summary: Ativar/desativar reordenação automática
 *     tags: [YouTube Auth]
 *     parameters:
 *       - in: path
 *         name: restaurantId
 *         required: true
 *         schema:
 *           type: string
 *       - in: path
 *         name: playlistId
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - enabled
 *             properties:
 *               enabled:
 *                 type: boolean
 *     responses:
 *       200:
 *         description: Configuração atualizada
 */
router.post(
  "/:restaurantId/playlists/:playlistId/auto-reorder",
  [
    param("restaurantId")
      .notEmpty()
      .withMessage("ID do restaurante é obrigatório"),
    param("playlistId").notEmpty().withMessage("ID da playlist é obrigatório"),
    body("enabled").isBoolean().withMessage("Campo enabled deve ser boolean"),
  ],
  authMiddleware,
  asyncHandler(async (req: Request, res: Response) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new ValidationError("Dados inválidos", errors.array());
    }

    const { restaurantId, playlistId } = req.params;
    const { enabled } = req.body;

    try {
      if (enabled) {
        // DESABILITADO - Usar LivePlaylistService
        throw new ValidationError(
          "Esta funcionalidade foi migrada para o LivePlaylistService."
        );

        // Código removido - funcionalidade migrada
      } else {
        res.json({
          success: true,
          message:
            "Reordenação automática desativada. A ordem da playlist permanecerá fixa.",
        });
      }
    } catch (error) {
      console.error("Erro ao configurar auto-reordenação:", error);
      res.status(500).json({
        success: false,
        message: "Erro ao configurar reordenação automática",
      });
    }
  })
);

export default router;
