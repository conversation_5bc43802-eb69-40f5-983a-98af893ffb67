import { Router } from "express";
import { param, body, validateRequest } from "../utils/validation";
import { asyncHandler } from "../middleware/errorHandler";
import { authMiddleware } from "../middleware/auth";
import { livePlaylistService } from "../services/LivePlaylistService";
import { logger } from "../utils/logger";

const router = Router();

/**
 * @swagger
 * /api/v1/live-playlist/{restaurantId}/registrar-supervoto:
 *   post:
 *     summary: Registrar supervoto (PIX compra votos)
 *     description: Registra supervoto (R$ 5/20/50) convertendo em votos. A vencedora toca ao final da rodada de 5 minutos.
 *     tags: [Live Playlist]
 *     parameters:
 *       - in: path
 *         name: restaurantId
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               suggestionId:
 *                 type: string
 *                 format: uuid
 *                 description: ID da sugestão que foi paga
 *             required:
 *               - suggestionId
 *     responses:
 *       200:
 *         description: Música inserida com sucesso na playlist
 *       400:
 *         description: Dados inválidos
 *       401:
 *         description: Não autorizado
 *       500:
 *         description: Erro interno
 */
router.post(
  "/:restaurantId/registrar-supervoto",
  [
    param("restaurantId")
      .notEmpty()
      .withMessage("ID do restaurante é obrigatório"),
    body("suggestionId")
      .isUUID()
      .withMessage("ID da sugestão deve ser um UUID válido"),
  ],
  authMiddleware,
  asyncHandler(async (req, res) => {
    validateRequest(req);

    const { restaurantId } = req.params;
    const { suggestionId } = req.body;

  logger.info("Registrando supervoto para sugestão", {
      restaurantId,
      suggestionId,
      userId: req.user?.id,
    });

  const result = await livePlaylistService.insertPaidSuggestionNext(
      restaurantId,
      suggestionId
    );

    if (result.success) {
      res.json({
        success: true,
        message: result.message,
        data: {
          newPosition: result.newPosition,
          type: "supervote_registered",
        },
      });
    } else {
      res.status(400).json({
        success: false,
        message: result.message,
      });
    }
  })
);

/**
 * @swagger
 * /api/v1/live-playlist/{restaurantId}/add-free:
 *   post:
 *     summary: Adicionar música gratuita ao final da playlist
 *     description: Cliente escolhe música gratuita que vai para o final da playlist
 *     tags: [Live Playlist]
 *     parameters:
 *       - in: path
 *         name: restaurantId
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               suggestionId:
 *                 type: string
 *                 format: uuid
 *                 description: ID da sugestão gratuita
 *             required:
 *               - suggestionId
 *     responses:
 *       200:
 *         description: Música adicionada com sucesso
 */
router.post(
  "/:restaurantId/add-free",
  [
    param("restaurantId")
      .notEmpty()
      .withMessage("ID do restaurante é obrigatório"),
    body("suggestionId")
      .isUUID()
      .withMessage("ID da sugestão deve ser um UUID válido"),
  ],
  authMiddleware,
  asyncHandler(async (req, res) => {
    validateRequest(req);

    const { restaurantId } = req.params;
    const { suggestionId } = req.body;

    logger.info("Adicionando música gratuita à playlist", {
      restaurantId,
      suggestionId,
      userId: req.user?.id,
    });

    const result = await livePlaylistService.addFreeSuggestionToEnd(
      restaurantId,
      suggestionId
    );

    if (result.success) {
      res.json({
        success: true,
        message: result.message,
        data: {
          position: result.position,
          type: "free_addition",
        },
      });
    } else {
      res.status(400).json({
        success: false,
        message: result.message,
      });
    }
  })
);

/**
 * @swagger
 * /api/v1/live-playlist/{restaurantId}/state:
 *   get:
 *     summary: Obter estado atual da playlist ao vivo
 *     description: Retorna informações sobre a playlist atual, músicas na fila e estatísticas
 *     tags: [Live Playlist]
 *     parameters:
 *       - in: path
 *         name: restaurantId
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Estado da playlist obtido com sucesso
 */
router.get(
  "/:restaurantId/state",
  [
    param("restaurantId")
      .notEmpty()
      .withMessage("ID do restaurante é obrigatório"),
  ],
  asyncHandler(async (req, res) => {
    validateRequest(req);

    const { restaurantId } = req.params;

    const state = await livePlaylistService.getLivePlaylistState(restaurantId);

    if (state) {
      res.json({
        success: true,
        data: state,
      });
    } else {
      res.status(404).json({
        success: false,
        message: "Playlist ativa não encontrada",
      });
    }
  })
);

/**
 * @swagger
 * /api/v1/live-playlist/{restaurantId}/registrar-supervoto-pagamento:
 *   post:
 *     summary: Processar pagamento PIX e registrar supervoto
 *     description: Processa pagamento (validação externa) e registra supervoto que conta votos na rodada.
 *     tags: [Live Playlist]
 *     parameters:
 *       - in: path
 *         name: restaurantId
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               suggestionId:
 *                 type: string
 *                 format: uuid
 *               paymentId:
 *                 type: string
 *                 description: ID do pagamento PIX confirmado
 *               amount:
 *                 type: number
 *                 description: Valor pago (deve ser 2.00)
 *             required:
 *               - suggestionId
 *               - paymentId
 *               - amount
 *     responses:
 *       200:
 *         description: Pagamento processado e música inserida
 */
router.post(
  "/:restaurantId/registrar-supervoto-pagamento",
  [
    param("restaurantId")
      .notEmpty()
      .withMessage("ID do restaurante é obrigatório"),
    body("suggestionId")
      .isUUID()
      .withMessage("ID da sugestão deve ser um UUID válido"),
    body("paymentId").notEmpty().withMessage("ID do pagamento é obrigatório"),
    body("amount")
      .isFloat({ min: 2.0, max: 2.0 })
      .withMessage("Valor deve ser exatamente R$ 2,00"),
  ],
  asyncHandler(async (req, res) => {
    validateRequest(req);

    const { restaurantId } = req.params;
    const { suggestionId, paymentId, amount } = req.body;

  logger.info("Processando pagamento PIX para registrar supervoto", {
      restaurantId,
      suggestionId,
      paymentId,
      amount,
    });

    // TODO: Validar pagamento PIX com Mercado Pago
    // const paymentValid = await paymentService.validatePixPayment(paymentId, amount);
    // if (!paymentValid) {
    //   return res.status(400).json({
    //     success: false,
    //     message: "Pagamento PIX não confirmado"
    //   });
    // }

  // Registrar supervoto relacionado ao pagamento
    const result = await livePlaylistService.insertPaidSuggestionNext(
      restaurantId,
      suggestionId
    );

    if (result.success) {
      res.json({
        success: true,
        message: "Pagamento confirmado! Supervoto registrado.",
        data: {
          newPosition: result.newPosition,
          paymentId,
          amount,
          type: "supervote_registered",
        },
      });
    } else {
      res.status(400).json({
        success: false,
        message: result.message,
      });
    }
  })
);

export default router;
