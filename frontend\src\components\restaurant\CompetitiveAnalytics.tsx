import React, { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { useRestaurantContext } from "./RestaurantDashboard";
import { buildApiUrl } from "../../config/api";
import {
  TrendingUp,
  DollarSign,
  Star,
  Users,
  Trophy,
  Clock,
  CreditCard,
  Vote,
  Target,
  BarChart3,
  PieChart,
  Calendar,
  Heart,
  ThumbsUp,
  ThumbsDown,
} from "lucide-react";

interface CompetitiveAnalytics {
  period: string;
  revenue: {
    total: number;
    totalPayments: number;
    averageAmount: number;
    dailyRevenue: Array<{ date: string; revenue: number; count: number }>;
  };
  voting: {
    totalVotes: number;
    totalPerformances: number;
    averageRating: number;
    topPerformers: Array<{
      rank: number;
      clientName: string;
      tableName: string;
      songTitle: string;
      averageRating: number;
      totalVotes: number;
    }>;
  };
  queue: {
    totalItems: number;
    paidItems: number;
    freeItems: number;
    paidPercentage: number;
  };
  engagement: {
    paymentConversionRate: number;
    votingParticipationRate: number;
    averageVotesPerSong: number;
  };
  trends: {
    hourlyRevenue: Array<{ hour: number; revenue: number; count: number }>;
    topTables: Array<{
      tableName: string;
      totalVotes: number;
      performances: number;
      averageRating: number;
    }>;
    topLikedSongs: Array<{
      youtubeVideoId: string;
      likes: number;
      dislikes: number;
      likeRatio: number;
    }>;
  };
  likes: {
    totalLikes: number;
    totalDislikes: number;
    likeDislikeRatio: number;
    totalInteractions: number;
  };
}

interface CompetitiveAnalyticsProps {
  restaurantId?: string; // Opcional, pois será obtido do contexto
}

const CompetitiveAnalytics: React.FC<CompetitiveAnalyticsProps> = ({
  restaurantId: propRestaurantId,
}) => {
  const { restaurantId: contextRestaurantId } = useRestaurantContext();
  const restaurantId = propRestaurantId || contextRestaurantId;

  const [analytics, setAnalytics] = useState<CompetitiveAnalytics | null>(null);
  const [loading, setLoading] = useState(false);
  const [period, setPeriod] = useState<"1d" | "7d" | "30d">("1d");

  useEffect(() => {
    loadAnalytics();
  }, [restaurantId, period]);

  const loadAnalytics = async () => {
    if (!restaurantId) {
      console.error("Restaurant ID não encontrado");
      return;
    }

    try {
      setLoading(true);

      const response = await fetch(
        buildApiUrl(`/analytics/competitive/${restaurantId}?period=${period}`)
      );

      if (response.ok) {
        const data = await response.json();
        setAnalytics(data.analytics);
      }
    } catch (error) {
      console.error("Erro ao carregar analytics competitivos:", error);
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (value: number | null | undefined): string => {
    if (value === null || value === undefined || isNaN(value)) {
      return "R$ 0,00";
    }
    return new Intl.NumberFormat("pt-BR", {
      style: "currency",
      currency: "BRL",
    }).format(value);
  };

  const formatPercentage = (value: number | null | undefined): string => {
    if (value === null || value === undefined || isNaN(value)) {
      return "0.0%";
    }
    return `${value.toFixed(1)}%`;
  };

  const getPeriodLabel = (period: string): string => {
    const labels = {
      "1d": "Últimas 24h",
      "7d": "Últimos 7 dias",
      "30d": "Últimos 30 dias",
    };
    return labels[period as keyof typeof labels] || period;
  };

  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow-sm p-6">
        <div className="flex items-center justify-center space-x-2">
          <div className="w-6 h-6 border-2 border-blue-600 border-t-transparent rounded-full animate-spin" />
          <span className="text-gray-600">
            Carregando analytics competitivos...
          </span>
        </div>
      </div>
    );
  }

  if (!analytics) {
    return (
      <div className="bg-white rounded-lg shadow-sm p-6">
        <div className="text-center text-gray-500">
          <BarChart3 className="w-12 h-12 mx-auto mb-3 opacity-50" />
          <p>Nenhum dado disponível</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white rounded-lg shadow-sm p-6">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-2xl font-bold text-gray-900 flex items-center space-x-2">
              <Trophy className="w-6 h-6 text-yellow-500" />
              <span>Analytics Competitivos</span>
            </h2>
            <p className="text-gray-600 mt-1">
              Métricas de votação, receita e engajamento -{" "}
              {getPeriodLabel(period)}
            </p>
          </div>

          <div className="flex items-center space-x-2">
            {(["1d", "7d", "30d"] as const).map((p) => (
              <button
                key={p}
                onClick={() => setPeriod(p)}
                className={`px-3 py-1 rounded-lg text-sm font-medium transition-colors ${
                  period === p
                    ? "bg-blue-600 text-white"
                    : "bg-gray-100 text-gray-600 hover:bg-gray-200"
                }`}
              >
                {getPeriodLabel(p)}
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* KPI Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {/* Receita Total */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-white rounded-lg shadow-sm p-6"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Receita Total</p>
              <p className="text-2xl font-bold text-green-600">
                {formatCurrency(analytics.revenue.total)}
              </p>
              <p className="text-xs text-gray-500 mt-1">
                {analytics.revenue.totalPayments} pagamentos
              </p>
            </div>
            <div className="p-3 bg-green-100 rounded-full">
              <DollarSign className="w-6 h-6 text-green-600" />
            </div>
          </div>
        </motion.div>

        {/* Total de Votos */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="bg-white rounded-lg shadow-sm p-6"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">
                Total de Votos
              </p>
              <p className="text-2xl font-bold text-blue-600">
                {analytics.voting.totalVotes}
              </p>
              <p className="text-xs text-gray-500 mt-1">
                {analytics.voting.totalPerformances} performances
              </p>
            </div>
            <div className="p-3 bg-blue-100 rounded-full">
              <Vote className="w-6 h-6 text-blue-600" />
            </div>
          </div>
        </motion.div>

        {/* Avaliação Média */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="bg-white rounded-lg shadow-sm p-6"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">
                Avaliação Média
              </p>
              <p className="text-2xl font-bold text-yellow-600">
                {(analytics.voting.averageRating || 0).toFixed(1)}⭐
              </p>
              <p className="text-xs text-gray-500 mt-1">de 5.0 estrelas</p>
            </div>
            <div className="p-3 bg-yellow-100 rounded-full">
              <Star className="w-6 h-6 text-yellow-600" />
            </div>
          </div>
        </motion.div>

        {/* Taxa de Conversão */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="bg-white rounded-lg shadow-sm p-6"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">
                Taxa de Conversão
              </p>
              <p className="text-2xl font-bold text-purple-600">
                {formatPercentage(analytics.engagement.paymentConversionRate)}
              </p>
              <p className="text-xs text-gray-500 mt-1">pagamentos aprovados</p>
            </div>
            <div className="p-3 bg-purple-100 rounded-full">
              <Target className="w-6 h-6 text-purple-600" />
            </div>
          </div>
        </motion.div>
      </div>

      {/* Curtidas Section */}
      {analytics.likes && (
        <div className="bg-white rounded-lg shadow-sm p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center space-x-2">
            <Heart className="w-5 h-5 text-pink-600" />
            <span>Curtidas e Interações</span>
          </h3>

          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            <div className="text-center p-4 bg-pink-50 rounded-lg">
              <div className="flex items-center justify-center mb-2">
                <ThumbsUp className="w-6 h-6 text-pink-600" />
              </div>
              <p className="text-2xl font-bold text-pink-600">
                {analytics.likes.totalLikes}
              </p>
              <p className="text-sm text-gray-600">Total de Curtidas</p>
            </div>

            <div className="text-center p-4 bg-red-50 rounded-lg">
              <div className="flex items-center justify-center mb-2">
                <ThumbsDown className="w-6 h-6 text-red-600" />
              </div>
              <p className="text-2xl font-bold text-red-600">
                {analytics.likes.totalDislikes}
              </p>
              <p className="text-sm text-gray-600">Total de Dislikes</p>
            </div>

            <div className="text-center p-4 bg-green-50 rounded-lg">
              <div className="flex items-center justify-center mb-2">
                <Heart className="w-6 h-6 text-green-600" />
              </div>
              <p className="text-2xl font-bold text-green-600">
                {analytics.likes.likeDislikeRatio}%
              </p>
              <p className="text-sm text-gray-600">Ratio Positivo</p>
            </div>

            <div className="text-center p-4 bg-blue-50 rounded-lg">
              <div className="flex items-center justify-center mb-2">
                <Target className="w-6 h-6 text-blue-600" />
              </div>
              <p className="text-2xl font-bold text-blue-600">
                {analytics.likes.totalInteractions}
              </p>
              <p className="text-sm text-gray-600">Total de Interações</p>
            </div>
          </div>

          {/* Top Liked Songs */}
          {analytics.trends.topLikedSongs && analytics.trends.topLikedSongs.length > 0 && (
            <div>
              <h4 className="text-md font-semibold text-gray-800 mb-3">
                Músicas Mais Curtidas
              </h4>
              <div className="space-y-2">
                {analytics.trends.topLikedSongs.slice(0, 5).map((song, index) => (
                  <div key={song.youtubeVideoId} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div className="flex items-center space-x-3">
                      <div className="w-8 h-8 bg-pink-100 rounded-full flex items-center justify-center">
                        <span className="text-sm font-bold text-pink-600">
                          {index + 1}
                        </span>
                      </div>
                      <div>
                        <p className="font-medium text-gray-900 text-sm">
                          Música #{index + 1}
                        </p>
                        <p className="text-xs text-gray-500">
                          ID: {song.youtubeVideoId.slice(0, 8)}...
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-4 text-sm">
                      <div className="flex items-center space-x-1 text-green-600">
                        <ThumbsUp className="w-4 h-4" />
                        <span>{song.likes}</span>
                      </div>
                      <div className="flex items-center space-x-1 text-red-600">
                        <ThumbsDown className="w-4 h-4" />
                        <span>{song.dislikes}</span>
                      </div>
                      <div className="text-blue-600 font-medium">
                        {song.likeRatio}%
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      )}

      {/* Charts Row */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Fila de Reprodução */}
        <div className="bg-white rounded-lg shadow-sm p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center space-x-2">
            <PieChart className="w-5 h-5 text-blue-600" />
            <span>Fila de Reprodução</span>
          </h3>

          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Total de músicas</span>
              <span className="font-semibold">
                {analytics.queue.totalItems}
              </span>
            </div>

            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600 flex items-center space-x-1">
                <CreditCard className="w-3 h-3 text-yellow-500" />
                <span>Músicas pagas</span>
              </span>
              <span className="font-semibold text-yellow-600">
                {analytics.queue.paidItems} (
                {formatPercentage(analytics.queue.paidPercentage)})
              </span>
            </div>

            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Músicas gratuitas</span>
              <span className="font-semibold text-gray-600">
                {analytics.queue.freeItems}
              </span>
            </div>

            {/* Progress Bar */}
            <div className="mt-4">
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div
                  className="bg-yellow-500 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${analytics.queue.paidPercentage}%` }}
                />
              </div>
              <p className="text-xs text-gray-500 mt-1 text-center">
                {formatPercentage(analytics.queue.paidPercentage)} músicas pagas
              </p>
            </div>
          </div>
        </div>

        {/* Engajamento */}
        <div className="bg-white rounded-lg shadow-sm p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center space-x-2">
            <TrendingUp className="w-5 h-5 text-green-600" />
            <span>Métricas de Engajamento</span>
          </h3>

          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Taxa de Pagamento</span>
              <span className="font-semibold text-green-600">
                {formatPercentage(analytics.engagement.paymentConversionRate)}
              </span>
            </div>

            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">
                Participação em Votação
              </span>
              <span className="font-semibold text-blue-600">
                {formatPercentage(analytics.engagement.votingParticipationRate)}
              </span>
            </div>

            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Votos por Música</span>
              <span className="font-semibold text-purple-600">
                {analytics.engagement.averageVotesPerSong}
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Top Performers */}
      <div className="bg-white rounded-lg shadow-sm p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center space-x-2">
          <Trophy className="w-5 h-5 text-yellow-500" />
          <span>Top Performances</span>
        </h3>

        {analytics.voting.topPerformers.length > 0 ? (
          <div className="space-y-3">
            {analytics.voting.topPerformers.map((performer, index) => (
              <div
                key={index}
                className="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
              >
                <div className="flex items-center space-x-3">
                  <div
                    className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold ${
                      index === 0
                        ? "bg-yellow-500 text-white"
                        : index === 1
                        ? "bg-gray-400 text-white"
                        : index === 2
                        ? "bg-orange-500 text-white"
                        : "bg-gray-200 text-gray-600"
                    }`}
                  >
                    {index + 1}
                  </div>

                  <div>
                    <p className="font-medium text-gray-900">
                      {performer.songTitle}
                    </p>
                    <p className="text-sm text-gray-600">
                      {performer.clientName} • {performer.tableName}
                    </p>
                  </div>
                </div>

                <div className="text-right">
                  <div className="flex items-center space-x-1">
                    <Star className="w-4 h-4 text-yellow-500" />
                    <span className="font-semibold">
                      {(performer.averageRating || 0).toFixed(1)}
                    </span>
                  </div>
                  <p className="text-xs text-gray-500">
                    {performer.totalVotes} votos
                  </p>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-8 text-gray-500">
            <Trophy className="w-12 h-12 mx-auto mb-3 opacity-50" />
            <p>Nenhuma performance avaliada ainda</p>
          </div>
        )}
      </div>

      {/* Top Tables */}
      {analytics.trends.topTables.length > 0 && (
        <div className="bg-white rounded-lg shadow-sm p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center space-x-2">
            <Users className="w-5 h-5 text-blue-600" />
            <span>Mesas Mais Ativas</span>
          </h3>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {analytics.trends.topTables.map((table, index) => (
              <div key={index} className="p-4 bg-gray-50 rounded-lg">
                <div className="flex items-center justify-between mb-2">
                  <h4 className="font-medium text-gray-900">
                    {table.tableName}
                  </h4>
                  <div className="flex items-center space-x-1">
                    <Star className="w-4 h-4 text-yellow-500" />
                    <span className="text-sm font-semibold">
                      {(table.averageRating || 0).toFixed(1)}
                    </span>
                  </div>
                </div>

                <div className="space-y-1 text-sm text-gray-600">
                  <div className="flex justify-between">
                    <span>Performances:</span>
                    <span>{table.performances}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Total de votos:</span>
                    <span>{table.totalVotes}</span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default CompetitiveAnalytics;
