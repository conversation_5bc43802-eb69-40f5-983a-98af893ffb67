// Verifica se há reordenação imediata da fila após um supervoto
// Esperado: NÃO deve reordenar imediatamente; apenas o orquestrador deve decidir ao fim da rodada

const http = require('http');

function req(method, path, body) {
  return new Promise((resolve) => {
    const options = {
      hostname: 'localhost',
      port: 8001,
      path,
      method,
      headers: body ? { 'Content-Type': 'application/json' } : {},
    };
    const q = http.request(options, (res) => {
      let d = '';
      res.on('data', (c) => (d += c));
      res.on('end', () => {
        try {
          resolve({ s: res.statusCode, j: JSON.parse(d) });
        } catch {
          resolve({ s: res.statusCode, j: { raw: d } });
        }
      });
    });
    q.on('error', (e) => resolve({ s: 0, j: { error: e.message } }));
    if (body) q.write(JSON.stringify(body));
    q.end();
  });
}

(async () => {
  const restaurantId = process.env.RESTAURANT_ID || 'demo-restaurant';
  const baseQueue = `/api/v1/playback-queue/${restaurantId}`;
  const baseCollab = `/api/v1/collaborative-playlist/${restaurantId}`;

  // 1) Capturar fila inicial
  const before = await req('GET', baseQueue);
  if (before.s !== 200 || !before.j?.queue?.length) {
    console.log('ERROR: queue unavailable', before.s, before.j?.error || before.j);
    process.exit(1);
  }
  const queue = before.j.queue;
  const top = queue.slice(0, 6);
  const posByVideo = new Map(top.map((x, i) => [x.youtubeVideoId, i]));

  // Escolher alvo: item de índice 2 (ou 1) para tentar promover
  const target = top[2] || top[1];
  if (!target) {
    console.log('ERROR: not enough items in queue to test');
    process.exit(1);
  }

  const initialPos = posByVideo.get(target.youtubeVideoId);
  console.log('Initial top:', top.map(x => `${x.title}(${x.youtubeVideoId})`).join(' | '));
  console.log('Target:', { title: target.title, youtubeVideoId: target.youtubeVideoId, initialPos });

  // 2) Aplicar supervoto mínimo (R$5) ao alvo
  const paymentId = `smoke_${Date.now()}`;
  const sv = await req('POST', `${baseCollab}/supervote`, {
    youtubeVideoId: target.youtubeVideoId,
    paymentAmount: 5,
    paymentId,
  });
  console.log('Supervote resp:', sv.s, sv.j?.message || sv.j);

  // 3) Buscar fila logo em seguida
  const after = await req('GET', baseQueue);
  const afterTop = after.j?.queue?.slice(0, 6) || [];
  const newPos = afterTop.findIndex(x => x.youtubeVideoId === target.youtubeVideoId);
  const immediateReorder = newPos !== -1 && newPos < (initialPos ?? 999999);

  console.log('After top:', afterTop.map(x => `${x.title}(${x.youtubeVideoId})`).join(' | '));
  console.log('IMMEDIATE_REORDER_DETECTED', immediateReorder, `initial=${initialPos} new=${newPos}`);

  // Sinalizar saída não-zero se detectado (para pipelines)
  if (immediateReorder) process.exit(2); else process.exit(0);
})();
