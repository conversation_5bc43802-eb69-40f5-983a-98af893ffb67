import { AppDataSource } from "../config/database";
import { Repository } from "typeorm";
import { collaborativePlaylistService } from "./CollaborativePlaylistService";
import {
  IsString,
  IsNumber,
  IsOptional,
  IsBoolean,
  IsEnum,
  Min,
  Max,
  validate,
} from "class-validator";
import { plainToClass } from "class-transformer";
import { Suggestion, SuggestionStatus } from "../models/Suggestion";
import { Vote, VoteType } from "../models/Vote";
import { PlayHistory, PlayStatus } from "../models/PlayHistory";
import { Restaurant } from "../models/Restaurant";
import { Playlist, PlaylistStatus } from "../models/Playlist";
import { PlaylistSchedule } from "../models/PlaylistSchedule";
import { PlaylistTrack } from "../models/PlaylistTrack";
import { WebSocketService } from "./WebSocketService";
import { PlaylistAnalyticsService } from "./PlaylistAnalyticsService";
import { logger } from "../utils/logger";
import { redisClient } from "../config/redis";

/**
 * Enums para estados de reprodução
 */
export enum PlaybackTransitionMode {
  MANUAL = "manual",
  AUTOMATIC = "automatic",
  SCHEDULED = "scheduled",
}

export enum PlaybackAction {
  PLAY = "play",
  PAUSE = "pause",
  SKIP = "skip",
  STOP = "stop",
  NEXT = "next",
}

/**
 * Interfaces tipadas para reprodução
 */
export interface ITrack {
  id: string;
  title: string;
  artist: string;
  youtubeVideoId: string;
  duration: number;
  thumbnailUrl: string;
  upvotes: number;
  downvotes: number;
  score: number;
  suggestedBy?: string;
  createdAt: Date;
  metadata?: {
    genre?: string;
    mood?: string;
    language?: string;
    explicit?: boolean;
  };
}

export interface IPlaylist {
  id: string;
  name: string;
  type: string;
  description?: string;
  trackCount?: number;
  totalDuration?: number;
}

export interface IScheduledPlaylist {
  id: string;
  name: string;
  startsIn: number; // minutes
  startsAt: Date;
  priority: number;
}

export interface IPlaybackState {
  currentTrack: ITrack | null;
  isPlaying: boolean;
  currentTime: number;
  volume: number;
  queue: ITrack[];
  priorityQueue: ITrack[]; // Fila prioritária (músicas pagas)
  normalQueue: ITrack[]; // Fila normal (músicas gratuitas)
  history: ITrack[];
  currentPlaylist: IPlaylist | null;
  nextScheduledPlaylist: IScheduledPlaylist | null;
  transitionMode: PlaybackTransitionMode;
  isTransitioning: boolean;
  problematicTracks: string[];
  lastActivity: Date;
  sessionId?: string;
}

export interface IPlaybackStats {
  totalPlayed: number;
  totalSkipped: number;
  averagePlayTime: number;
  mostPlayedGenres: string[];
  peakHours: number[];
  skipRate: number;
}

/**
 * Classes de validação
 */
export class PlaybackActionDto {
  @IsString()
  restaurantId: string;

  @IsEnum(PlaybackAction)
  action: PlaybackAction;

  @IsOptional()
  @IsString()
  trackId?: string;
}

export class VolumeControlDto {
  @IsString()
  restaurantId: string;

  @IsNumber()
  @Min(0)
  @Max(100)
  volume: number;
}

export class PlaylistTransitionDto {
  @IsString()
  restaurantId: string;

  @IsString()
  playlistId: string;

  @IsOptional()
  @IsEnum(PlaybackTransitionMode)
  mode?: PlaybackTransitionMode;
}

/**
 * Classe de erro personalizada para reprodução
 */
export class PlaybackError extends Error {
  public readonly code: string;
  public readonly statusCode: number;
  public readonly isOperational: boolean;
  public readonly restaurantId?: string;
  public readonly trackId?: string;

  constructor(
    message: string,
    code: string = "PLAYBACK_ERROR",
    statusCode: number = 500,
    isOperational: boolean = true,
    restaurantId?: string,
    trackId?: string
  ) {
    super(message);
    this.name = "PlaybackError";
    this.code = code;
    this.statusCode = statusCode;
    this.isOperational = isOperational;
    this.restaurantId = restaurantId;
    this.trackId = trackId;

    Error.captureStackTrace(this, this.constructor);
  }
}

/**
 * Classe para gerenciar locks avançados com timeout
 */
class PlaybackMutex {
  private locks: Map<
    string,
    { locked: boolean; queue: Array<() => void>; timeout?: NodeJS.Timeout }
  > = new Map();
  private readonly lockTimeout = 30000; // 30 segundos

  async acquire(key: string): Promise<void> {
    return new Promise((resolve) => {
      if (!this.locks.has(key)) {
        this.locks.set(key, { locked: false, queue: [] });
      }

      const lock = this.locks.get(key)!;

      if (!lock.locked) {
        lock.locked = true;
        this.setLockTimeout(key);
        resolve();
      } else {
        lock.queue.push(resolve);
      }
    });
  }

  release(key: string): void {
    const lock = this.locks.get(key);
    if (!lock) return;

    if (lock.timeout) {
      clearTimeout(lock.timeout);
      lock.timeout = undefined;
    }

    if (lock.queue.length > 0) {
      const next = lock.queue.shift()!;
      this.setLockTimeout(key);
      next();
    } else {
      lock.locked = false;
    }
  }

  private setLockTimeout(key: string): void {
    const lock = this.locks.get(key);
    if (!lock) return;

    lock.timeout = setTimeout(() => {
      logger.warn(`Lock timeout para ${key} - forçando liberação`);
      this.forceRelease(key);
    }, this.lockTimeout);
  }

  private forceRelease(key: string): void {
    const lock = this.locks.get(key);
    if (!lock) return;

    if (lock.timeout) {
      clearTimeout(lock.timeout);
      lock.timeout = undefined;
    }

    lock.locked = false;

    // Processar próximo na fila
    if (lock.queue.length > 0) {
      const next = lock.queue.shift()!;
      lock.locked = true;
      this.setLockTimeout(key);
      next();
    }
  }
}

/**
 * Serviço de reprodução com gerenciamento avançado de estado
 *
 * @class PlaybackService
 * @description Gerencia reprodução de música, filas, playlists e transições
 * com sistema de locks avançado para prevenir race conditions.
 */
export class PlaybackService {
  private static instance: PlaybackService;
  private playbackStates: Map<string, IPlaybackState> = new Map();
  private playbackTimers: Map<string, NodeJS.Timeout> = new Map();
  private scheduleTimers: Map<string, NodeJS.Timeout> = new Map();
  private scheduleInterval?: NodeJS.Timeout;
  private cleanupInterval?: NodeJS.Timeout;
  private isShuttingDown = false;
  private wsService: WebSocketService;
  private analyticsService: PlaylistAnalyticsService;
  private mutex: PlaybackMutex = new PlaybackMutex();

  // Repositórios - inicialização lazy
  private _suggestionRepository?: Repository<Suggestion>;
  private _playHistoryRepository?: Repository<PlayHistory>;
  private _restaurantRepository?: Repository<Restaurant>;
  private _playlistRepository?: Repository<Playlist>;
  private _playlistScheduleRepository?: Repository<PlaylistSchedule>;

  private get suggestionRepository() {
    if (!this._suggestionRepository) {
      this._suggestionRepository = AppDataSource.getRepository(Suggestion);
    }
    return this._suggestionRepository;
  }

  private get playHistoryRepository() {
    if (!this._playHistoryRepository) {
      this._playHistoryRepository = AppDataSource.getRepository(PlayHistory);
    }
    return this._playHistoryRepository;
  }

  private get restaurantRepository() {
    if (!this._restaurantRepository) {
      this._restaurantRepository = AppDataSource.getRepository(Restaurant);
    }
    return this._restaurantRepository;
  }

  private get playlistRepository() {
    if (!this._playlistRepository) {
      this._playlistRepository = AppDataSource.getRepository(Playlist);
    }
    return this._playlistRepository;
  }

  private get playlistScheduleRepository() {
    if (!this._playlistScheduleRepository) {
      this._playlistScheduleRepository = AppDataSource.getRepository(PlaylistSchedule);
    }
    return this._playlistScheduleRepository;
  }

  private constructor() {
    this.wsService = WebSocketService.getInstance();
    this.analyticsService = PlaylistAnalyticsService.getInstance();

    // Iniciar verificação de agendamentos a cada minuto
    this.scheduleInterval = setInterval(() => {
      if (this.isShuttingDown) return;
      this.checkScheduledPlaylists();
    }, 60000);

    // Limpeza periódica de estados inativos
    this.cleanupInterval = setInterval(() => {
      if (this.isShuttingDown) return;
      this.cleanupInactiveStates();
    }, 300000); // 5 minutos

    logger.info("PlaybackService inicializado");
  }

  static getInstance(): PlaybackService {
    if (!PlaybackService.instance) {
      PlaybackService.instance = new PlaybackService();
    }
    return PlaybackService.instance;
  }

  /**
   * Valida dados de entrada usando class-validator
   * @private
   */
  private async validateInput<T extends object>(
    dto: new () => T,
    data: any
  ): Promise<T> {
    const instance = plainToClass(dto, data);
    const errors = await validate(instance);

    if (errors.length > 0) {
      const errorMessages = errors
        .map((error) => Object.values(error.constraints || {}).join(", "))
        .join("; ");

      throw new PlaybackError(
        `Dados inválidos: ${errorMessages}`,
        "VALIDATION_ERROR",
        400
      );
    }

    return instance;
  }

  /**
   * Limpa estados inativos para economizar memória
   * @private
   */
  private cleanupInactiveStates(): void {
    const now = new Date();
    const inactiveThreshold = 30 * 60 * 1000; // 30 minutos

    for (const [restaurantId, state] of Array.from(
      this.playbackStates.entries()
    )) {
      const timeSinceActivity = now.getTime() - state.lastActivity.getTime();

      if (timeSinceActivity > inactiveThreshold && !state.isPlaying) {
        logger.info(`Limpando estado inativo para restaurante ${restaurantId}`);
        this.playbackStates.delete(restaurantId);

        // Limpar timers associados
        const timer = this.playbackTimers.get(restaurantId);
        if (timer) {
          clearTimeout(timer);
          this.playbackTimers.delete(restaurantId);
        }

        const scheduleTimer = this.scheduleTimers.get(restaurantId);
        if (scheduleTimer) {
          clearTimeout(scheduleTimer);
          this.scheduleTimers.delete(restaurantId);
        }
      }
    }
  }

  /**
   * Inicializa estado de reprodução para um restaurante
   * @param restaurantId - ID do restaurante
   * @returns Promise<IPlaybackState> - Estado inicial de reprodução
   * @throws PlaybackError - Quando há erro na inicialização
   */
  async initializePlayback(restaurantId: string): Promise<IPlaybackState> {
    try {
      await this.mutex.acquire(`init-${restaurantId}`);

      if (!this.playbackStates.has(restaurantId)) {
        // Validar se restaurante existe
        const restaurant = await this.restaurantRepository.findOne({
          where: { id: restaurantId },
        });

        if (!restaurant) {
          throw new PlaybackError(
            "Restaurante não encontrado",
            "RESTAURANT_NOT_FOUND",
            404,
            true,
            restaurantId
          );
        }

        // Obter playlist ativa atual
        const currentPlaylist = await this.getCurrentActivePlaylist(
          restaurantId
        );
        const nextScheduled = await this.getNextScheduledPlaylist(restaurantId);

        const initialState: IPlaybackState = {
          currentTrack: null,
          isPlaying: false,
          currentTime: 0,
          volume: 70,
          queue: [],
          priorityQueue: [],
          normalQueue: [],
          history: [],
          currentPlaylist,
          nextScheduledPlaylist: nextScheduled,
          transitionMode: PlaybackTransitionMode.AUTOMATIC,
          isTransitioning: false,
          problematicTracks: [],
          lastActivity: new Date(),
        };

        this.playbackStates.set(restaurantId, initialState);

        // Carregar fila inicial e músicas problemáticas
        await Promise.all([
          this.refreshQueue(restaurantId),
          this.loadProblematicTracks(restaurantId),
        ]);

        logger.info(
          `Estado de reprodução inicializado para restaurante ${restaurantId}`
        );
      }

      const state = this.playbackStates.get(restaurantId)!;
      state.lastActivity = new Date();

      return state;
    } catch (error) {
      logger.error(
        `Erro ao inicializar reprodução para restaurante ${restaurantId}:`,
        error
      );

      if (error instanceof PlaybackError) {
        throw error;
      }

      throw new PlaybackError(
        `Falha ao inicializar reprodução: ${
          error instanceof Error ? error.message : "Erro desconhecido"
        }`,
        "INITIALIZATION_FAILED",
        500,
        true,
        restaurantId
      );
    } finally {
      this.mutex.release(`init-${restaurantId}`);
    }
  }

  /**
   * Reproduz uma faixa específica
   * @param restaurantId ID do restaurante
   * @param trackId ID da faixa a ser reproduzida
   */
  async playTrack(restaurantId: string, trackId: string): Promise<void> {
    try {
      await this.mutex.acquire(`play-${restaurantId}`);

      // Inicializar estado se necessário
      await this.initializePlayback(restaurantId);
      const state = this.playbackStates.get(restaurantId)!;

      // Verificar transição de playlist
      if (state.isTransitioning) {
        logger.warn(
          `Reprodução rejeitada: transição de playlist em andamento para ${restaurantId}`
        );
        throw new PlaybackError(
          "Transição de playlist em andamento",
          "PLAYLIST_TRANSITION_IN_PROGRESS",
          409,
          true,
          restaurantId
        );
      }

      // Buscar sugestão
      const suggestion = await this.suggestionRepository.findOne({
        where: { id: trackId },
      });

      if (!suggestion) {
        throw new PlaybackError(
          "Faixa não encontrada",
          "TRACK_NOT_FOUND",
          404,
          true,
          restaurantId,
          trackId
        );
      }

      // Cancelar timer anterior se existir
      const existingTimer = this.playbackTimers.get(restaurantId);
      if (existingTimer) {
        clearTimeout(existingTimer);
        this.playbackTimers.delete(restaurantId);
      }

      // Se há música tocando, marcar como interrompida
      if (state.currentTrack) {
        await this.markAsSkipped(restaurantId, state.currentTrack);
      }

      // Converter para ITrack
      const track: ITrack = {
        id: suggestion.id,
        title: suggestion.title,
        artist: suggestion.artist || "Artista Desconhecido",
        youtubeVideoId: suggestion.youtubeVideoId,
        duration: suggestion.duration || 180,
        thumbnailUrl: suggestion.thumbnailUrl || "",
        upvotes: suggestion.upvotes || 0,
        downvotes: suggestion.downvotes || 0,
        score: (suggestion.upvotes || 0) - (suggestion.downvotes || 0),
        suggestedBy: suggestion.suggestedBy?.name,
        createdAt: suggestion.createdAt,
      };

      // Atualizar estado
      state.currentTrack = track;
      state.isPlaying = true;
      state.currentTime = 0;
      state.lastActivity = new Date();

      // Refletir início de reprodução na Suggestion (status/playedAt)
      try {
        await this.suggestionRepository
          .createQueryBuilder()
          .update(Suggestion)
          .set({ status: SuggestionStatus.PLAYING, playedAt: new Date() })
          .where("id = :id", { id: trackId })
          .execute();
      } catch (e) {
        logger.warn("Falha ao atualizar Suggestion como PLAYING:", e);
      }

      // Remover da fila se estiver presente
      state.priorityQueue = state.priorityQueue.filter(
        (t) => t.id !== track.id
      );
      state.normalQueue = state.normalQueue.filter((t) => t.id !== track.id);
      state.queue = [...state.priorityQueue, ...state.normalQueue];

      // Agendar fim da música
      this.scheduleTrackEnd(restaurantId, track.duration);

      // Salvar no histórico
      await this.saveToHistory(restaurantId, track);

      // Marcar música em cooldown de 10 minutos
      try {
        const { collaborativePlaylistService } = await import('./CollaborativePlaylistService');
        await collaborativePlaylistService.markSongInCooldown(
          restaurantId,
          track.youtubeVideoId || '',
          10 // 10 minutos
        );
      } catch (error) {
        logger.warn('Erro ao marcar música em cooldown:', error);
        // Não bloquear reprodução por erro de cooldown
      }

      // Notificar via WebSocket
      try {
        await this.wsService?.broadcastToRestaurant(
          restaurantId,
          "playback_started",
          {
            track: track,
            state: state,
          }
        );
      } catch (wsError) {
        logger.warn("Falha ao notificar via WebSocket:", wsError);
      }

      logger.info(
        `Reprodução iniciada: "${track.title}" - ${track.artist} para restaurante ${restaurantId}`
      );
    } catch (error) {
      logger.error(
        `Erro ao reproduzir faixa ${trackId} para restaurante ${restaurantId}:`,
        error
      );

      if (error instanceof PlaybackError) {
        throw error;
      }

      throw new PlaybackError(
        `Falha ao reproduzir faixa: ${
          error instanceof Error ? error.message : "Erro desconhecido"
        }`,
        "PLAYBACK_FAILED",
        500,
        true,
        restaurantId,
        trackId
      );
    } finally {
      this.mutex.release(`play-${restaurantId}`);
    }
  }

  /**
   * Pausa a reprodução atual
   * @param restaurantId ID do restaurante
   */
  async pausePlayback(restaurantId: string): Promise<void> {
    try {
      await this.mutex.acquire(`pause-${restaurantId}`);

      const state = this.playbackStates.get(restaurantId);
      if (!state || !state.isPlaying || !state.currentTrack) {
        logger.warn(
          `Tentativa de pausar reprodução em restaurante sem música ativa: ${restaurantId}`
        );
        return;
      }

      // Cancelar timer
      const timer = this.playbackTimers.get(restaurantId);
      if (timer) {
        clearTimeout(timer);
        this.playbackTimers.delete(restaurantId);
      }

      state.isPlaying = false;
      state.lastActivity = new Date();

      // Notificar clientes
      await this.wsService.broadcastToRestaurant(
        restaurantId,
        "playback_paused",
        {
          state: {
            currentTrack: state.currentTrack,
            currentTime: state.currentTime,
            isPlaying: false,
          },
        }
      );

      logger.info(`Reprodução pausada para restaurante ${restaurantId}`);
    } catch (error) {
      logger.error(
        `Erro ao pausar reprodução para restaurante ${restaurantId}:`,
        error
      );
      throw new PlaybackError(
        `Falha ao pausar reprodução: ${
          error instanceof Error ? error.message : "Erro desconhecido"
        }`,
        "PAUSE_FAILED",
        500,
        true,
        restaurantId
      );
    } finally {
      this.mutex.release(`pause-${restaurantId}`);
    }
  }

  /**
   * Retoma a reprodução pausada
   * @param restaurantId ID do restaurante
   */
  async resumePlayback(restaurantId: string): Promise<void> {
    try {
      await this.mutex.acquire(`resume-${restaurantId}`);

      const state = this.playbackStates.get(restaurantId);
      if (!state || state.isPlaying || !state.currentTrack) {
        logger.warn(
          `Tentativa de retomar reprodução em estado inválido para restaurante ${restaurantId}`
        );
        return;
      }

      state.isPlaying = true;
      state.lastActivity = new Date();

      // Calcular tempo restante
      const remainingTime = state.currentTrack.duration - state.currentTime;
      if (remainingTime > 0) {
        this.scheduleTrackEnd(restaurantId, remainingTime);
      } else {
        // Se não sobrou tempo, finalize a música
        this.onTrackEnded(restaurantId);
        return;
      }

      // Notificar clientes
      await this.wsService.broadcastToRestaurant(
        restaurantId,
        "playback_resumed",
        {
          state: {
            currentTrack: state.currentTrack,
            currentTime: state.currentTime,
            isPlaying: true,
          },
        }
      );

      logger.info(`Reprodução retomada para restaurante ${restaurantId}`);
    } catch (error) {
      logger.error(
        `Erro ao retomar reprodução para restaurante ${restaurantId}:`,
        error
      );
      throw new PlaybackError(
        `Falha ao retomar reprodução: ${
          error instanceof Error ? error.message : "Erro desconhecido"
        }`,
        "RESUME_FAILED",
        500,
        true,
        restaurantId
      );
    } finally {
      this.mutex.release(`resume-${restaurantId}`);
    }
  }

  /**
   * Pula para a próxima faixa
   * @param restaurantId ID do restaurante
   */
  async skipToNext(restaurantId: string): Promise<void> {
    try {
      await this.mutex.acquire(`skip-${restaurantId}`);

      const state = this.playbackStates.get(restaurantId);
      if (!state) {
        logger.warn(
          `Tentativa de pular faixa em restaurante sem estado: ${restaurantId}`
        );
        return;
      }

      // Cancelar timer atual
      const timer = this.playbackTimers.get(restaurantId);
      if (timer) {
        clearTimeout(timer);
        this.playbackTimers.delete(restaurantId);
      }

      // Marcar como pulada no histórico
      if (state.currentTrack) {
        await this.markAsSkipped(restaurantId, state.currentTrack);

        // Adicionar ao histórico local
        state.history.unshift(state.currentTrack);

        // Manter apenas últimas 50 músicas no histórico
        if (state.history.length > 50) {
          state.history = state.history.slice(0, 50);
        }
      }

      // Parar reprodução atual
      state.isPlaying = false;
      state.currentTrack = null;
      state.currentTime = 0;
      state.lastActivity = new Date();

      // Notificar clientes
      await this.wsService.broadcastToRestaurant(
        restaurantId,
        "track_skipped",
        {
          message: "Música pulada manualmente",
          nextAction: "loading_next_track",
        }
      );

      logger.info(`Faixa pulada para restaurante ${restaurantId}`);

      // Iniciar próxima música com um pequeno intervalo
      setTimeout(() => {
        if (this.isShuttingDown) return;
        this.startNextTrack(restaurantId);
      }, 1000);
    } catch (error) {
      logger.error(
        `Erro ao pular faixa para restaurante ${restaurantId}:`,
        error
      );
      throw new PlaybackError(
        `Falha ao pular faixa: ${
          error instanceof Error ? error.message : "Erro desconhecido"
        }`,
        "SKIP_FAILED",
        500,
        true,
        restaurantId
      );
    } finally {
      this.mutex.release(`skip-${restaurantId}`);
    }
  }

  /**
   * Inicia a próxima faixa da fila
   * @param restaurantId ID do restaurante
   */
  async startNextTrack(restaurantId: string): Promise<void> {
    try {
      const nextTrack = await this.getNextTrack(restaurantId);
      if (nextTrack) {
        await this.playTrack(restaurantId, nextTrack.id);
      } else {
        // Se não há próxima faixa, verificar se há transição de playlist pendente
        const state = this.playbackStates.get(restaurantId);
        if (state?.currentPlaylist) {
          // Tentar atualizar a fila antes de desistir
          await this.refreshQueue(restaurantId);

          // Verificar novamente após atualização da fila
          const updatedNextTrack = await this.getNextTrack(restaurantId);
          if (updatedNextTrack) {
            await this.playTrack(restaurantId, updatedNextTrack.id);
          } else {
            logger.warn(
              `Nenhuma faixa disponível para reprodução no restaurante ${restaurantId}`
            );

            // Notificar clientes
            await this.wsService.broadcastToRestaurant(
              restaurantId,
              "queue_empty",
              {
                message: "Fila de reprodução vazia",
                suggestAction: "add_tracks",
              }
            );
          }
        }
      }
    } catch (error) {
      logger.error(
        `Erro ao iniciar próxima faixa para restaurante ${restaurantId}:`,
        error
      );
      // Não lançamos erro aqui para evitar interrupção do fluxo de reprodução
    }
  }

  /**
   * Agendar fim da música atual
   * @private
   */
  private scheduleTrackEnd(restaurantId: string, duration: number): void {
    // Garantir que a duração é um número positivo
    const safeDuration = Math.max(1, Math.min(duration || 180, 600)); // Entre 1s e 10min

    // Cancelar timer anterior se existir
    const existingTimer = this.playbackTimers.get(restaurantId);
    if (existingTimer) {
      clearTimeout(existingTimer);
    }

    const timer = setTimeout(() => {
      this.onTrackEnded(restaurantId);
    }, safeDuration * 1000);

    this.playbackTimers.set(restaurantId, timer);
    logger.debug(
      `Fim de faixa agendado em ${safeDuration}s para restaurante ${restaurantId}`
    );
  }

  /**
   * Manipulador para quando uma música termina
   * @private
   */
  private async onTrackEnded(restaurantId: string): Promise<void> {
    try {
      await this.mutex.acquire(`track-end-${restaurantId}`);

      const state = this.playbackStates.get(restaurantId);
      if (!state || !state.currentTrack) {
        logger.warn(
          `onTrackEnded chamado sem faixa atual para restaurante ${restaurantId}`
        );
        return;
      }

      logger.info(
        `Faixa terminou: "${state.currentTrack.title}" para restaurante ${restaurantId}`
      );

      // Marcar como completada no histórico (já zera os votos internamente)
      await this.markAsCompleted(restaurantId, state.currentTrack);

      // Adicionar ao histórico local
      state.history.unshift(state.currentTrack);

      // Manter apenas últimas 50 músicas no histórico
      if (state.history.length > 50) {
        state.history = state.history.slice(0, 50);
      }

      // Verificar transição de playlist pendente
      const pendingTransition = state.isTransitioning;

      // Limpar estado atual
      state.currentTrack = null;
      state.isPlaying = false;
      state.currentTime = 0;
      state.lastActivity = new Date();

      // Limpar timer
      this.playbackTimers.delete(restaurantId);

      // Notificar fim da música
      try {
        await this.wsService?.broadcastToRestaurant(restaurantId, "track_ended", {
          state: {
            isPlaying: false,
            currentTrack: null,
            queueSize: state.priorityQueue.length + state.normalQueue.length,
          },
        });
      } catch (wsError) {
        logger.warn("Falha ao notificar fim da música via WebSocket:", wsError);
      }

      // Se há transição pendente, executá-la antes de iniciar próxima música
      if (pendingTransition && state.currentPlaylist) {
        await this.executePlaylistTransition(
          restaurantId,
          state.currentPlaylist.id
        );
      } else {
        // Iniciar próxima música automaticamente com um pequeno intervalo
        setTimeout(() => {
          if (this.isShuttingDown) return;
          this.startNextTrack(restaurantId);
        }, 1000);
      }
    } catch (error) {
      logger.error(
        `Erro ao processar fim de faixa para restaurante ${restaurantId}:`,
        error
      );
    } finally {
      this.mutex.release(`track-end-${restaurantId}`);
    }
  }

  /**
   * Força o término imediato da faixa atual (idempotente) e inicia a próxima.
   * Útil para integrar confirmações de clients (/song-ended) com o estado autoritativo.
   */
  public async forceEndCurrentTrack(restaurantId: string): Promise<void> {
    try {
      // Se não houver estado/track, não faz nada
      const state = this.playbackStates.get(restaurantId);
      if (!state || !state.currentTrack) return;

      // Cancelar timer pendente e acionar o mesmo fluxo de término
      const existingTimer = this.playbackTimers.get(restaurantId);
      if (existingTimer) {
        clearTimeout(existingTimer);
        this.playbackTimers.delete(restaurantId);
      }
      await this.onTrackEnded(restaurantId);
    } catch (e) {
      logger.warn(`forceEndCurrentTrack falhou para ${restaurantId}:`, e);
    }
  }

  /**
   * Atualiza fila de reprodução com sugestões aprovadas
   * @private
   */
  private async refreshQueue(restaurantId: string): Promise<void> {
    try {
      const state = this.playbackStates.get(restaurantId);
      if (!state) return;

      // Buscar playlist ativa; se não houver, manter filas vazias
      const active = await collaborativePlaylistService.getActivePlaylistForRestaurant(
        restaurantId
      );
      if (!active || !Array.isArray((active as any).tracks)) {
        state.priorityQueue = [];
        state.normalQueue = [];
        state.queue = [];
        logger.warn(
          `Sem playlist ativa para ${restaurantId}; fila vazia até definir playlist.`
        );
        return;
      }

      // Base: todas as faixas da playlist ativa (ordem original)
      const tracks = ((active as any).tracks || []) as Array<{
        youtubeVideoId: string;
        title?: string;
        artist?: string;
        duration?: number;
        thumbnailUrl?: string;
        position?: number;
      }>;

      const restaurant = await this.restaurantRepository.findOne({
        where: { id: restaurantId },
      });
      if (!restaurant) {
        state.priorityQueue = [];
        state.normalQueue = [];
        state.queue = [];
        logger.warn(
          `Restaurante não encontrado (${restaurantId}) ao montar fila da playlist.`
        );
        return;
      }

  // Preparar lista de IDs de vídeo presentes na playlist ativa
  const videoIds = tracks.map((t) => t.youtubeVideoId).filter(Boolean);

  // CORREÇÃO: Resetar sugestões completed antigas para approved
  if (videoIds.length > 0) {
        await this.suggestionRepository
          .createQueryBuilder()
          .update()
          .set({ status: SuggestionStatus.APPROVED })
          .where("restaurant_id = :restaurantId", { restaurantId })
          .andWhere("status = :completedStatus", { completedStatus: "completed" })
          .andWhere("youtube_video_id IN (:...ids)", { ids: videoIds })
          .andWhere("completed_at < NOW() - INTERVAL '5 minutes'")
          .execute();
      }

      // Buscar sugestões de hoje para faixas da playlist (approved, playing, completed)
      const suggestionsToday = videoIds.length
        ? await this.suggestionRepository
            .createQueryBuilder("s")
            .leftJoin("s.restaurant", "r")
            .where("r.id = :restaurantId", { restaurantId })
            .andWhere("s.status IN (:...statuses)", { statuses: ["approved", "playing", "completed"] })
            .andWhere("DATE(s.createdAt) = CURRENT_DATE")
            .andWhere("s.youtubeVideoId IN (:...ids)", { ids: videoIds })
            .getMany()
        : [];

      // Buscar músicas que já foram tocadas hoje para excluir da fila
      const playedToday = videoIds.length
        ? await this.suggestionRepository
            .createQueryBuilder("s")
            .leftJoin("s.restaurant", "r")
            .where("r.id = :restaurantId", { restaurantId })
            .andWhere("s.status IN (:...statuses)", { statuses: ["played", "completed", "skipped"] })
            .andWhere("DATE(s.createdAt) = CURRENT_DATE")
            .andWhere("s.youtubeVideoId IN (:...ids)", { ids: videoIds })
            .getMany()
        : [];

      // Criar set de vídeos já tocados para filtrar
      const playedVideoIds = new Set(playedToday.map(s => s.youtubeVideoId));

      // Filtrar tracks que já foram tocadas
      const availableTracks = tracks.filter(t => !playedVideoIds.has(t.youtubeVideoId));

      // Agrupar por vídeo e escolher a sugestão "representante" por vídeo
      const groupedByVideo = new Map<string, Suggestion[]>();
      for (const s of suggestionsToday) {
        const arr = groupedByVideo.get(s.youtubeVideoId) || [];
        arr.push(s);
        groupedByVideo.set(s.youtubeVideoId, arr);
      }

      const pickRepresentative = (list: Suggestion[]): Suggestion => {
        // 1) Preferir paga
        const paid = list.filter((x) => !!x.isPaid);
        if (paid.length > 0) {
          // entre pagas, maior upvote primeiro; empate por mais recente
          paid.sort((a, b) => (b.upvotes || 0) - (a.upvotes || 0) || (b.createdAt?.getTime?.() || 0) - (a.createdAt?.getTime?.() || 0));
          return paid[0];
        }
        // 2) Senão, maior score (up - down); empate por mais recente
        const sorted = [...list].sort(
          (a, b) =>
            ((b.upvotes || 0) - (b.downvotes || 0)) - ((a.upvotes || 0) - (a.downvotes || 0)) ||
            (b.createdAt?.getTime?.() || 0) - (a.createdAt?.getTime?.() || 0)
        );
        return sorted[0];
      };

      const byVideo = new Map<string, Suggestion>();
      for (const [vid, list] of groupedByVideo) {
        byVideo.set(vid, pickRepresentative(list));
      }

      // Garantir que existe uma Suggestion (AUTO) para cada faixa disponível da playlist
      const ensuredSuggestions: Suggestion[] = [];
      for (const t of availableTracks) {
        let s = byVideo.get(t.youtubeVideoId);
        if (!s) {
          s = this.suggestionRepository.create({
            youtubeVideoId: t.youtubeVideoId,
            title: t.title || `Vídeo ${t.youtubeVideoId}`,
            artist: t.artist || "",
            duration: t.duration || 0,
            thumbnailUrl:
              t.thumbnailUrl ||
              `https://i.ytimg.com/vi/${t.youtubeVideoId}/mqdefault.jpg`,
            restaurant,
            status: SuggestionStatus.APPROVED,
            isPaid: false,
            voteCount: 0,
            upvotes: 0,
            downvotes: 0,
            source: "auto" as any,
          });
          try {
            s = await this.suggestionRepository.save(s);
          } catch (e) {
            // Em concorrência, tentar novamente buscando do banco
            const retry = await this.suggestionRepository
              .createQueryBuilder("s")
              .leftJoin("s.restaurant", "r")
              .where("r.id = :restaurantId", { restaurantId })
              .andWhere("s.status IN (:...statuses)", { statuses: ["approved", "playing", "completed"] })
              .andWhere("s.youtubeVideoId = :vid", { vid: t.youtubeVideoId })
              .getOne();
            if (retry) s = retry;
          }
          byVideo.set(t.youtubeVideoId, s);
        }
        ensuredSuggestions.push(s);
      }

      // Calcular pesos de votos do dia por VÍDEO (somando duplicadas)
      const weightByVideo = new Map<string, number>();
      for (const [vid, list] of groupedByVideo) {
        let up = 0;
        let down = 0;
        for (const s of list) {
          up += Number(s.upvotes || 0);
          down += Number(s.downvotes || 0);
        }
        weightByVideo.set(vid, up - down);
      }

      // Mapear para ITrack preservando ordem original da playlist quando peso igual/ausente
      const baseTracks: Array<ITrack & { __origIndex: number }> = availableTracks.map(
        (t, idx) => {
          const s = byVideo.get(t.youtubeVideoId)!; // representativo do vídeo
          return {
            id: s.id,
            title: s.title || t.title || `Vídeo ${t.youtubeVideoId}`,
            artist: s.artist || t.artist || "Artista Desconhecido",
            youtubeVideoId: t.youtubeVideoId,
            duration: (s.duration as any) || t.duration || 180,
            thumbnailUrl: s.thumbnailUrl || t.thumbnailUrl || "",
            upvotes: s.upvotes || 0,
            downvotes: s.downvotes || 0,
            score: (s.upvotes || 0) - (s.downvotes || 0),
            suggestedBy: s.suggestedBy?.name,
            createdAt: s.createdAt,
            __origIndex: idx,
          } as any;
        }
      );

      // Ordenar: maior peso primeiro; em empate, manter ordem da playlist
      baseTracks.sort((a, b) => {
  const aw = weightByVideo.get(a.youtubeVideoId) ?? 0;
  const bw = weightByVideo.get(b.youtubeVideoId) ?? 0;
        if (aw !== bw) return bw - aw;
        return a.__origIndex - b.__origIndex;
      });

      // Atualizar filas: usamos apenas normalQueue (sem pista prioritária separada)
      state.priorityQueue = [];
      state.normalQueue = baseTracks.map(({ __origIndex, ...t }) => t);
      state.queue = [...state.normalQueue];

      logger.info(
        `Fila atualizada por playlist para ${restaurantId}: ${state.normalQueue.length} faixas disponíveis (${playedVideoIds.size} já tocadas hoje, reordenadas por votos)`
      );
    } catch (error) {
      logger.error(
        `Erro ao atualizar fila do restaurante ${restaurantId}:`,
        error
      );
    }
  }

  /**
   * Recalcula a fila atual baseada nos votos mais recentes.
   * Não interrompe a música atual; apenas reordena o restante da fila.
   */
  public async recomputeQueue(
    restaurantId: string,
    options?: { broadcast?: boolean }
  ): Promise<void> {
    const { broadcast = true } = options || {};
    try {
      await this.mutex.acquire(`queue-${restaurantId}`);
      const state = this.playbackStates.get(restaurantId);
      if (!state) {
        await this.initializePlayback(restaurantId);
        return;
      }

      // Se a fila está vazia, recarregar da playlist
      if (state.normalQueue.length === 0 && state.priorityQueue.length === 0) {
        await this.refreshQueue(restaurantId);
      } else {
        // Apenas reordenar a fila existente baseada nos votos atuais
        await this.reorderExistingQueue(restaurantId);
      }

      if (broadcast) {
        const st = this.playbackStates.get(restaurantId);
        await this.wsService.broadcastToRestaurant(
          restaurantId,
          "queue_updated",
          {
            queue: st ? st.queue : [],
            message: "Fila reordenada por votos",
          }
        );
      }
    } catch (error) {
      logger.warn(
        `Falha ao recomputar fila para restaurante ${restaurantId}:`,
        error
      );
    } finally {
      this.mutex.release(`queue-${restaurantId}`);
    }
  }

  /**
   * Reordena a fila existente baseada nos votos atuais sem recarregar da playlist
   * @private
   */
  private async reorderExistingQueue(restaurantId: string): Promise<void> {
    try {
      const state = this.playbackStates.get(restaurantId);
      if (!state || state.normalQueue.length === 0) return;

      // Buscar votos atuais para as músicas na fila
      const videoIds = state.normalQueue.map(t => (t as any).youtubeVideoId).filter(Boolean);
      if (videoIds.length === 0) return;

      const suggestionsToday = await this.suggestionRepository
        .createQueryBuilder("s")
        .leftJoin("s.restaurant", "r")
        .where("r.id = :restaurantId", { restaurantId })
        .andWhere("s.status IN (:...statuses)", { statuses: ["approved", "playing"] })
        .andWhere("DATE(s.createdAt) = CURRENT_DATE")
        .andWhere("s.youtubeVideoId IN (:...ids)", { ids: videoIds })
        .getMany();

      // Calcular pesos de votos por vídeo
      const weightByVideo = new Map<string, number>();
      const groupedByVideo = new Map<string, Suggestion[]>();

      for (const s of suggestionsToday) {
        const arr = groupedByVideo.get(s.youtubeVideoId) || [];
        arr.push(s);
        groupedByVideo.set(s.youtubeVideoId, arr);
      }

      for (const [vid, list] of groupedByVideo) {
        let up = 0;
        let down = 0;
        for (const s of list) {
          up += Number(s.upvotes || 0);
          down += Number(s.downvotes || 0);
        }
        weightByVideo.set(vid, up - down);
      }

      // Reordenar fila existente baseada nos pesos
      const tracksWithWeight = state.normalQueue.map((track, idx) => ({
        track,
        weight: weightByVideo.get((track as any).youtubeVideoId) || 0,
        originalIndex: idx
      }));

      tracksWithWeight.sort((a, b) => {
        if (a.weight !== b.weight) return b.weight - a.weight;
        return a.originalIndex - b.originalIndex;
      });

      // Atualizar fila
      state.normalQueue = tracksWithWeight.map(item => item.track);
      state.queue = [...state.priorityQueue, ...state.normalQueue];

      logger.info(
        `Fila reordenada por votos para ${restaurantId}: ${state.normalQueue.length} faixas`
      );
    } catch (error) {
      logger.error(
        `Erro ao reordenar fila existente para restaurante ${restaurantId}:`,
        error
      );
    }
  }

  /**
   * Salva música no histórico de reprodução
   * @private
   */
  private async saveToHistory(
    restaurantId: string,
    track: ITrack
  ): Promise<void> {
    try {
      const restaurant = await this.restaurantRepository.findOne({
        where: { id: restaurantId },
      });

      if (!restaurant) {
        logger.warn(
          `Tentativa de salvar histórico para restaurante inexistente: ${restaurantId}`
        );
        return;
      }

      const historyEntry = this.playHistoryRepository.create({
        restaurant,
        youtubeVideoId: track.youtubeVideoId,
        title: track.title,
        artist: track.artist,
        duration: track.duration,
        playedAt: new Date(),
        playDuration: 0, // Será atualizado quando terminar
        status: PlayStatus.PLAYING,
        metadata: {
          source: "playlist",
          volume: 100,
        },
      });

      await this.playHistoryRepository.save(historyEntry);
      logger.debug(
        `Entrada de histórico criada para "${track.title}" no restaurante ${restaurantId}`
      );
    } catch (error) {
      logger.error(
        `Erro ao salvar no histórico para restaurante ${restaurantId}:`,
        error
      );
    }
  }

  /**
   * Marca música como completada no histórico
   * @private
   */
  private async markAsCompleted(
    restaurantId: string,
    track: ITrack
  ): Promise<void> {
    try {
      // Buscar o registro mais recente primeiro
      const playHistoryRecord = await this.playHistoryRepository
        .createQueryBuilder("ph")
        .leftJoin("ph.restaurant", "r")
        .where("r.id = :restaurantId", { restaurantId })
        .andWhere("ph.youtubeVideoId = :videoId", {
          videoId: track.youtubeVideoId,
        })
        .andWhere("ph.status = :status", { status: "playing" })
        .orderBy("ph.playedAt", "DESC")
        .getOne();

      if (playHistoryRecord) {
        await this.playHistoryRepository
          .createQueryBuilder()
          .update(PlayHistory)
          .set({
            status: PlayStatus.COMPLETED,
            playDuration: track.duration,
          })
          .where("id = :id", { id: playHistoryRecord.id })
          .execute();
      }

      logger.debug(
        `Faixa "${track.title}" marcada como completada para restaurante ${restaurantId}`
      );

      // ZERAR VOTOS DE TODAS AS SUGESTÕES DESTA MÚSICA
      try {
        console.log(`🎵 INICIANDO ZERAGEM DE VOTOS para: ${track.title} (${track.youtubeVideoId})`);

        // ESTRATÉGIA ROBUSTA: Buscar por youtubeVideoId E título similar
        const updateResult = await this.suggestionRepository
          .createQueryBuilder()
          .update(Suggestion)
          .set({
            status: SuggestionStatus.COMPLETED,
            completedAt: new Date(),
            upvotes: 0,      // ZERAR VOTOS - Música tocou, votos foram "consumidos"
            downvotes: 0     // ZERAR VOTOS - Evita música ficar sempre no topo
          })
          .where("youtubeVideoId = :vid", { vid: track.youtubeVideoId })
          .andWhere("restaurantId = :restaurantId", { restaurantId })
          .andWhere("status IN (:...statuses)", {
            statuses: [SuggestionStatus.APPROVED, SuggestionStatus.PLAYING, SuggestionStatus.COMPLETED]
          })
          .execute();

        console.log(`✅ VOTOS ZERADOS: ${updateResult.affected} sugestões atualizadas para ${track.title}`);

        // FALLBACK: Se não encontrou por youtubeVideoId, tentar por título
        if (updateResult.affected === 0) {
          console.log(`🔄 FALLBACK: Tentando por título similar para ${track.title}`);

          const fallbackResult = await this.suggestionRepository
            .createQueryBuilder()
            .update(Suggestion)
            .set({
              status: SuggestionStatus.COMPLETED,
              completedAt: new Date(),
              upvotes: 0,
              downvotes: 0
            })
            .where("restaurantId = :restaurantId", { restaurantId })
            .andWhere("LOWER(title) LIKE LOWER(:title)", { title: `%${track.title.split('(')[0].trim()}%` })
            .andWhere("status IN (:...statuses)", {
              statuses: [SuggestionStatus.APPROVED, SuggestionStatus.PLAYING]
            })
            .execute();

          console.log(`✅ FALLBACK RESULT: ${fallbackResult.affected} sugestões atualizadas por título`);
        }

      } catch (e) {
        console.error("❌ ERRO ao zerar votos:", e);
        logger.warn("Falha ao zerar votos da suggestion:", e);
      }
    } catch (error) {
      logger.error(
        `Erro ao marcar faixa como completada para restaurante ${restaurantId}:`,
        error
      );
    }
  }

  /**
   * Marca música como pulada no histórico
   * @private
   */
  private async markAsSkipped(
    restaurantId: string,
    track: ITrack
  ): Promise<void> {
    try {
      const state = this.playbackStates.get(restaurantId);

      // Buscar o registro mais recente primeiro
      const playHistoryRecord = await this.playHistoryRepository
        .createQueryBuilder("ph")
        .leftJoin("ph.restaurant", "r")
        .where("r.id = :restaurantId", { restaurantId })
        .andWhere("ph.youtubeVideoId = :videoId", {
          videoId: track.youtubeVideoId,
        })
        .andWhere("ph.status = :status", { status: "playing" })
        .orderBy("ph.playedAt", "DESC")
        .getOne();

      if (playHistoryRecord) {
        await this.playHistoryRepository
          .createQueryBuilder()
          .update(PlayHistory)
          .set({
            status: PlayStatus.SKIPPED,
            playDuration: state?.currentTime || 0,
            skipReason: "Manual skip by admin",
          })
          .where("id = :id", { id: playHistoryRecord.id })
          .execute();
      }

      logger.debug(
        `Faixa "${track.title}" marcada como pulada para restaurante ${restaurantId}`
      );

      // Refletir na Suggestion (SKIPPED)
      try {
        await this.suggestionRepository
          .createQueryBuilder()
          .update(Suggestion)
          .set({ status: SuggestionStatus.SKIPPED })
          .where("id = :id", { id: track.id })
          .execute();
      } catch (e) {
        logger.warn("Falha ao atualizar Suggestion como SKIPPED:", e);
      }
    } catch (error) {
      logger.error(
        `Erro ao marcar faixa como pulada para restaurante ${restaurantId}:`,
        error
      );
    }
  }

  /**
   * Ajusta o volume de reprodução
   * @param restaurantId ID do restaurante
   * @param volume Nível de volume (0-100)
   */
  async setVolume(restaurantId: string, volume: number): Promise<void> {
    try {
      await this.validateInput(VolumeControlDto, { restaurantId, volume });

      const state = this.playbackStates.get(restaurantId);
      if (!state) {
        await this.initializePlayback(restaurantId);
      }

      const safeVolume = Math.max(0, Math.min(100, volume));
      const updatedState = this.playbackStates.get(restaurantId)!;
      updatedState.volume = safeVolume;
      updatedState.lastActivity = new Date();

      await this.wsService.broadcastToRestaurant(
        restaurantId,
        "volume_changed",
        {
          volume: safeVolume,
        }
      );

      logger.info(
        `Volume ajustado para ${safeVolume} no restaurante ${restaurantId}`
      );
    } catch (error) {
      logger.error(
        `Erro ao ajustar volume para restaurante ${restaurantId}:`,
        error
      );

      if (error instanceof PlaybackError) {
        throw error;
      }

      throw new PlaybackError(
        `Falha ao ajustar volume: ${
          error instanceof Error ? error.message : "Erro desconhecido"
        }`,
        "VOLUME_CONTROL_FAILED",
        500,
        true,
        restaurantId
      );
    }
  }

  /**
   * Adiciona música à fila
   * @param restaurantId ID do restaurante
   * @param track Faixa a ser adicionada
   * @param isPriority Se deve ser adicionada à fila prioritária
   */
  async addToQueue(
    restaurantId: string,
    track: ITrack,
    isPriority: boolean = false
  ): Promise<void> {
    try {
      await this.mutex.acquire(`queue-${restaurantId}`);

      const state = this.playbackStates.get(restaurantId);
      if (!state) {
        await this.initializePlayback(restaurantId);
      }

      const updatedState = this.playbackStates.get(restaurantId)!;

      if (isPriority) {
        updatedState.priorityQueue.push(track);
      } else {
        updatedState.normalQueue.push(track);
      }

      // Atualizar fila combinada
      updatedState.queue = [
        ...updatedState.priorityQueue,
        ...updatedState.normalQueue,
      ];
      updatedState.lastActivity = new Date();

      await this.wsService.broadcastToRestaurant(
        restaurantId,
        "queue_updated",
        {
          queue: updatedState.queue,
          added: track,
          message: `"${track.title}" adicionada à fila${
            isPriority ? " prioritária" : ""
          }`,
        }
      );

      logger.info(
        `"${track.title}" adicionada à fila${
          isPriority ? " prioritária" : ""
        } do restaurante ${restaurantId}`
      );

      // Iniciar reprodução se nada estiver tocando
      if (!updatedState.isPlaying && !updatedState.currentTrack) {
        setTimeout(() => {
          if (this.isShuttingDown) return;
          this.startNextTrack(restaurantId);
        }, 1000);
      }
    } catch (error) {
      logger.error(
        `Erro ao adicionar faixa à fila do restaurante ${restaurantId}:`,
        error
      );
      throw new PlaybackError(
        `Falha ao adicionar à fila: ${
          error instanceof Error ? error.message : "Erro desconhecido"
        }`,
        "QUEUE_ADD_FAILED",
        500,
        true,
        restaurantId
      );
    } finally {
      this.mutex.release(`queue-${restaurantId}`);
    }
  }

  /**
   * Remove música da fila
   * @param restaurantId ID do restaurante
   * @param trackId ID da faixa a ser removida
   */
  async removeFromQueue(restaurantId: string, trackId: string): Promise<void> {
    try {
      await this.mutex.acquire(`queue-${restaurantId}`);

      const state = this.playbackStates.get(restaurantId);
      if (!state) {
        logger.warn(
          `Tentativa de remover da fila em restaurante sem estado: ${restaurantId}`
        );
        return;
      }

      // Buscar detalhes da faixa antes de remover (para logging)
      const trackInPriority = state.priorityQueue.find((t) => t.id === trackId);
      const trackInNormal = state.normalQueue.find((t) => t.id === trackId);
      const track = trackInPriority || trackInNormal;

      // Remover da fila apropriada
      if (trackInPriority) {
        state.priorityQueue = state.priorityQueue.filter(
          (t) => t.id !== trackId
        );
      }

      if (trackInNormal) {
        state.normalQueue = state.normalQueue.filter((t) => t.id !== trackId);
      }

      // Atualizar fila combinada
      state.queue = [...state.priorityQueue, ...state.normalQueue];
      state.lastActivity = new Date();

      // Notificar clientes
      await this.wsService.broadcastToRestaurant(
        restaurantId,
        "queue_updated",
        {
          queue: state.queue,
          removed: trackId,
          message: track
            ? `"${track.title}" removida da fila`
            : `Faixa removida da fila`,
        }
      );

      logger.info(
        `Faixa ${
          track ? `"${track.title}"` : trackId
        } removida da fila do restaurante ${restaurantId}`
      );
    } catch (error) {
      logger.error(
        `Erro ao remover faixa da fila do restaurante ${restaurantId}:`,
        error
      );
      throw new PlaybackError(
        `Falha ao remover da fila: ${
          error instanceof Error ? error.message : "Erro desconhecido"
        }`,
        "QUEUE_REMOVE_FAILED",
        500,
        true,
        restaurantId,
        trackId
      );
    } finally {
      this.mutex.release(`queue-${restaurantId}`);
    }
  }

  /**
   * Obtém próxima música da fila
   * @param restaurantId ID do restaurante
   * @returns Próxima faixa ou null se a fila estiver vazia
   */
  async getNextTrack(restaurantId: string): Promise<ITrack | null> {
    try {
  if (this.isShuttingDown) return null;
      await this.mutex.acquire(`next-track-${restaurantId}`);

      const state = this.playbackStates.get(restaurantId);
      if (!state) {
        await this.initializePlayback(restaurantId);
      }

      const updatedState = this.playbackStates.get(restaurantId)!;

      // Atualizar fila se estiver vazia
      if (
        updatedState.priorityQueue.length === 0 &&
        updatedState.normalQueue.length === 0
      ) {
        await this.refreshQueue(restaurantId);
      }

      // Filtrar músicas problemáticas
      const availablePriorityTracks = updatedState.priorityQueue.filter(
        (track) => !updatedState.problematicTracks.includes(track.id)
      );

      const availableNormalTracks = updatedState.normalQueue.filter(
        (track) => !updatedState.problematicTracks.includes(track.id)
      );

      // Seleção: preservar ordem já calculada em refreshQueue (que considera votos)
      const combinedAvailable = [
        ...availableNormalTracks, // sem fila prioritária separada
      ];
      // Respeitar cooldown autoritativo (10 min) — filtrar músicas em cooldown
      let filteredAvailable: ITrack[] = [];
      if (combinedAvailable.length > 0) {
        try {
          for (const t of combinedAvailable) {
            const yid = (t as any).youtubeVideoId || "";
            if (!yid) {
              filteredAvailable.push(t);
              continue;
            }
            const inCooldown = await (collaborativePlaylistService as any).isSongInCooldown(
              restaurantId,
              yid
            );
            if (!inCooldown) filteredAvailable.push(t);
          }
        } catch (e) {
          // Em caso de erro no Redis, seguir com a lista original para não travar reprodução
          filteredAvailable = combinedAvailable;
        }
      }

      if (filteredAvailable.length > 0) {
        return filteredAvailable[0];
      }

      // Se todas as músicas são problemáticas ou em cooldown, ainda podemos tentar uma não em cooldown em cada fila
      if (updatedState.priorityQueue.length > 0) {
        logger.warn(
          `Todas as músicas prioritárias são problemáticas para restaurante ${restaurantId}`
        );
        // tentar primeira fora de cooldown
        for (const t of updatedState.priorityQueue) {
          const yid = (t as any).youtubeVideoId || "";
          try {
            if (!yid || !(await (collaborativePlaylistService as any).isSongInCooldown(restaurantId, yid))) {
              return t;
            }
          } catch {
            return t; // em caso de erro, não bloquear
          }
        }
      }

      if (updatedState.normalQueue.length > 0) {
        logger.warn(
          `Todas as músicas normais são problemáticas para restaurante ${restaurantId}`
        );
        for (const t of updatedState.normalQueue) {
          const yid = (t as any).youtubeVideoId || "";
          try {
            if (!yid || !(await (collaborativePlaylistService as any).isSongInCooldown(restaurantId, yid))) {
              return t;
            }
          } catch {
            return t;
          }
        }
      }

      logger.info(
        `Nenhuma faixa disponível na fila do restaurante ${restaurantId}`
      );
      return null;
    } catch (error) {
      logger.error(
        `Erro ao obter próxima faixa para restaurante ${restaurantId}:`,
        error
      );
      return null;
    } finally {
      this.mutex.release(`next-track-${restaurantId}`);
    }
  }

  /**
   * Obtém estado atual de reprodução
   * @param restaurantId ID do restaurante
   */
  async getPlaybackState(restaurantId: string): Promise<IPlaybackState | null> {
    try {
      const state = this.playbackStates.get(restaurantId);
      if (!state) {
        return await this.initializePlayback(restaurantId);
      }

      // Atualizar hora da última atividade
      state.lastActivity = new Date();
      return state;
    } catch (error) {
      logger.error(
        `Erro ao obter estado de reprodução para restaurante ${restaurantId}:`,
        error
      );
      return null;
    }
  }

  /**
   * Atualiza o tempo atual de reprodução
   * @param restaurantId ID do restaurante
   * @param currentTime Tempo atual em segundos
   */
  async updateCurrentTime(
    restaurantId: string,
    currentTime: number
  ): Promise<void> {
    try {
      const state = this.playbackStates.get(restaurantId);
      if (!state || !state.currentTrack) {
        return;
      }

      // Validar tempo
      const safeTime = Math.max(
        0,
        Math.min(state.currentTrack.duration, currentTime)
      );

      state.currentTime = safeTime;
      state.lastActivity = new Date();

      // Não precisamos notificar os clientes em cada atualização de tempo
    } catch (error) {
      logger.error(
        `Erro ao atualizar tempo de reprodução para restaurante ${restaurantId}:`,
        error
      );
    }
  }

  /**
   * Obter playlist ativa atual baseada no agendamento
   * @private
   */
  private async getCurrentActivePlaylist(
    restaurantId: string
  ): Promise<IPlaylist | null> {
    try {
      const schedule = await this.playlistScheduleRepository.findOne({
        where: { restaurantId, isActive: true },
        relations: ["slots", "fallbackPlaylist"],
      });

      if (schedule) {
        // Lógica para determinar a playlist atual com base no horário
        const now = new Date();
        const currentDay = now.getDay(); // 0 = Domingo, 1 = Segunda, etc.
        const currentHour = now.getHours();
        const currentMinute = now.getMinutes();

        // Verificar slots ativos
        const activeSlot = schedule.timeSlots?.find((slot) => {
          const currentTime = `${currentHour
            .toString()
            .padStart(2, "0")}:${currentMinute.toString().padStart(2, "0")}`;
          return (
            slot.days.includes(currentDay) &&
            slot.isActive &&
            currentTime >= slot.startTime &&
            currentTime <= slot.endTime
          );
        });

        if (activeSlot) {
          const playlist = await this.playlistRepository.findOne({
            where: { id: activeSlot.playlistId },
          });

          if (playlist) {
            return {
              id: playlist.id,
              name: playlist.name,
              type: "scheduled",
              description: playlist.description,
            };
          }
        }

        // Fallback playlist não está implementado no modelo atual
      }

      // Buscar playlist ativa do restaurante (priorizar padrão)
      const defaultPlaylist = await this.playlistRepository.findOne({
        where: {
          restaurant: { id: restaurantId },
          status: PlaylistStatus.ACTIVE,
        },
        order: {
          isDefault: "DESC", // Priorizar padrão se existir
          createdAt: "DESC", // Mais recente primeiro
        },
      });

      if (defaultPlaylist) {
        return {
          id: defaultPlaylist.id,
          name: defaultPlaylist.name,
          type: "default",
          description: defaultPlaylist.description,
        };
      }

      return null;
    } catch (error) {
      logger.error(`Erro ao obter playlist ativa para ${restaurantId}:`, error);
      return null;
    }
  }

  /**
   * Obter próxima playlist agendada
   * @private
   */
  private async getNextScheduledPlaylist(
    restaurantId: string
  ): Promise<IScheduledPlaylist | null> {
    try {
      const schedule = await this.playlistScheduleRepository.findOne({
        where: { restaurantId, isActive: true },
        // timeSlots é uma coluna JSON, não precisa de relations
      });

      if (schedule && schedule.timeSlots && schedule.timeSlots.length > 0) {
        const now = new Date();
        const currentDay = now.getDay();
        const currentHour = now.getHours();
        const currentMinute = now.getMinutes();
        const currentTotalMinutes = currentHour * 60 + currentMinute;

        // Encontrar o próximo slot programado
        let nextSlot = null;
        let minWaitTime = Number.MAX_SAFE_INTEGER;

        for (const slot of schedule.timeSlots) {
          // Parse startTime (HH:MM) to minutes
          const [startHour, startMinute] = slot.startTime
            .split(":")
            .map(Number);
          const slotStartTime = startHour * 60 + startMinute;
          let waitTime = 0;

          // Check if slot is active on any of its days
          for (const dayOfWeek of slot.days) {
            if (
              dayOfWeek === currentDay &&
              slotStartTime > currentTotalMinutes
            ) {
              // Mesmo dia, mais tarde
              waitTime = slotStartTime - currentTotalMinutes;
            } else if (dayOfWeek > currentDay) {
              // Dias posteriores nesta semana
              waitTime =
                (dayOfWeek - currentDay) * 24 * 60 +
                slotStartTime -
                currentTotalMinutes;
            } else if (dayOfWeek < currentDay) {
              // Dias da próxima semana
              waitTime =
                (7 - currentDay + dayOfWeek) * 24 * 60 +
                slotStartTime -
                currentTotalMinutes;
            }

            if (waitTime > 0 && waitTime < minWaitTime) {
              minWaitTime = waitTime;
              nextSlot = slot;
              break; // Found a better slot, no need to check other days for this slot
            }
          }
        }

        if (nextSlot) {
          const playlist = await this.playlistRepository.findOne({
            where: { id: nextSlot.playlistId },
          });

          if (playlist) {
            const startsAt = new Date();
            startsAt.setMinutes(startsAt.getMinutes() + minWaitTime);

            return {
              id: playlist.id,
              name: playlist.name,
              startsIn: minWaitTime,
              startsAt,
              priority: nextSlot.priority || 1,
            };
          }
        }
      }

      return null;
    } catch (error) {
      logger.error(
        `Erro ao obter próxima playlist para ${restaurantId}:`,
        error
      );
      return null;
    }
  }

  /**
   * Carregar músicas problemáticas
   * @private
   */
  private async loadProblematicTracks(restaurantId: string): Promise<void> {
    try {
      const state = this.playbackStates.get(restaurantId);
      if (!state) return;

      const worstTracks = await this.analyticsService.getWorstRatedTracks(
        restaurantId
      );
      state.problematicTracks = worstTracks.map((track) => track.id);

      logger.info(
        `${state.problematicTracks.length} músicas problemáticas identificadas para restaurante ${restaurantId}`
      );
    } catch (error) {
      logger.error(
        `Erro ao carregar músicas problemáticas para ${restaurantId}:`,
        error
      );
    }
  }

  /**
   * Verificar agendamentos de playlist
   * @private
   */
  private async checkScheduledPlaylists(): Promise<void> {
    for (const [restaurantId, state] of Array.from(
      this.playbackStates.entries()
    )) {
      try {
        const nextScheduled = await this.getNextScheduledPlaylist(restaurantId);

        // Atualizar estado
        state.nextScheduledPlaylist = nextScheduled;

        if (nextScheduled && nextScheduled.startsIn <= 1) {
          // 1 minuto ou menos
          await this.handlePlaylistTransition(restaurantId, nextScheduled.id);
        }
      } catch (error) {
        logger.error(
          `Erro ao verificar agendamentos para ${restaurantId}:`,
          error
        );
      }
    }
  }

  /**
   * Lidar com transição de playlist
   * @private
   */
  private async handlePlaylistTransition(
    restaurantId: string,
    newPlaylistId: string
  ): Promise<void> {
    try {
      await this.mutex.acquire(`transition-${restaurantId}`);

      const state = this.playbackStates.get(restaurantId);
      if (!state) return;

      logger.info(
        `Transição de playlist iniciada para restaurante ${restaurantId}`
      );
      state.isTransitioning = true;

      // Se há música tocando, aguardar terminar
      if (state.isPlaying && state.currentTrack) {
        logger.info(
          `Aguardando música atual terminar para transição em ${restaurantId}`
        );

        // Notificar sobre transição pendente
        await this.wsService.broadcastToRestaurant(
          restaurantId,
          "playlist_transition_pending",
          {
            currentTrack: state.currentTrack,
            newPlaylistId,
            message: "Transição de playlist será feita após a música atual",
          }
        );
      } else {
        // Fazer transição imediatamente se não há música tocando
        await this.executePlaylistTransition(restaurantId, newPlaylistId);
      }
    } catch (error) {
      logger.error(
        `Erro na transição de playlist para ${restaurantId}:`,
        error
      );
    } finally {
      this.mutex.release(`transition-${restaurantId}`);
    }
  }

  /**
   * Executar transição de playlist
   * @private
   */
  private async executePlaylistTransition(
    restaurantId: string,
    newPlaylistId: string
  ): Promise<void> {
    try {
      const state = this.playbackStates.get(restaurantId);
      if (!state) return;

      // Atualizar playlist atual
      const newPlaylist = await this.playlistRepository.findOne({
        where: { id: newPlaylistId },
      });

      if (newPlaylist) {
        state.currentPlaylist = {
          id: newPlaylist.id,
          name: newPlaylist.name,
          type: "scheduled",
          description: newPlaylist.description,
        };

        // Recarregar fila com nova playlist
        await this.refreshQueue(restaurantId);

        logger.info(
          `Transição concluída para playlist "${newPlaylist.name}" em ${restaurantId}`
        );

        // Notificar sobre transição concluída
        await this.wsService.broadcastToRestaurant(
          restaurantId,
          "playlist_transition_completed",
          {
            newPlaylist: state.currentPlaylist,
            queueSize: state.queue.length,
            message: `Transição para "${newPlaylist.name}" concluída`,
          }
        );

        // Iniciar próxima música se não há nada tocando
        if (!state.isPlaying && !state.currentTrack) {
          setTimeout(() => {
            this.startNextTrack(restaurantId);
          }, 2000); // 2 segundos de pausa para transição suave
        }
      }
    } catch (error) {
      logger.error(
        `Erro na execução da transição de playlist para ${restaurantId}:`,
        error
      );
    } finally {
      const state = this.playbackStates.get(restaurantId);
      if (state) {
        state.isTransitioning = false;
      }
    }
  }

  /**
   * Obter relatório de músicas problemáticas
   * @param restaurantId ID do restaurante
   */
  async getProblematicTracksReport(restaurantId: string): Promise<{
    problematicTracks: any[];
    recommendations: string[];
    healthScore: number;
  }> {
    try {
      const worstTracks = await this.analyticsService.getWorstRatedTracks(
        restaurantId
      );
      const tracksToRemove = await this.analyticsService.getTracksToRemove(
        restaurantId
      );

      const recommendations: string[] = [];

      if (worstTracks.length > 0) {
        recommendations.push(
          `${worstTracks.length} músicas com alta taxa de rejeição identificadas`
        );
      }

      if (tracksToRemove.length > 0) {
        recommendations.push(
          `${tracksToRemove.length} músicas recomendadas para remoção`
        );
      }

      if (worstTracks.length === 0) {
        recommendations.push(
          "Playlist está saudável - nenhuma música problemática detectada"
        );
      }

      const healthScore = Math.max(0, 100 - worstTracks.length * 10);

      logger.info(
        `Relatório de saúde gerado para restaurante ${restaurantId}: score ${healthScore}`
      );

      return {
        problematicTracks: worstTracks,
        recommendations,
        healthScore,
      };
    } catch (error) {
      logger.error(`Erro ao gerar relatório para ${restaurantId}:`, error);
      return {
        problematicTracks: [],
        recommendations: ["Erro ao analisar playlist"],
        healthScore: 50,
      };
    }
  }

  /**
   * Limpar recursos quando restaurante desconecta
   * @param restaurantId ID do restaurante
   */
  cleanup(restaurantId: string): void {
    const playbackTimer = this.playbackTimers.get(restaurantId);
    if (playbackTimer) {
      clearTimeout(playbackTimer);
      this.playbackTimers.delete(restaurantId);
    }

    const scheduleTimer = this.scheduleTimers.get(restaurantId);
    if (scheduleTimer) {
      clearTimeout(scheduleTimer);
      this.scheduleTimers.delete(restaurantId);
    }

    this.playbackStates.delete(restaurantId);
    logger.info(
      `Recursos de reprodução limpos para restaurante ${restaurantId}`
    );
  }

  /**
   * Realizar ação de reprodução
   * @param actionData Dados da ação
   */
  async performAction(actionData: PlaybackActionDto): Promise<void> {
    try {
      await this.validateInput(PlaybackActionDto, actionData);

      const { restaurantId, action, trackId } = actionData;

      switch (action) {
        case PlaybackAction.PLAY:
          if (trackId) {
            await this.playTrack(restaurantId, trackId);
          } else {
            const state = await this.getPlaybackState(restaurantId);
            if (state?.currentTrack && !state.isPlaying) {
              await this.resumePlayback(restaurantId);
            } else {
              await this.startNextTrack(restaurantId);
            }
          }
          break;

        case PlaybackAction.PAUSE:
          await this.pausePlayback(restaurantId);
          break;

        case PlaybackAction.SKIP:
          await this.skipToNext(restaurantId);
          break;

        case PlaybackAction.NEXT:
          await this.skipToNext(restaurantId);
          break;

        case PlaybackAction.STOP:
          const state = this.playbackStates.get(restaurantId);
          if (state && state.currentTrack) {
            await this.skipToNext(restaurantId);
            await this.pausePlayback(restaurantId);
          }
          break;

        default:
          throw new PlaybackError(
            `Ação de reprodução desconhecida: ${action}`,
            "UNKNOWN_ACTION",
            400,
            true,
            restaurantId
          );
      }

      logger.info(`Ação ${action} executada para restaurante ${restaurantId}`);
    } catch (error) {
      logger.error(`Erro ao executar ação de reprodução:`, error);

      if (error instanceof PlaybackError) {
        throw error;
      }

      throw new PlaybackError(
        `Falha ao executar ação: ${
          error instanceof Error ? error.message : "Erro desconhecido"
        }`,
        "ACTION_FAILED",
        500
      );
    }
  }

  /**
   * Encerra o serviço limpando timers e intervalos. Use em teardown de testes.
   */
  public shutdown(): void {
    this.isShuttingDown = true;
    if (this.scheduleInterval) {
      clearInterval(this.scheduleInterval);
      this.scheduleInterval = undefined;
    }
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
      this.cleanupInterval = undefined;
    }
    for (const [, t] of this.playbackTimers) clearTimeout(t);
    this.playbackTimers.clear();
    for (const [, t] of this.scheduleTimers) clearTimeout(t);
    this.scheduleTimers.clear();
  }
}

// Exportar instância singleton
export const playbackService = PlaybackService.getInstance();
export default playbackService;
