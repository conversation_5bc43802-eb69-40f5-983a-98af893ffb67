import { Router, Request, Response } from "express";
import { param, query, validationResult } from "../utils/validation";
import { analyticsService } from "../services/AnalyticsService";
import asyncHandler from "../middleware/asyncHandler";
import { optionalAuth } from "../middleware/auth";
import { ValidationError, NotFoundError } from "../utils/errors";
import { AppDataSource } from "../config/database";
import { Suggestion } from "../models/Suggestion";
import { Vote } from "../models/Vote";
import { ClientSession } from "../models/ClientSession";
import { playbackQueueService } from "../services/PlaybackQueueService";
import { redisClient } from "../config/redis";

// Interfaces para tipos de dados
interface PaymentStats {
  totalRevenue: number;
  totalPayments: number;
  averageAmount: number;
  dailyRevenue: Array<{
    date: string;
    revenue: number;
    count: number;
  }>;
  approvedPayments: number;
}

interface PaymentData {
  stats: PaymentStats;
}

interface VotingLeaderboardEntry {
  totalVotes: number;
  [key: string]: any;
}

interface VotingData {
  leaderboard: VotingLeaderboardEntry[];
  total: number;
}

interface QueueData {
  [key: string]: any;
}

const router = Router();

/**
 * Busca dados de curtidas (likes/dislikes) do Redis
 */
async function getLikesData(restaurantId: string): Promise<{
  totalLikes: number;
  totalDislikes: number;
  topLikedSongs: Array<{
    youtubeVideoId: string;
    title?: string;
    likes: number;
    dislikes: number;
  }>;
}> {
  try {
    if (!redisClient.isReady) {
      return { totalLikes: 0, totalDislikes: 0, topLikedSongs: [] };
    }

    const client = redisClient.getClient();

    // Buscar todas as chaves de likes e dislikes para este restaurante
    const likeKeys = await client.keys(`analytics:likes:${restaurantId}:*`);
    const dislikeKeys = await client.keys(`analytics:dislikes:${restaurantId}:*`);

    let totalLikes = 0;
    let totalDislikes = 0;
    const songStats: Map<string, { likes: number; dislikes: number }> = new Map();

    // Processar likes
    for (const key of likeKeys) {
      const youtubeVideoId = key.split(':').pop();
      if (youtubeVideoId) {
        const likes = parseInt(await client.get(key) || '0');
        totalLikes += likes;

        const current = songStats.get(youtubeVideoId) || { likes: 0, dislikes: 0 };
        songStats.set(youtubeVideoId, { ...current, likes });
      }
    }

    // Processar dislikes
    for (const key of dislikeKeys) {
      const youtubeVideoId = key.split(':').pop();
      if (youtubeVideoId) {
        const dislikes = parseInt(await client.get(key) || '0');
        totalDislikes += dislikes;

        const current = songStats.get(youtubeVideoId) || { likes: 0, dislikes: 0 };
        songStats.set(youtubeVideoId, { ...current, dislikes });
      }
    }

    // Criar lista das músicas mais curtidas
    const topLikedSongs = Array.from(songStats.entries())
      .map(([youtubeVideoId, stats]) => ({
        youtubeVideoId,
        likes: stats.likes,
        dislikes: stats.dislikes
      }))
      .sort((a, b) => b.likes - a.likes)
      .slice(0, 10); // Top 10

    return {
      totalLikes,
      totalDislikes,
      topLikedSongs
    };
  } catch (error) {
    console.error('Erro ao buscar dados de curtidas:', error);
    return { totalLikes: 0, totalDislikes: 0, topLikedSongs: [] };
  }
}

/**
 * @swagger
 * /api/v1/analytics/tracks/{restaurantId}:
 *   get:
 *     summary: Obter análise detalhada das tracks da playlist
 *     tags: [Analytics]
 *     parameters:
 *       - in: path
 *         name: restaurantId
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Análise das tracks
 */
router.get(
  "/tracks/:restaurantId",
  [
    param("restaurantId")
      .notEmpty()
      .withMessage("ID do restaurante é obrigatório"),
  ],
  optionalAuth,
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new ValidationError("Parâmetros inválidos", errors.array());
    }

    const { restaurantId } = req.params;

    try {
      const trackAnalytics = await analyticsService.getTracksAnalytics(
        restaurantId
      );

      res.json({
        success: true,
        trackAnalytics,
        generatedAt: new Date().toISOString(),
      });
    } catch (error) {
      console.error("Erro ao buscar analytics de tracks:", error);
      res.json({
        success: true,
        trackAnalytics: [],
        generatedAt: new Date().toISOString(),
      });
    }
  })
);

/**
 * @swagger
 * /api/v1/analytics/playlist-health/{restaurantId}:
 *   get:
 *     summary: Obter saúde geral da playlist
 *     tags: [Analytics]
 *     parameters:
 *       - in: path
 *         name: restaurantId
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Saúde da playlist
 */
router.get(
  "/playlist-health/:restaurantId",
  [
    param("restaurantId")
      .notEmpty()
      .withMessage("ID do restaurante é obrigatório"),
  ],
  optionalAuth,
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new ValidationError("Parâmetros inválidos", errors.array());
    }

    const { restaurantId } = req.params;

    try {
      const playlistHealth = await analyticsService.getPlaylistHealth(
        restaurantId
      );

      res.json({
        success: true,
        playlistHealth,
        generatedAt: new Date().toISOString(),
      });
    } catch (error) {
      console.error("Erro ao buscar saúde da playlist:", error);
      res.json({
        success: true,
        playlistHealth: null,
        generatedAt: new Date().toISOString(),
      });
    }
  })
);

/**
 * @swagger
 * /api/v1/analytics/dashboard/{restaurantId}:
 *   get:
 *     summary: Obter resumo do dashboard de analytics
 *     tags: [Analytics]
 *     parameters:
 *       - in: path
 *         name: restaurantId
 *         required: true
 *         schema:
 *           type: string
 *       - in: query
 *         name: period
 *         schema:
 *           type: string
 *           enum: [1d, 7d, 30d, 90d]
 *           default: 7d
 *     responses:
 *       200:
 *         description: Resumo do dashboard
 */
router.get(
  "/dashboard/:restaurantId",
  [
    param("restaurantId")
      .notEmpty()
      .withMessage("ID do restaurante é obrigatório"),
    query("period").optional().isIn(["1d", "7d", "30d", "90d"]),
  ],
  optionalAuth,
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new ValidationError("Parâmetros inválidos", errors.array());
    }

    const { restaurantId } = req.params;
    const { period = "7d" } = req.query;

    const summary = await analyticsService.generateDashboardSummary(
      restaurantId,
      period as string
    );

    res.json({
      success: true,
      summary,
      period,
      generatedAt: new Date().toISOString(),
    });
  })
);

/**
 * @swagger
 * /api/v1/analytics/metrics/{restaurantId}:
 *   get:
 *     summary: Obter métricas detalhadas
 *     tags: [Analytics]
 *     parameters:
 *       - in: path
 *         name: restaurantId
 *         required: true
 *         schema:
 *           type: string
 *       - in: query
 *         name: period
 *         schema:
 *           type: string
 *           enum: [1d, 7d, 30d, 90d]
 *           default: 7d
 *     responses:
 *       200:
 *         description: Métricas detalhadas
 */
router.get(
  "/metrics/:restaurantId",
  [
    param("restaurantId")
      .notEmpty()
      .withMessage("ID do restaurante é obrigatório"),
    query("period").optional().isIn(["1d", "7d", "30d", "90d"]),
  ],
  optionalAuth,
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new ValidationError("Parâmetros inválidos", errors.array());
    }

    const { restaurantId } = req.params;
    const { period = "7d" } = req.query;

    const metrics = await analyticsService.getMetrics(
      restaurantId,
      period as any
    );

    res.json({
      success: true,
      metrics,
      period,
      generatedAt: new Date().toISOString(),
    });
  })
);

/**
 * @swagger
 * /api/v1/analytics/stats/{restaurantId}:
 *   get:
 *     summary: Obter estatísticas rápidas (compatibilidade)
 *     tags: [Analytics]
 *     parameters:
 *       - in: path
 *         name: restaurantId
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Estatísticas rápidas
 */
router.get(
  "/stats/:restaurantId",
  [
    param("restaurantId")
      .notEmpty()
      .withMessage("ID do restaurante é obrigatório"),
  ],
  optionalAuth,
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new ValidationError("Parâmetros inválidos", errors.array());
    }

    const { restaurantId } = req.params;

    // Compatibilidade com endpoint antigo
    const [summary, metrics, popularSongs] = await Promise.all([
      analyticsService.generateDashboardSummary(restaurantId, "7d"),
      analyticsService.getMetrics(restaurantId, "7d" as any),
      analyticsService.getPopularSongs(restaurantId, 5),
    ]);

    res.json({
      success: true,
      stats: {
        summary,
        totalSuggestions: metrics.totalSuggestions,
        totalVotes: metrics.totalVotes,
        activeUsers: metrics.uniqueSessions,
        topSongs: popularSongs,
        hourlyActivity: metrics.hourlyActivity,
        topGenres: metrics.topGenres,
      },
      generatedAt: new Date().toISOString(),
    });
  })
);

/**
 * @swagger
 * /api/v1/analytics/competitive/{restaurantId}:
 *   get:
 *     summary: Obter analytics competitivos
 *     tags: [Analytics]
 *     parameters:
 *       - in: path
 *         name: restaurantId
 *         required: true
 *         schema:
 *           type: string
 *       - in: query
 *         name: period
 *         schema:
 *           type: string
 *           enum: [1d, 7d, 30d]
 *           default: 1d
 *     responses:
 *       200:
 *         description: Analytics competitivos
 */
router.get(
  "/competitive/:restaurantId",
  [
    param("restaurantId")
      .notEmpty()
      .withMessage("ID do restaurante é obrigatório"),
    query("period").optional().isIn(["1d", "7d", "30d"]),
  ],
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new ValidationError("Parâmetros inválidos", errors.array());
    }

    const { restaurantId } = req.params;
    const { period = "1d" } = req.query;

    // Calcular período
    const endDate = new Date();
    const startDate = new Date();

    switch (period) {
      case "1d":
        startDate.setDate(endDate.getDate() - 1);
        break;
      case "7d":
        startDate.setDate(endDate.getDate() - 7);
        break;
      case "30d":
        startDate.setDate(endDate.getDate() - 30);
        break;
      default:
        startDate.setDate(endDate.getDate() - 1);
    }

    try {
      // Buscar dados de pagamentos
      const paymentResponse = await fetch(
        `http://localhost:5000/api/v1/payments/stats/${restaurantId}?period=${period}`
      );
      const paymentData = paymentResponse.ok
        ? await paymentResponse.json()
        : null;

      const paymentStats = (paymentData as any)?.stats || {
        total: 0,
        approved: 0,
        pending: 0,
        rejected: 0,
        totalAmount: 0,
      };

      // Buscar dados de votação
      const votingResponse = await fetch(
        `http://localhost:5000/api/v1/competitive-voting/leaderboard/${restaurantId}`
      );
      const votingData = votingResponse.ok ? await votingResponse.json() : null;

      const votingStats = (votingData as any) || { leaderboard: [], total: 0 };

      // Buscar dados de curtidas (likes/dislikes)
      const likesData = await getLikesData(restaurantId);

      // Buscar fila de reprodução usando serviço interno
      const queueData = await playbackQueueService.getPlaybackQueue(restaurantId);
      const queueStats = queueData ? {
        totalItems: queueData.queue?.length || 0,
        estimatedWaitTime: queueData.stats?.estimatedWaitTime || 0,
        paidCount: queueData.queue?.filter(item => item.isPaid).length || 0,
        freeCount: queueData.queue?.filter(item => !item.isPaid).length || 0
      } : null;

      // Compilar analytics competitivos
      const competitiveAnalytics = {
        period,
        revenue: {
          total: (paymentStats as any)?.totalRevenue || 0,
          totalPayments: (paymentStats as any)?.totalPayments || 0,
          averageAmount: (paymentStats as any)?.averageAmount || 0,
          dailyRevenue: (paymentStats as any)?.dailyRevenue || [],
        },
        voting: {
          totalVotes:
            votingStats?.leaderboard?.reduce(
              (sum: number, entry: any) => sum + entry.totalVotes,
              0
            ) || 0,
          totalPerformances: votingStats?.total || 0,
          averageRating:
            votingStats?.leaderboard?.length > 0
              ? votingStats.leaderboard.reduce(
                  (sum: number, entry: any) => sum + entry.averageRating,
                  0
                ) / votingStats.leaderboard.length
              : 0,
          topPerformers: votingStats?.leaderboard?.slice(0, 5) || [],
        },
        queue: {
          totalItems: queueStats?.totalItems || 0,
          paidItems: queueStats?.paidCount || 0,
          freeItems: queueStats?.freeCount || 0,
          paidPercentage:
            queueStats?.totalItems > 0
              ? Math.round(
                  (queueStats.paidCount / queueStats.totalItems) * 100
                )
              : 0,
        },
        engagement: {
          paymentConversionRate:
            (paymentData as any)?.stats?.totalPayments &&
            (paymentData as any)?.stats?.totalPayments > 0
              ? Math.round(
                  ((paymentData as any).stats.approvedPayments /
                    (paymentData as any).stats.totalPayments) *
                    100
                )
              : 0,
          votingParticipationRate:
            votingStats?.leaderboard?.length > 0 &&
            queueStats?.paidCount > 0
              ? Math.round(
                  (votingStats.leaderboard.length / queueStats.paidCount) *
                    100
                )
              : 0,
          averageVotesPerSong:
            votingStats?.leaderboard?.length > 0
              ? Math.round(
                  votingStats.leaderboard.reduce(
                    (sum: number, entry: any) => sum + entry.totalVotes,
                    0
                  ) / votingStats.leaderboard.length
                )
              : 0,
        },
        trends: {
          hourlyRevenue:
            (paymentData as any)?.stats?.dailyRevenue?.map((day: any) => ({
              hour: new Date(day.date).getHours(),
              revenue: day.revenue,
              count: day.count,
            })) || [],
          topTables:
            votingStats?.leaderboard
              ?.reduce((tables: any[], entry: any) => {
                const existing = tables.find(
                  (t) => t.tableName === entry.tableName
                );
                if (existing) {
                  existing.totalVotes += entry.totalVotes;
                  existing.performances += 1;
                  existing.averageRating =
                    (existing.averageRating + entry.averageRating) / 2;
                } else {
                  tables.push({
                    tableName: entry.tableName,
                    totalVotes: entry.totalVotes,
                    performances: 1,
                    averageRating: entry.averageRating,
                  });
                }
                return tables;
              }, [])
              ?.sort((a: any, b: any) => b.averageRating - a.averageRating)
              ?.slice(0, 5) || [],
          topLikedSongs: likesData.topLikedSongs.slice(0, 5).map(song => ({
            youtubeVideoId: song.youtubeVideoId,
            likes: song.likes,
            dislikes: song.dislikes,
            likeRatio: song.likes + song.dislikes > 0
              ? Math.round((song.likes / (song.likes + song.dislikes)) * 100)
              : 0
          })),
        },
        likes: {
          totalLikes: likesData.totalLikes,
          totalDislikes: likesData.totalDislikes,
          likeDislikeRatio: likesData.totalLikes + likesData.totalDislikes > 0
            ? Math.round((likesData.totalLikes / (likesData.totalLikes + likesData.totalDislikes)) * 100)
            : 0,
          totalInteractions: likesData.totalLikes + likesData.totalDislikes,
        },
      };

      res.json({
        success: true,
        analytics: competitiveAnalytics,
        generatedAt: new Date().toISOString(),
      });
    } catch (error) {
      console.error("Erro ao obter analytics competitivos:", error);

      // Retornar dados mock em caso de erro
      res.json({
        success: true,
        analytics: {
          period,
          revenue: {
            total: 0,
            totalPayments: 0,
            averageAmount: 0,
            dailyRevenue: [],
          },
          voting: {
            totalVotes: 0,
            totalPerformances: 0,
            averageRating: 0,
            topPerformers: [],
          },
          queue: {
            totalItems: 0,
            paidItems: 0,
            freeItems: 0,
            paidPercentage: 0,
          },
          engagement: {
            paymentConversionRate: 0,
            votingParticipationRate: 0,
            averageVotesPerSong: 0,
          },
          trends: { hourlyRevenue: [], topTables: [] },
        },
        generatedAt: new Date().toISOString(),
      });
    }
  })
);

/**
 * @swagger
 * /api/v1/analytics/tables/{restaurantId}/leaderboard:
 *   get:
 *     summary: Obter ranking das mesas por atividade
 *     tags: [Analytics]
 *     parameters:
 *       - in: path
 *         name: restaurantId
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Ranking das mesas
 */
router.get(
  "/tables/:restaurantId/leaderboard",
  [
    param("restaurantId")
      .notEmpty()
      .withMessage("ID do restaurante é obrigatório"),
  ],
  asyncHandler(async (req: Request, res: Response) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new ValidationError("Dados inválidos", errors.array());
    }

    const { restaurantId } = req.params;

    try {
      // Buscar estatísticas das mesas baseado em sugestões e votos
      const suggestionRepository = AppDataSource.getRepository(Suggestion);
      const voteRepository = AppDataSource.getRepository(Vote);
      const sessionRepository = AppDataSource.getRepository(ClientSession);

      // Obter todas as sessões do restaurante com estatísticas
      const sessions = await sessionRepository
        .createQueryBuilder("session")
        .where("session.restaurant_id = :restaurantId", { restaurantId })
        .andWhere("session.tableNumber IS NOT NULL")
        .getMany();

      // Agrupar por mesa e calcular estatísticas
      const tableStats = new Map();

      for (const session of sessions) {
        const tableNumber = session.tableNumber;
        if (!tableNumber) continue;

        if (!tableStats.has(tableNumber)) {
          tableStats.set(tableNumber, {
            tableNumber,
            totalSuggestions: 0,
            totalVotes: 0,
            paidSuggestions: 0,
            points: 0,
            averageScore: 0,
            sessions: [],
          });
        }

        const tableData = tableStats.get(tableNumber);
        tableData.sessions.push(session);

        // Somar pontos da sessão
        tableData.points += session.points || 0;
        tableData.totalSuggestions += session.suggestionsCount || 0;
        tableData.totalVotes += session.votesCount || 0;
      }

      // Buscar sugestões pagas por mesa
      const paidSuggestions = await suggestionRepository
        .createQueryBuilder("suggestion")
        .leftJoin("suggestion.clientSession", "session")
        .where("suggestion.restaurant_id = :restaurantId", { restaurantId })
        .andWhere("suggestion.isPaid = true")
        .andWhere("session.tableNumber IS NOT NULL")
        .select(["session.tableNumber", "COUNT(*) as count"])
        .groupBy("session.tableNumber")
        .getRawMany();

      // Atualizar contadores de sugestões pagas
      paidSuggestions.forEach(({ session_tableNumber, count }) => {
        if (tableStats.has(session_tableNumber)) {
          tableStats.get(session_tableNumber).paidSuggestions = parseInt(count);
        }
      });

      // Calcular score médio por mesa
      for (const [tableNumber, data] of tableStats) {
        const suggestions = await suggestionRepository
          .createQueryBuilder("suggestion")
          .leftJoin("suggestion.clientSession", "session")
          .where("suggestion.restaurant_id = :restaurantId", { restaurantId })
          .andWhere("session.tableNumber = :tableNumber", { tableNumber })
          .getMany();

        if (suggestions.length > 0) {
          const totalScore = suggestions.reduce(
            (sum, s) => sum + (s.voteCount || 0),
            0
          );
          data.averageScore = totalScore / suggestions.length;
        }
      }

      // Converter para array e ordenar por pontos
      const tablesArray = Array.from(tableStats.values())
        .sort((a, b) => b.points - a.points)
        .map((table, index) => ({
          ...table,
          rank: index + 1,
          sessions: undefined, // Remover dados internos
        }));

      res.json({
        success: true,
        tables: tablesArray,
        totalTables: tablesArray.length,
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      console.error("Erro ao buscar ranking das mesas:", error);
      res.status(500).json({
        success: false,
        message: "Erro ao buscar ranking das mesas",
      });
    }
  })
);

// Rota de teste simples
router.get("/test-tracks", (req, res) => {
  res.json({
    success: true,
    message: "Rota de teste tracks funcionando!",
    timestamp: new Date().toISOString(),
  });
});

export default router;
