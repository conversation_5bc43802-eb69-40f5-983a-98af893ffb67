// Verifica se um voto normal reordena imediatamente a fila (não deveria)
const http = require('http');
const { randomUUID } = require('crypto');

function req(method, path, body, headers={}) {
  return new Promise((resolve) => {
    const options = {
      hostname: 'localhost',
      port: 8001,
      path,
      method,
      headers: { ...(body ? { 'Content-Type': 'application/json' } : {}), ...headers },
    };
    const q = http.request(options, (res) => {
      let d = '';
      res.on('data', (c) => (d += c));
      res.on('end', () => {
        try { resolve({ s: res.statusCode, j: JSON.parse(d) }); }
        catch { resolve({ s: res.statusCode, j: { raw: d } }); }
      });
    });
    q.on('error', (e) => resolve({ s: 0, j: { error: e.message } }));
    if (body) q.write(JSON.stringify(body));
    q.end();
  });
}

(async () => {
  const restaurantId = process.env.RESTAURANT_ID || 'demo-restaurant';
  const baseQueue = `/api/v1/playback-queue/${restaurantId}`;
  const baseSugg = `/api/v1/suggestions/${restaurantId}`; // se existir listagem por restaurante

  // Buscar sugestões via endpoint já utilizado nos smokes anteriores
  const s = await req('GET', `/api/v1/suggestions/${restaurantId}`);
  if (s.s !== 200 || !s.j?.suggestions?.length) {
    console.log('ERROR: suggestions unavailable', s.s, s.j);
    process.exit(1);
  }

  // Capturar fila e mapear posições
  const before = await req('GET', baseQueue);
  if (before.s !== 200 || !before.j?.queue?.length) {
    console.log('ERROR: queue unavailable', before.s, before.j);
    process.exit(1);
  }
  const top = before.j.queue.slice(0, 6);
  const posByVideo = new Map(top.map((x, i) => [x.youtubeVideoId, i]));

  // Selecionar uma sugestão que esteja na fila (para refletir efeito do voto)
  const candidate = s.j.suggestions.find(x => x.status === 'approved' && posByVideo.has(x.youtubeVideoId));
  if (!candidate) {
    console.log('ERROR: no approved suggestion from queue to vote');
    process.exit(1);
  }

  const initialPos = posByVideo.get(candidate.youtubeVideoId);
  const voteSession = randomUUID();
  console.log('Voting on:', candidate.title, candidate.id, candidate.youtubeVideoId, 'pos', initialPos);

  // Enviar voto normal (up)
  const vote = await req('POST', `/api/v1/suggestions/${candidate.id}/vote`, { voteType: 'up' }, { 'x-session-id': voteSession });
  console.log('Vote resp:', vote.s, vote.j?.message || vote.j?.raw || vote.j);

  // Reconsultar fila imediatamente
  const after = await req('GET', baseQueue);
  const afterTop = after.j?.queue?.slice(0, 6) || [];
  const newPos = afterTop.findIndex(x => x.youtubeVideoId === candidate.youtubeVideoId);
  const immediateReorder = newPos !== -1 && newPos < (initialPos ?? 99999);
  console.log('IMMEDIATE_REORDER_AFTER_NORMAL_VOTE', immediateReorder, `initial=${initialPos} new=${newPos}`);

  process.exit(immediateReorder ? 2 : 0);
})();
