// Checa status do serviço de reordenação automática legado
const http = require('http');

function req(method, path) {
  return new Promise((resolve) => {
    const options = { hostname: 'localhost', port: 8001, path, method };
    const q = http.request(options, (res) => {
      let d = '';
      res.on('data', (c) => (d += c));
      res.on('end', () => {
        try { resolve({ s: res.statusCode, j: JSON.parse(d) }); }
        catch { resolve({ s: res.statusCode, j: { raw: d } }); }
      });
    });
    q.on('error', (e) => resolve({ s: 0, j: { error: e.message } }));
    q.end();
  });
}

(async () => {
  const st = await req('GET', '/api/v1/playlist-reorder/status');
  console.log('playlist-reorder/status:', st.s, st.j);
  process.exit(0);
})();
