{"version": 3, "file": "SnapshotService.js", "sourceRoot": "", "sources": ["../../src/services/SnapshotService.ts"], "names": [], "mappings": ";;;AACA,iDAAmD;AACnD,qDAAkD;AAClD,+CAA4C;AAC5C,2DAAwD;AACxD,yDAAsD;AAsCtD,MAAa,eAAe;IAW1B;QANQ,cAAS,GAAiC,IAAI,GAAG,EAAE,CAAC;QAE5D,8CAA8C;QACtC,qBAAgB,GAAwB,IAAI,GAAG,EAAE,CAAC;QACzC,0BAAqB,GAAG,IAAI,CAAC,CAAC,oCAAoC;QAGjF,IAAI,CAAC,oBAAoB,GAAG,wBAAa,CAAC,aAAa,CAAC,uBAAU,CAAC,CAAC;QACpE,IAAI,CAAC,iBAAiB,GAAG,wBAAa,CAAC,aAAa,CAAC,iBAAO,CAAC,CAAC;QAC9D,IAAI,CAAC,uBAAuB,GAAG,wBAAa,CAAC,aAAa,CAAC,6BAAa,CAAC,CAAC;QAC5E,IAAI,CAAC,gBAAgB,GAAG,mCAAgB,CAAC,WAAW,EAAE,CAAC;IACvD,CAAC;IAED,KAAK,CAAC,gBAAgB,CAAC,YAAoB,EAAE,eAAwB,KAAK;QACxE,IAAI;YACF,uDAAuD;YACvD,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YACvB,MAAM,QAAQ,GAAG,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;YAE9D,IAAI,CAAC,YAAY,IAAI,CAAC,GAAG,GAAG,QAAQ,CAAC,GAAG,IAAI,CAAC,qBAAqB,EAAE;gBAClE,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;gBAChD,IAAI,MAAM,EAAE;oBACV,OAAO,CAAC,GAAG,CAAC,2CAA2C,YAAY,KAAK,GAAG,GAAG,QAAQ,SAAS,CAAC,CAAC;oBACjG,OAAO,MAAM,CAAC;iBACf;aACF;YAED,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,YAAY,EAAE,GAAG,CAAC,CAAC;YAC7C,qDAAqD;YACrD,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,oBAAoB;iBAChD,kBAAkB,CAAC,YAAY,CAAC;iBAChC,QAAQ,CAAC,UAAU,EAAE,SAAS,EAAE,mEAAmE,CAAC;iBACpG,SAAS,CAAC;gBACT,oDAAoD;gBACpD,mEAAmE;aACpE,CAAC;iBACD,KAAK,CAAC,0CAA0C,EAAE,EAAE,YAAY,EAAE,CAAC;iBACnE,QAAQ,CAAC,8CAA8C,CAAC;iBACxD,OAAO,CAAC,eAAe,CAAC;iBACxB,iBAAiB,EAAE,CAAC;YAEvB,yEAAyE;YACzE,MAAM,iBAAiB,GAAG,WAAW,CAAC,QAAQ;iBAC3C,GAAG,CAAC,UAAU,CAAC,EAAE;gBAChB,MAAM,GAAG,GAAG,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,aAAa,KAAK,UAAU,CAAC,EAAE,CAAC,CAAC;gBACzE,MAAM,aAAa,GAAG,UAAU,CAAC,GAAG,EAAE,cAAc,IAAI,GAAG,CAAC,CAAC;gBAC7D,MAAM,MAAM,GAAG,GAAG,EAAE,OAAO,KAAK,IAAI,IAAI,aAAa,GAAG,CAAC,CAAC;gBAE1D,oGAAoG;gBACpG,MAAM,eAAe,GAAG,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,wBAAwB,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAClF,MAAM,cAAc,GAAG,CAAC,UAAU,CAAC,SAAS,IAAI,CAAC,CAAC,GAAG,eAAe,CAAC;gBAErE,OAAO;oBACL,EAAE,EAAE,UAAU,CAAC,EAAE;oBACjB,cAAc,EAAE,UAAU,CAAC,cAAc;oBACzC,KAAK,EAAE,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,KAAK,CAAC;oBAC3C,MAAM,EAAE,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,MAAM,CAAC,IAAI,sBAAsB;oBACvE,SAAS,EAAE,cAAc;oBACzB,iBAAiB,EAAE,UAAU,CAAC,SAAS,IAAI,CAAC;oBAC5C,eAAe;oBACf,KAAK,EAAE,IAAI,CAAC,cAAc,CAAC,UAAU,EAAE,aAAa,EAAE,cAAc,CAAC;oBACrE,MAAM;oBACN,aAAa;iBACd,CAAC;YACJ,CAAC,CAAC;iBACD,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;gBACb,2EAA2E;gBAC3E,IAAI,CAAC,CAAC,SAAS,KAAK,CAAC,CAAC,SAAS,EAAE;oBAC/B,OAAO,CAAC,CAAC,SAAS,GAAG,CAAC,CAAC,SAAS,CAAC;iBAClC;gBAED,8DAA8D;gBAC9D,MAAM,QAAQ,GAAI,WAAW,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,EAAE,CAAS,EAAE,SAAS,IAAI,IAAI,IAAI,EAAE,CAAC;gBACjG,MAAM,QAAQ,GAAI,WAAW,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,EAAE,CAAS,EAAE,SAAS,IAAI,IAAI,IAAI,EAAE,CAAC;gBACjG,OAAO,IAAI,IAAI,CAAC,QAAQ,CAAC,CAAC,OAAO,EAAE,GAAG,IAAI,IAAI,CAAC,QAAQ,CAAC,CAAC,OAAO,EAAE,CAAC;YACrE,CAAC,CAAC,CAAC;YAEL,6FAA6F;YAC7F,MAAM,SAAS,GAAG,iBAAiB;iBAChC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC;iBAC3B,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;gBACZ,EAAE,EAAE,IAAI,CAAC,EAAE;gBACX,cAAc,EAAE,IAAI,CAAC,cAAc;gBACnC,KAAK,EAAE,IAAI,CAAC,KAAK;gBACjB,MAAM,EAAE,IAAI,CAAC,MAAM;gBACnB,SAAS,EAAE,IAAI,CAAC,SAAS;gBACzB,iBAAiB,EAAE,IAAI,CAAC,iBAAiB;gBACzC,eAAe,EAAE,IAAI,CAAC,eAAe;gBACrC,aAAa,EAAE,IAAI,CAAC,aAAa;aAClC,CAAC,CAAC,CAAC;YAEN,MAAM,SAAS,GAAG,iBAAiB;iBAChC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC;iBAC5B,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;gBACZ,EAAE,EAAE,IAAI,CAAC,EAAE;gBACX,cAAc,EAAE,IAAI,CAAC,cAAc;gBACnC,KAAK,EAAE,IAAI,CAAC,KAAK;gBACjB,MAAM,EAAE,IAAI,CAAC,MAAM;gBACnB,SAAS,EAAE,IAAI,CAAC,SAAS;gBACzB,iBAAiB,EAAE,IAAI,CAAC,iBAAiB;gBACzC,eAAe,EAAE,IAAI,CAAC,eAAe;aACtC,CAAC,CAAC,CAAC;YAEN,4EAA4E;YAC5E,IAAI,SAAS,GAAG,iBAAiB,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;YAE3E,uDAAuD;YACvD,IAAI,CAAC,SAAS,EAAE;gBACd,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,uBAAuB;qBACjD,kBAAkB,CAAC,OAAO,CAAC;qBAC3B,SAAS,CAAC,gBAAgB,EAAE,UAAU,CAAC;qBACvC,KAAK,CAAC,wCAAwC,EAAE,EAAE,YAAY,EAAE,CAAC;qBACjE,QAAQ,CAAC,wBAAwB,CAAC;qBAClC,OAAO,CAAC,gBAAgB,EAAE,KAAK,CAAC;qBAChC,MAAM,EAAE,CAAC;gBAEZ,IAAI,SAAS,EAAE;oBACb,SAAS,GAAG;wBACV,EAAE,EAAE,SAAS,CAAC,EAAE;wBAChB,cAAc,EAAE,SAAS,CAAC,cAAc;wBACxC,KAAK,EAAE,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,KAAK,CAAC;wBAC1C,MAAM,EAAE,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,MAAM,CAAC;wBAC5C,SAAS,EAAE,CAAC;wBACZ,iBAAiB,EAAE,CAAC;wBACpB,eAAe,EAAE,CAAC;wBAClB,KAAK,EAAE,CAAC;wBACR,MAAM,EAAE,KAAK;wBACb,aAAa,EAAE,CAAC;qBACjB,CAAC;iBACH;aACF;YAED,MAAM,QAAQ,GAAoB;gBAChC,YAAY;gBACZ,SAAS;gBACT,SAAS;gBACT,SAAS;gBACT,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC;YAEF,iBAAiB;YACjB,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,YAAY,EAAE,QAAQ,CAAC,CAAC;YAE/C,iDAAiD;YACjD,IAAI,CAAC,gBAAgB,EAAE,qBAAqB,CAAC,YAAY,EAAE,kBAAkB,EAAE;gBACzE,SAAS,EAAE,QAAQ,CAAC,SAAS;gBAC7B,SAAS,EAAE,QAAQ,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC;gBAC1C,SAAS,EAAE,QAAQ,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC;gBAC1C,SAAS,EAAE,QAAQ,CAAC,SAAS;aAC9B,CAAC,CAAC;YAEH,OAAO,CAAC,GAAG,CAAC,wCAAwC,YAAY,GAAG,EAAE;gBACnE,SAAS,EAAE,SAAS,EAAE,KAAK,IAAI,MAAM;gBACrC,eAAe,EAAE,SAAS,EAAE,MAAM,IAAI,MAAM;gBAC5C,WAAW,EAAE,SAAS,EAAE,cAAc,IAAI,MAAM;gBAChD,cAAc,EAAE,SAAS,EAAE,SAAS,IAAI,CAAC;gBACzC,sBAAsB,EAAE,SAAS,EAAE,iBAAiB,IAAI,CAAC;gBACzD,wBAAwB,EAAE,SAAS,EAAE,eAAe,IAAI,CAAC;gBACzD,gBAAgB,EAAE,iBAAiB,CAAC,MAAM;gBAC1C,SAAS,EAAE,SAAS,CAAC,MAAM;gBAC3B,SAAS,EAAE,SAAS,CAAC,MAAM;aAC5B,CAAC,CAAC;YAEH,2CAA2C;YAC3C,IAAI,SAAS,EAAE,KAAK,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,SAAS,CAAC,KAAK,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,SAAS,CAAC,KAAK,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,EAAE;gBACzH,OAAO,CAAC,GAAG,CAAC,mDAAmD,SAAS,CAAC,KAAK,SAAS,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC;aAC7G;YAED,kCAAkC;YAClC,IAAI,iBAAiB,CAAC,MAAM,GAAG,CAAC,EAAE;gBAChC,OAAO,CAAC,GAAG,CAAC,+BAA+B,EACzC,iBAAiB,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE,CAAC,CAAC;oBAClD,QAAQ,EAAE,KAAK,GAAG,CAAC;oBACnB,KAAK,EAAE,IAAI,CAAC,KAAK;oBACjB,UAAU,EAAE,IAAI,CAAC,SAAS;oBAC1B,aAAa,EAAE,IAAI,CAAC,iBAAiB;oBACrC,eAAe,EAAE,IAAI,CAAC,eAAe;oBACrC,MAAM,EAAE,IAAI,CAAC,MAAM;iBACpB,CAAC,CAAC,CACJ,CAAC;aACH;YAED,OAAO,QAAQ,CAAC;SACjB;QAAC,OAAO,KAAK,EAAE;YACd,OAAO,CAAC,KAAK,CAAC,8CAA8C,YAAY,GAAG,EAAE;gBAC3E,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;gBAC7D,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,SAAS;gBACvD,YAAY;aACb,CAAC,CAAC;YAEH,0EAA0E;YAC1E,MAAM,aAAa,GAAoB;gBACrC,YAAY;gBACZ,SAAS,EAAE,IAAI;gBACf,SAAS,EAAE,EAAE;gBACb,SAAS,EAAE,EAAE;gBACb,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC;YAEF,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,YAAY,EAAE,aAAa,CAAC,CAAC;YAChD,OAAO,aAAa,CAAC;SACtB;IACH,CAAC;IAEO,aAAa,CAAC,KAAgC;QACpD,IAAI,CAAC,KAAK;YAAE,OAAO,EAAE,CAAC;QAEtB,IAAI;YACF,kDAAkD;YAClD,IAAI,SAAS,GAAG,KAAK,CAAC,QAAQ,EAAE,CAAC;YAEjC,6BAA6B;YAC7B,SAAS,GAAG,SAAS;iBAClB,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC;iBACnB,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC;iBACnB,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC;iBACnB,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC;iBACnB,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC;iBACnB,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC;iBACnB,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC;iBACnB,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC;iBACnB,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC;iBACnB,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC;iBACnB,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;YAEvB,+BAA+B;YAC/B,SAAS,GAAG,SAAS,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;YAEvC,oDAAoD;YACpD,SAAS,GAAG,SAAS;iBAClB,OAAO,CAAC,KAAK,EAAE,MAAM,CAAC,CAAE,qBAAqB;iBAC7C,OAAO,CAAC,IAAI,EAAE,KAAK,CAAC,CAAI,uBAAuB;iBAC/C,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC,CAAG,kBAAkB;iBAC1C,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC,CAAG,0BAA0B;iBAClD,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC,CAAE,cAAc;YAEzC,0CAA0C;YAC1C,SAAS,GAAG,SAAS,CAAC,IAAI,EAAE,CAAC;YAC7B,IAAI,SAAS,CAAC,MAAM,GAAG,GAAG,EAAE;gBAC1B,SAAS,GAAG,SAAS,CAAC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,GAAG,KAAK,CAAC;aACjD;YAED,OAAO,SAAS,CAAC;SAClB;QAAC,OAAO,KAAK,EAAE;YACd,OAAO,CAAC,KAAK,CAAC,yBAAyB,EAAE,KAAK,EAAE,iBAAiB,EAAE,KAAK,CAAC,CAAC;YAC1E,OAAO,MAAM,CAAC,KAAK,IAAI,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;SAC9C;IACH,CAAC;IAED;;OAEG;IACK,wBAAwB,CAAC,aAAqB;QACpD,+DAA+D;QAC/D,IAAI,aAAa,IAAI,EAAE;YAAE,OAAO,GAAG,CAAC,CAAC,qBAAqB;QAC1D,IAAI,aAAa,IAAI,EAAE;YAAE,OAAO,EAAE,CAAC,CAAE,oBAAoB;QACzD,IAAI,aAAa,IAAI,CAAC;YAAE,OAAO,EAAE,CAAC,CAAG,oBAAoB;QACzD,OAAO,CAAC,CAAC,CAAC,cAAc;IAC1B,CAAC;IAEO,cAAc,CAAC,UAAsB,EAAE,aAAqB,EAAE,cAAsB;QAC1F,+DAA+D;QAC/D,8EAA8E;QAC9E,MAAM,SAAS,GAAG,cAAc,CAAC;QAEjC,iEAAiE;QACjE,MAAM,OAAO,GAAI,UAAkB,CAAC,SAAS,IAAK,UAAkB,CAAC,SAAS,IAAI,IAAI,IAAI,EAAE,CAAC;QAC7F,IAAI,SAAiB,CAAC;QACtB,IAAI;YACF,MAAM,CAAC,GAAG,IAAI,IAAI,CAAC,OAAc,CAAC,CAAC,OAAO,EAAE,CAAC;YAC7C,SAAS,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC;SAC1C;QAAC,MAAM;YACN,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;SACxB;QACD,MAAM,QAAQ,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC,GAAG,CAAC,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;QAC7D,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,QAAQ,GAAG,EAAE,CAAC,CAAC,CAAC,8BAA8B;QAE1E,OAAO,SAAS,GAAG,SAAS,CAAC;IAC/B,CAAC;IAED,WAAW,CAAC,YAAoB;QAC9B,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,YAAY,CAAC,IAAI,IAAI,CAAC;IAClD,CAAC;IAED,KAAK,CAAC,sBAAsB;QAC1B,OAAO,CAAC,GAAG,CAAC,4DAA4D,CAAC,CAAC;QAE1E,WAAW,CAAC,KAAK,IAAI,EAAE;YACrB,IAAI;gBACF,6BAA6B;gBAC7B,MAAM,WAAW,GAAG,MAAM,wBAAa,CAAC,KAAK,CAAC;;;;;;;SAO7C,CAAC,CAAC;gBAEH,KAAK,MAAM,UAAU,IAAI,WAAW,EAAE;oBACpC,MAAM,IAAI,CAAC,gBAAgB,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC;iBAC5C;aACF;YAAC,OAAO,KAAK,EAAE;gBACd,OAAO,CAAC,KAAK,CAAC,wCAAwC,EAAE,KAAK,CAAC,CAAC;aAChE;QACH,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,YAAY;IACjC,CAAC;IAED,KAAK,CAAC,uCAAuC;QAC3C,IAAI;YACF,MAAM,WAAW,GAAG,MAAM,wBAAa,CAAC,KAAK,CAAC;;;;OAI7C,CAAC,CAAC;YAEH,KAAK,MAAM,UAAU,IAAI,WAAW,EAAE;gBACpC,MAAM,IAAI,CAAC,gBAAgB,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC;aAC5C;SACF;QAAC,OAAO,KAAK,EAAE;YACd,OAAO,CAAC,KAAK,CAAC,iDAAiD,EAAE,KAAK,CAAC,CAAC;SACzE;IACH,CAAC;CACF;AAxUD,0CAwUC;AAED,qBAAqB;AACR,QAAA,eAAe,GAAG,IAAI,eAAe,EAAE,CAAC"}