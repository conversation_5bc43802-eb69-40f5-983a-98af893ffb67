import { Repository } from "typeorm";
import { AppDataSource } from "../config/database";
import { Suggestion } from "../models/Suggestion";
import { Payment } from "../models/Payment";
import { PlaylistTrack } from "../models/PlaylistTrack";
import { WebSocketService } from "./WebSocketService";

export interface RankingSnapshot {
  restaurantId: string;
  nextTrack: {
    id: string;
    youtubeVideoId: string;
    title: string;
    artist: string;
    voteCount: number; // Votação total (votos normais + peso PIX)
    originalVoteCount: number; // Votos normais apenas
    superVoteWeight: number; // Peso dos supervotos
    score: number;
    isPaid: boolean;
    paymentAmount?: number;
  } | null;
  paidQueue: Array<{
    id: string;
    youtubeVideoId: string;
    title: string;
    artist: string;
    voteCount: number; // Votação total
    originalVoteCount: number;
    superVoteWeight: number;
    paymentAmount: number;
  }>;
  freeQueue: Array<{
    id: string;
    youtubeVideoId: string;
    title: string;
    artist: string;
    voteCount: number; // Votação total
    originalVoteCount: number;
    superVoteWeight: number;
  }>;
  timestamp: Date;
}

export class SnapshotService {
  private suggestionRepository: Repository<Suggestion>;
  private paymentRepository: Repository<Payment>;
  private playlistTrackRepository: Repository<PlaylistTrack>;
  private webSocketService?: WebSocketService;
  private snapshots: Map<string, RankingSnapshot> = new Map();

  // Cache para evitar recálculos desnecessários
  private lastSnapshotTime: Map<string, number> = new Map();
  private readonly MIN_SNAPSHOT_INTERVAL = 5000; // 5 segundos mínimo entre snapshots

  constructor() {
    this.suggestionRepository = AppDataSource.getRepository(Suggestion);
    this.paymentRepository = AppDataSource.getRepository(Payment);
    this.playlistTrackRepository = AppDataSource.getRepository(PlaylistTrack);
  this.webSocketService = WebSocketService.getInstance();
  }

  async generateSnapshot(restaurantId: string, forceRefresh: boolean = false): Promise<RankingSnapshot> {
    try {
      // Verificar cache para evitar snapshots desnecessários
      const now = Date.now();
      const lastTime = this.lastSnapshotTime.get(restaurantId) || 0;

      if (!forceRefresh && (now - lastTime) < this.MIN_SNAPSHOT_INTERVAL) {
        const cached = this.snapshots.get(restaurantId);
        if (cached) {
          console.log(`📸 Using cached snapshot for restaurant ${restaurantId} (${now - lastTime}ms ago)`);
          return cached;
        }
      }

      this.lastSnapshotTime.set(restaurantId, now);
      // Get all active suggestions with votes and payments
      const suggestions = await this.suggestionRepository
        .createQueryBuilder("suggestion")
        .leftJoin("payments", "payment", "payment.suggestion_id = suggestion.id AND payment.status = 'paid'")
        .addSelect([
          "COALESCE(SUM(payment.amount), 0) as payment_amount",
          "COUNT(CASE WHEN payment.id IS NOT NULL THEN 1 END) > 0 as is_paid"
        ])
        .where("suggestion.restaurant_id = :restaurantId", { restaurantId })
        .andWhere("suggestion.status IN ('pending', 'approved')")
        .groupBy("suggestion.id")
        .getRawAndEntities();

      // Calcular score total (votos + peso do PIX) e ordenar por votação total
      const rankedSuggestions = suggestions.entities
        .map(suggestion => {
          const raw = suggestions.raw.find(r => r.suggestion_id === suggestion.id);
          const paymentAmount = parseFloat(raw?.payment_amount || '0');
          const isPaid = raw?.is_paid === true || paymentAmount > 0;

          // Calcular peso dos supervotos baseado no valor pago (mesma lógica do CollaborativePlaylistService)
          const superVoteWeight = isPaid ? this.calculateSuperVoteWeight(paymentAmount) : 0;
          const totalVoteCount = (suggestion.voteCount || 0) + superVoteWeight;

          return {
            id: suggestion.id,
            youtubeVideoId: suggestion.youtubeVideoId,
            title: this.sanitizeTitle(suggestion.title),
            artist: this.sanitizeTitle(suggestion.artist) || 'Artista Desconhecido',
            voteCount: totalVoteCount, // Votos normais + peso do PIX
            originalVoteCount: suggestion.voteCount || 0,
            superVoteWeight,
            score: this.calculateScore(suggestion, paymentAmount, totalVoteCount),
            isPaid,
            paymentAmount
          };
        })
        .sort((a, b) => {
          // Ordenar por votação total (votos + peso PIX), depois por data de criação
          if (b.voteCount !== a.voteCount) {
            return b.voteCount - a.voteCount;
          }

          // Em caso de empate, música mais antiga tem prioridade (FIFO)
          const aCreated = (suggestions.entities.find(s => s.id === a.id) as any)?.createdAt || new Date();
          const bCreated = (suggestions.entities.find(s => s.id === b.id) as any)?.createdAt || new Date();
          return new Date(aCreated).getTime() - new Date(bCreated).getTime();
        });

      // Separar em filas apenas para compatibilidade com interface (mas ordem é única por votação)
      const paidQueue = rankedSuggestions
        .filter(item => item.isPaid)
        .map(item => ({
          id: item.id,
          youtubeVideoId: item.youtubeVideoId,
          title: item.title,
          artist: item.artist,
          voteCount: item.voteCount,
          originalVoteCount: item.originalVoteCount,
          superVoteWeight: item.superVoteWeight,
          paymentAmount: item.paymentAmount
        }));

      const freeQueue = rankedSuggestions
        .filter(item => !item.isPaid)
        .map(item => ({
          id: item.id,
          youtubeVideoId: item.youtubeVideoId,
          title: item.title,
          artist: item.artist,
          voteCount: item.voteCount,
          originalVoteCount: item.originalVoteCount,
          superVoteWeight: item.superVoteWeight
        }));

      // Determinar próxima música priorizando a FILA AUTORITATIVA
      let nextTrack: RankingSnapshot["nextTrack"] = null;
      try {
        const { playbackService } = await import("./PlaybackService");
        const queueNext = await playbackService.getNextTrack(restaurantId);
        if (queueNext && queueNext.youtubeVideoId) {
          // Tentar casar com rankedSuggestions para enriquecer com votos/supervoto
          const rankedMatch = rankedSuggestions.find(r => r.id === (queueNext as any).id);
          if (rankedMatch) {
            nextTrack = {
              id: rankedMatch.id,
              youtubeVideoId: rankedMatch.youtubeVideoId,
              title: this.sanitizeTitle(rankedMatch.title),
              artist: this.sanitizeTitle(rankedMatch.artist),
              voteCount: rankedMatch.voteCount,
              originalVoteCount: rankedMatch.originalVoteCount,
              superVoteWeight: rankedMatch.superVoteWeight,
              score: rankedMatch.score,
              isPaid: rankedMatch.isPaid,
              paymentAmount: rankedMatch.paymentAmount ?? 0
            };
          } else {
            // Sem enriquecimento de ranking: refletir a fila com valores padrão
            nextTrack = {
              id: (queueNext as any).id,
              youtubeVideoId: (queueNext as any).youtubeVideoId,
              title: this.sanitizeTitle((queueNext as any).title),
              artist: this.sanitizeTitle((queueNext as any).artist),
              voteCount: 0,
              originalVoteCount: 0,
              superVoteWeight: 0,
              score: 0,
              isPaid: false,
              paymentAmount: 0
            };
          }
        }
      } catch (e) {
        console.warn("SnapshotService: falha ao consultar próxima da fila autoritativa:", e);
      }

      // Fallback 1: usar ranking (se houver)
      if (!nextTrack && rankedSuggestions.length > 0) {
        const top = rankedSuggestions[0];
        nextTrack = {
          id: top.id,
          youtubeVideoId: top.youtubeVideoId,
          title: this.sanitizeTitle(top.title),
          artist: this.sanitizeTitle(top.artist),
          voteCount: top.voteCount,
          originalVoteCount: top.originalVoteCount,
          superVoteWeight: top.superVoteWeight,
          score: top.score,
          isPaid: top.isPaid,
          paymentAmount: top.paymentAmount ?? 0
        };
      }

      // Fallback 2: base playlist (quando não há ranking nem fila)
      if (!nextTrack) {
        const baseTrack = await this.playlistTrackRepository
          .createQueryBuilder("track")
          .innerJoin("track.playlist", "playlist")
          .where("playlist.restaurant_id = :restaurantId", { restaurantId })
          .andWhere("track.is_active = true")
          .orderBy("track.position", "ASC")
          .getOne();

        if (baseTrack) {
          nextTrack = {
            id: baseTrack.id,
            youtubeVideoId: baseTrack.youtubeVideoId,
            title: this.sanitizeTitle(baseTrack.title),
            artist: this.sanitizeTitle(baseTrack.artist),
            voteCount: 0,
            originalVoteCount: 0,
            superVoteWeight: 0,
            score: 0,
            isPaid: false,
            paymentAmount: 0
          };
        }
      }

      const snapshot: RankingSnapshot = {
        restaurantId,
        nextTrack,
        paidQueue,
        freeQueue,
        timestamp: new Date()
      };

      // Store snapshot
      this.snapshots.set(restaurantId, snapshot);

      // Broadcast to connected clients (se disponível)
      console.log(`[SNAPSHOT WS] Emitindo ranking-snapshot: ${nextTrack?.title || 'None'} (${nextTrack?.youtubeVideoId || 'N/A'})`);
      this.webSocketService?.broadcastToRestaurant(restaurantId, 'ranking-snapshot', {
        nextTrack: snapshot.nextTrack,
        paidQueue: snapshot.paidQueue.slice(0, 10), // Limit for performance
        freeQueue: snapshot.freeQueue.slice(0, 20),
        timestamp: snapshot.timestamp
      });

      console.log(`📸 Generated snapshot for restaurant ${restaurantId}:`, {
        nextTrack: nextTrack?.title || 'None',
        nextTrackArtist: nextTrack?.artist || 'None',
        nextTrackId: nextTrack?.youtubeVideoId || 'None',
        nextTrackVotes: nextTrack?.voteCount || 0,
        nextTrackOriginalVotes: nextTrack?.originalVoteCount || 0,
        nextTrackSuperVoteWeight: nextTrack?.superVoteWeight || 0,
        totalSuggestions: rankedSuggestions.length,
        paidCount: paidQueue.length,
        freeCount: freeQueue.length
      });

      // Log any problematic titles for debugging
      if (nextTrack?.title && (nextTrack.title.includes('|') || nextTrack.title.includes('"') || nextTrack.title.includes("'"))) {
        console.log(`⚠️  Special characters detected in next track: "${nextTrack.title}" by "${nextTrack.artist}"`);
      }

      // Log top 3 do ranking para debug
      if (rankedSuggestions.length > 0) {
        console.log(`🏆 Top 3 ranking por votação:`,
          rankedSuggestions.slice(0, 3).map((item, index) => ({
            position: index + 1,
            title: item.title,
            totalVotes: item.voteCount,
            originalVotes: item.originalVoteCount,
            superVoteWeight: item.superVoteWeight,
            isPaid: item.isPaid
          }))
        );
      }

      return snapshot;
    } catch (error) {
      console.error(`❌ Error generating snapshot for restaurant ${restaurantId}:`, {
        error: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined,
        restaurantId
      });

      // Return empty snapshot instead of throwing to prevent service disruption
      const emptySnapshot: RankingSnapshot = {
        restaurantId,
        nextTrack: null,
        paidQueue: [],
        freeQueue: [],
        timestamp: new Date()
      };

      this.snapshots.set(restaurantId, emptySnapshot);
      return emptySnapshot;
    }
  }

  private sanitizeTitle(title: string | null | undefined): string {
    if (!title) return '';

    try {
      // Handle encoding issues and normalize the string
      let sanitized = title.toString();

      // Fix common encoding issues
      sanitized = sanitized
        .replace(/Ã£/g, 'ã')
        .replace(/Ã¡/g, 'á')
        .replace(/Ã©/g, 'é')
        .replace(/Ã­/g, 'í')
        .replace(/Ã³/g, 'ó')
        .replace(/Ãº/g, 'ú')
        .replace(/Ã§/g, 'ç')
        .replace(/Ã /g, 'à')
        .replace(/Ã¢/g, 'â')
        .replace(/Ãª/g, 'ê')
        .replace(/Ã´/g, 'ô');

      // Normalize Unicode characters
      sanitized = sanitized.normalize('NFC');

      // Escape problematic characters for JSON/SQL safety
      sanitized = sanitized
        .replace(/\\/g, '\\\\')  // Escape backslashes
        .replace(/"/g, '\\"')    // Escape double quotes
        .replace(/\n/g, '\\n')   // Escape newlines
        .replace(/\r/g, '\\r')   // Escape carriage returns
        .replace(/\t/g, '\\t');  // Escape tabs

      // Trim and limit length to prevent issues
      sanitized = sanitized.trim();
      if (sanitized.length > 500) {
        sanitized = sanitized.substring(0, 497) + '...';
      }

      return sanitized;
    } catch (error) {
      console.error('Error sanitizing title:', error, 'Original title:', title);
      return String(title || '').substring(0, 500);
    }
  }

  /**
   * Calcular peso do supervoto baseado no valor pago (mesma lógica do CollaborativePlaylistService)
   */
  private calculateSuperVoteWeight(paymentAmount: number): number {
    // Pesos definidos: R$5=10 votos, R$20=50 votos, R$50=150 votos
    if (paymentAmount >= 50) return 150; // R$ 50+ = 150 votos
    if (paymentAmount >= 20) return 50;  // R$ 20+ = 50 votos
    if (paymentAmount >= 5) return 10;   // R$ 5+  = 10 votos
    return 1; // Voto normal
  }

  private calculateScore(suggestion: Suggestion, paymentAmount: number, totalVoteCount: number): number {
    // Score baseado na votação total (votos normais + peso do PIX)
    // Não há mais boost artificial para músicas pagas - tudo é baseado em votação
    const baseScore = totalVoteCount;

    // Aplicar decay temporal para evitar que músicas antigas dominem
    const created = (suggestion as any).createdAt ?? (suggestion as any).updatedAt ?? new Date();
    let createdMs: number;
    try {
      const t = new Date(created as any).getTime();
      createdMs = isFinite(t) ? t : Date.now();
    } catch {
      createdMs = Date.now();
    }
    const hoursOld = (Date.now() - createdMs) / (1000 * 60 * 60);
    const timeDecay = Math.exp(-hoursOld / 48); // Decay mais suave (48 horas)

    return baseScore * timeDecay;
  }

  getSnapshot(restaurantId: string): RankingSnapshot | null {
    return this.snapshots.get(restaurantId) || null;
  }

  async startPeriodicSnapshots(): Promise<void> {
    console.log('🔄 Starting periodic snapshot generation (every 5 minutes)');
    
    setInterval(async () => {
      try {
        // Get all active restaurants
        const restaurants = await AppDataSource.query(`
          SELECT DISTINCT r.id
          FROM restaurants r
          INNER JOIN suggestions s ON s.restaurant_id = r.id
          WHERE r."isActive" = true
          AND s.status IN ('pending', 'approved')
          AND COALESCE(s."createdAt", s.created_at) > NOW() - INTERVAL '24 hours'
        `);

        for (const restaurant of restaurants) {
          await this.generateSnapshot(restaurant.id);
        }
      } catch (error) {
        console.error('Error in periodic snapshot generation:', error);
      }
    }, 5 * 60 * 1000); // 5 minutes
  }

  async generateSnapshotForAllActiveRestaurants(): Promise<void> {
    try {
      const restaurants = await AppDataSource.query(`
        SELECT DISTINCT r.id
        FROM restaurants r
        WHERE r."isActive" = true
      `);

      for (const restaurant of restaurants) {
        await this.generateSnapshot(restaurant.id);
      }
    } catch (error) {
      console.error('Error generating snapshots for all restaurants:', error);
    }
  }
}

// Singleton instance
export const snapshotService = new SnapshotService();
