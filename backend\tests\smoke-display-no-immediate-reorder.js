// Smoke: após um supervoto, a ordem exibida/fila NÃO deve mudar imediatamente
// Critérios PASS:
// 1) Ordem da fila imediatamente após supervoto permanece igual
// 2) Nenhum evento 'reorderSelected' recebido em 10s

/* eslint-disable no-console */
const { io } = require('socket.io-client');

const BASE = process.env.BASE_URL || 'http://localhost:8001/api/v1';
const RESTAURANT_ID = process.env.RESTAURANT_ID || 'demo-restaurant';

const sleep = (ms) => new Promise((r) => setTimeout(r, ms));

async function getQueue() {
  const res = await fetch(`${BASE}/playback-queue/${RESTAURANT_ID}`);
  if (!res.ok) throw new Error(`queue HTTP ${res.status}`);
  const json = await res.json();
  const list = Array.isArray(json.queue) ? json.queue : [];
  const ids = list
    .map((it) => String(it.youtubeVideoId || it.suggestionId || it.id || ''))
    .map((s) => (s.match(/([a-zA-Z0-9_-]{11})/) || [,''])[1])
    .filter(Boolean);
  const cur = json.currentlyPlaying?.youtubeVideoId || json.currentlyPlaying?.id || null;
  return { ids, currentlyPlaying: cur };
}

async function supervote(videoId) {
  const body = {
    youtubeVideoId: videoId,
    paymentAmount: 20,
    paymentId: `smoke-${Date.now()}`,
  };
  const res = await fetch(`${BASE}/collaborative-playlist/${RESTAURANT_ID}/supervote`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(body),
  });
  const json = await res.json().catch(() => ({}));
  return { status: res.status, json };
}

async function main() {
  console.log('⏱️  Iniciando smoke: no-immediate-reorder');
  const before = await getQueue();
  if (!before.ids.length) throw new Error('Fila vazia');
  const curId = (before.currentlyPlaying || '').match(/([a-zA-Z0-9_-]{11})/)?.[1] || null;
  const target = before.ids.find((id) => id && id !== curId);
  if (!target) throw new Error('Sem candidato para supervoto (apenas 1 item na fila?)');
  console.log('Fila inicial (top 5):', before.ids.slice(0, 5));
  console.log('Atual:', curId, '| Alvo supervoto:', target);

  // Conectar WS e entrar na sala (melhor esforço)
  let gotReorderSelected = false;
  let gotQueueUpdate = false;
  const socket = io(BASE.replace('/api/v1', ''), { transports: ['websocket'], timeout: 5000 });
  socket.on('connect', () => {
    try { socket.emit('joinRestaurant', RESTAURANT_ID); } catch {}
    try { socket.emit('join-restaurant', RESTAURANT_ID); } catch {}
  });
  socket.on('reorderSelected', () => { gotReorderSelected = true; });
  socket.on('queue-update', () => { gotQueueUpdate = true; });

  // Disparar supervoto
  const sv = await supervote(target);
  console.log('Supervote resp:', sv.status, sv.json?.message || sv.json?.error || sv.json?.status || 'ok');
  if (sv.status >= 400) throw new Error('Falha ao aplicar supervoto');

  // Dar um pequeno tempo e checar fila novamente
  await sleep(2000);
  const after = await getQueue();
  console.log('Fila após 2s (top 5):', after.ids.slice(0, 5));

  const immediateChanged = JSON.stringify(before.ids) !== JSON.stringify(after.ids);

  // Aguardar mais alguns segundos para observar eventos
  await sleep(8000);
  socket.close();

  const pass = !immediateChanged && !gotReorderSelected;
  if (pass) {
    console.log('✅ PASS: Sem reordenação imediata nem reorderSelected logo após supervoto.');
    process.exit(0);
  } else {
    console.log('❌ FAIL:', {
      immediateChanged,
      gotReorderSelected,
      gotQueueUpdate,
    });
    process.exit(2);
  }
}

// Node 18+ tem fetch global; para versões antigas, usar node --experimental-fetch
if (typeof fetch !== 'function') {
  console.error('Node fetch indisponível. Use Node 18+ ou rode com --experimental-fetch');
  process.exit(3);
}

main().catch((err) => {
  console.error('Erro no smoke:', err?.message || err);
  process.exit(1);
});
