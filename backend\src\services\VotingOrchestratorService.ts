import { Repository } from "typeorm";
import { AppDataSource } from "../config/database";
import { Restaurant } from "../models/Restaurant";
import { Suggestion, SuggestionStatus } from "../models/Suggestion";
import { collaborativePlaylistService } from "./CollaborativePlaylistService";
import { playbackService } from "./PlaybackService";

interface RoundState {
  startedAt: number; // epoch ms
  lastWinnerYoutubeId?: string;
}

/**
 * Orquestrador de Votação (única autoridade):
 * - Em cada restaurante ativo, inicia rodadas de 5 minutos.
 * - Ao final de cada rodada, escolhe a música com maior votação (ranking) e a coloca como PRÓXIMA na fila autoritativa.
 * - Após tocar, o PlaybackService já marca como completed e zera votos (comportamento existente).
 * - Enquanto a rodada está em andamento, o display segue a playlist/estado atual (sem interferência).
 */
export class VotingOrchestratorService {
  private static instance: VotingOrchestratorService;
  private restaurantRepository: Repository<Restaurant>;
  private suggestionRepository: Repository<Suggestion>;
  private tickTimer: NodeJS.Timeout | null = null;
  private rounds: Map<string, RoundState> = new Map();

  // Duração padrão de rodada: 5 minutos
  private readonly ROUND_MINUTES = 5;
  // Frequência de verificação: a cada 15s para ser responsivo sem sobrecarregar
  private readonly TICK_MS = 15_000;

  private constructor() {
    this.restaurantRepository = AppDataSource.getRepository(Restaurant);
    this.suggestionRepository = AppDataSource.getRepository(Suggestion);
  }

  static getInstance(): VotingOrchestratorService {
    if (!VotingOrchestratorService.instance) {
      VotingOrchestratorService.instance = new VotingOrchestratorService();
    }
    return VotingOrchestratorService.instance;
  }

  async start(): Promise<void> {
    // Inicializa estados de rodada para restaurantes ativos
    const restaurants = await this.restaurantRepository.find({ where: { isActive: true } });
    const now = Date.now();
    for (const r of restaurants) {
      if (!this.rounds.has(r.id)) {
        this.rounds.set(r.id, { startedAt: now });
      }
    }

    // Inicia o loop de verificação
    if (this.tickTimer) return;
    this.tickTimer = setInterval(() => {
      this.tick().catch((e) => console.error("VotingOrchestrator tick error:", e));
    }, this.TICK_MS);
    console.log("🚦 VotingOrchestratorService iniciado (rodadas de 5 minutos)");
  }

  stop(): void {
    if (this.tickTimer) {
      clearInterval(this.tickTimer);
      this.tickTimer = null;
    }
    console.log("🛑 VotingOrchestratorService parado");
  }

  getStatus(): {
    isRunning: boolean;
    nextRoundEnd?: Date;
    rounds: Record<string, { startedAt: Date; endsAt: Date; lastWinnerYoutubeId?: string }>;
  } {
    const isRunning = !!this.tickTimer;
    const rounds: Record<string, any> = {};
    const now = Date.now();
    for (const [restaurantId, round] of this.rounds.entries()) {
      const endsAt = new Date(round.startedAt + this.ROUND_MINUTES * 60000);
      rounds[restaurantId] = {
        startedAt: new Date(round.startedAt),
        endsAt,
        lastWinnerYoutubeId: round.lastWinnerYoutubeId,
      };
    }
    // próxima execução é o menor endsAt futuro, se houver
    const next = Object.values(rounds)
      .map((r: any) => r.endsAt.getTime())
      .filter((t) => t > now)
      .sort((a, b) => a - b)[0];

    return { isRunning, nextRoundEnd: next ? new Date(next) : undefined, rounds };
  }

  /**
   * Enfileira imediatamente uma sugestão como PRÓXIMA na fila autoritativa,
   * sem esperar a rodada terminar. Útil para casos especiais (ex: paga).
   */
  async enqueueImmediateNext(
    restaurantId: string,
    suggestionId: string
  ): Promise<void> {
    // Atualiza lastWinner para reduzir risco de repetição na rodada
    try {
      const s = await this.suggestionRepository.findOne({ where: { id: suggestionId } });
      if (s?.youtubeVideoId) {
        const current = this.rounds.get(restaurantId) || { startedAt: Date.now() };
        this.rounds.set(restaurantId, { ...current, lastWinnerYoutubeId: s.youtubeVideoId });
      }
    } catch {}
    await playbackService.enqueueAsNext(restaurantId, suggestionId);
  }

  private async tick(): Promise<void> {
    const restaurants = await this.restaurantRepository.find({ where: { isActive: true } });
    const now = Date.now();
    for (const r of restaurants) {
      const round = this.rounds.get(r.id) ?? { startedAt: now };
      const elapsedMin = (now - round.startedAt) / 60000;
      if (elapsedMin >= this.ROUND_MINUTES) {
        await this.finalizeRound(r.id, round);
        // reinicia a rodada
        this.rounds.set(r.id, { startedAt: Date.now() });
      } else {
        // mantém estado
        this.rounds.set(r.id, round);
      }
    }
  }

  private async finalizeRound(restaurantId: string, round: RoundState): Promise<void> {
    try {
      // Obtém ranking atual (já ponderado por supervoto)
      const ranking = await collaborativePlaylistService.getVotingRanking(restaurantId, 20);
      if (!ranking.success || !ranking.data || ranking.data.length === 0) {
        return;
      }

      // Escolhe a primeira com votos > 0 e diferente do último vencedor
      const winner = ranking.data.find(
        (i) => (i.voteCount ?? 0) > 0 && i.youtubeVideoId !== round.lastWinnerYoutubeId
      ) || ranking.data[0];

      if (!winner || (winner.voteCount ?? 0) <= 0) {
        return; // nada a tocar nesta rodada
      }

      // Localiza uma Suggestion válida para o vídeo vencedor
      const suggestion = await this.suggestionRepository
        .createQueryBuilder("s")
        .where("s.restaurant_id = :restaurantId", { restaurantId })
        .andWhere("s.youtube_video_id = :yid", { yid: winner.youtubeVideoId })
        .andWhere("s.status IN (:...st)", { st: [SuggestionStatus.PENDING, SuggestionStatus.APPROVED, SuggestionStatus.PLAYING] })
        .andWhere("s.completed_at IS NULL")
        .orderBy("s.vote_count", "DESC")
        .getOne();

      if (!suggestion) {
        return;
      }

      // Coloca como PRÓXIMA na fila autoritativa (sem interromper a atual)
      console.log(`[ORCHESTRATOR] Enfileirando próxima: ${winner.title} (${winner.youtubeVideoId}) - votos: ${winner.voteCount}`);
      await playbackService.enqueueAsNext(restaurantId, suggestion.id);

      // Atualiza último vencedor para evitar repetição imediata em próxima rodada
      const state = this.rounds.get(restaurantId) || { startedAt: Date.now() };
      this.rounds.set(restaurantId, { ...state, lastWinnerYoutubeId: winner.youtubeVideoId });
    } catch (error) {
      console.error("Erro ao finalizar rodada de votação:", error);
    }
  }
}

export const votingOrchestratorService = VotingOrchestratorService.getInstance();
