import { AppDataSource } from "../config/database";
import { Suggestion, SuggestionStatus } from "../models/Suggestion";
import { Restaurant } from "../models/Restaurant";
import { ClientSession } from "../models/ClientSession";
import { QueueItem as QueueItemEntity } from "../models/QueueItem";
import { redisClient } from "../config/redis";
import {
  notificationService,
  NotificationType,
  NotificationPriority,
} from "./NotificationService";
import { YouTubeService } from "./YouTubeService";
import { WebSocketService } from "./WebSocketService";
import { collaborativePlaylistService } from "./CollaborativePlaylistService";
import { playbackService } from "./PlaybackService";

export interface QueueItemDTO {
  id: string;
  suggestionId: string;
  title: string;
  artist: string;
  duration: number;
  thumbnailUrl?: string;
  youtubeVideoId: string;
  isPaid: boolean;
  paymentAmount?: number;
  clientName?: string;
  tableName?: string;
  sessionId: string;
  addedAt: Date;
  estimatedPlayTime?: Date;
  position: number;
  priority: number; // 1 = highest (paid), 2 = normal (free)
  likeCount: number; // Número de votos/likes da música
}

export interface QueueStats {
  totalItems: number;
  paidItems: number;
  freeItems: number;
  totalDuration: number;
  estimatedWaitTime: number;
  currentlyPlaying?: QueueItemDTO;
  nextUp?: QueueItemDTO;
}

class PlaybackQueueService {
  private suggestionRepository = AppDataSource.getRepository(Suggestion);
  private restaurantRepository = AppDataSource.getRepository(Restaurant);
  private sessionRepository = AppDataSource.getRepository(ClientSession);
  private queueItemRepository = AppDataSource.getRepository(QueueItemEntity);
  private youtubeService = new YouTubeService();
  private ws = WebSocketService.getInstance();
  // Cache simples por restaurante para aliviar carga das consultas da fila
  // TTL curto para manter frescor sem sobrecarregar: 1500ms
  private queueCache: Map<string, { data: {
    queue: QueueItemDTO[];
    stats: QueueStats;
    currentlyPlaying?: QueueItemDTO;
  }; expiresAt: number; inFlight?: Promise<any> } > = new Map();
  // Throttle de broadcasts para evitar tempestade de eventos
  private lastBroadcastAt: Map<string, number> = new Map();
  private readonly BROADCAST_MIN_INTERVAL_MS = 800;
  private readonly QUEUE_CACHE_TTL_MS = 1500;

  // Emite para os clients qual é o próximo vídeo autoritativo (primeiro da fila)
  private async broadcastSelectedNext(restaurantId: string): Promise<void> {
    try {
      // Throttle
      const now = Date.now();
      const last = this.lastBroadcastAt.get(restaurantId) || 0;
      if (now - last < this.BROADCAST_MIN_INTERVAL_MS) return;
      this.lastBroadcastAt.set(restaurantId, now);

      const { queue } = await this.getPlaybackQueue(restaurantId);
      const next = queue[0];
      if (!next) return;

      await this.ws?.emitToRestaurant(restaurantId, "reorderSelected", {
        video: {
          suggestionId: next.suggestionId,
          youtubeVideoId: next.youtubeVideoId,
          title: next.title,
          artist: next.artist,
          duration: next.duration,
          thumbnailUrl: next.thumbnailUrl,
          isPaid: next.isPaid,
          paymentAmount: next.paymentAmount,
          position: next.position,
        },
        timestamp: new Date().toISOString(),
      });
    } catch (e) {
      console.warn("Falha ao emitir reorderSelected:", e);
    }
  }

  // Obter fila de reprodução completa com cache leve (baseada na playlist ativa; votos apenas reordenam)
  async getPlaybackQueue(restaurantId: string): Promise<{
    queue: QueueItemDTO[];
    stats: QueueStats;
    currentlyPlaying?: QueueItemDTO;
  }> {
    // Retornar do cache se válido
    const cached = this.queueCache.get(restaurantId);
    const now = Date.now();
    if (cached && cached.expiresAt > now) {
      return cached.data;
    }
    // Evitar concorrência: se já houver uma construção em andamento, aguardar
    if (cached?.inFlight) {
      try { await cached.inFlight; } catch {}
      const again = this.queueCache.get(restaurantId);
      if (again && again.expiresAt > Date.now()) return again.data;
    }
    const buildPromise = this.buildPlaybackQueueInternal(restaurantId)
      .then((res) => {
        this.queueCache.set(restaurantId, {
          data: res,
          expiresAt: Date.now() + this.QUEUE_CACHE_TTL_MS,
        });
        return res;
      })
      .finally(() => {
        const entry = this.queueCache.get(restaurantId);
        if (entry) this.queueCache.set(restaurantId, { ...entry, inFlight: undefined });
      });
    this.queueCache.set(restaurantId, {
      data: cached?.data || { queue: [], stats: { totalItems: 0, paidItems: 0, freeItems: 0, totalDuration: 0, estimatedWaitTime: 0 } },
      expiresAt: now, // expirado; será atualizado quando a promessa concluir
      inFlight: buildPromise,
    });
    return buildPromise;
  }

  // Constrói a fila de reprodução (sem cache)
  private async buildPlaybackQueueInternal(restaurantId: string): Promise<{
    queue: QueueItemDTO[];
    stats: QueueStats;
    currentlyPlaying?: QueueItemDTO;
  }> {
    try {
      // Basear na playlist ativa
      const active = await collaborativePlaylistService.getActivePlaylistForRestaurant(restaurantId);
      if (!active || !Array.isArray((active as any).tracks)) {
        return {
          queue: [],
          stats: {
            totalItems: 0,
            paidItems: 0,
            freeItems: 0,
            totalDuration: 0,
            estimatedWaitTime: 0,
          },
        };
      }

      const tracks = ((active as any).tracks || []) as Array<{
        youtubeVideoId: string;
        title?: string;
        artist?: string;
        duration?: number; // pode vir em minutos; preferir formattedDuration
        formattedDuration?: string; // ex: "2:52"
        thumbnailUrl?: string;
      }>;

      // Deduplicar por vídeo preservando a primeira ocorrência (ordem da playlist)
      const seen = new Set<string>();
      const uniqueTracks = tracks.filter((t) => {
        const vid = t.youtubeVideoId;
        if (!vid || seen.has(vid)) return false;
        seen.add(vid);
        return true;
      });

      // Sugestões com votos para vídeos da playlist (para reordenar e sinalizar paga)
      // CORREÇÃO: Incluir sugestões 'completed' e expandir período para últimos 7 dias
      const ids = uniqueTracks.map((t) => t.youtubeVideoId).filter(Boolean);
      const todays = ids.length
        ? await this.suggestionRepository
            .createQueryBuilder("s")
            .leftJoinAndSelect("s.clientSession", "clientSession")
            .leftJoinAndSelect("s.restaurant", "restaurant")
            .where("restaurant.id = :restaurantId", { restaurantId })
            .andWhere("s.status = :approvedStatus", { approvedStatus: "approved" })
            .andWhere("s.createdAt >= CURRENT_DATE - INTERVAL '7 days'")
            .andWhere("s.youtubeVideoId IN (:...ids)", { ids })
            .getMany()
        : [];



      // Agrupar sugestões do dia por vídeo; somar peso; escolher representante (prioriza paga)
      const groupByVid = new Map<string, Suggestion[]>();
      for (const s of todays) {
        const arr = groupByVid.get(s.youtubeVideoId) || [];
        arr.push(s);
        groupByVid.set(s.youtubeVideoId, arr);
      }
      const pickRepresentative = (list: Suggestion): Suggestion => list;
      const repByVid = new Map<string, Suggestion>();
      const weightByVid = new Map<string, number>();
      for (const [vid, list] of groupByVid) {
        // representante: preferir paga; depois maior score; empate por mais recente
        const paid = list.filter((x) => !!x.isPaid);
        let rep: Suggestion;
        if (paid.length > 0) {
          paid.sort(
            (a, b) =>
              (b.upvotes || 0) - (a.upvotes || 0) ||
              (b.createdAt?.getTime?.() || 0) - (a.createdAt?.getTime?.() || 0)
          );
          rep = paid[0];
        } else {
          rep = [...list].sort(
            (a, b) =>
              ((b.upvotes || 0) - (b.downvotes || 0)) -
                ((a.upvotes || 0) - (a.downvotes || 0)) ||
              (b.createdAt?.getTime?.() || 0) - (a.createdAt?.getTime?.() || 0)
          )[0];
        }
        repByVid.set(vid, rep);
        const up = list.reduce((acc, x) => acc + (x.upvotes || 0), 0);
        const down = list.reduce((acc, x) => acc + (x.downvotes || 0), 0);
        weightByVid.set(vid, up - down);
      }

      // Utilitários de duração: preferir segundos válidos; senão, parse do formattedDuration; fallback 180s
      const parseDuration = (t?: { duration?: number; formattedDuration?: string }, s?: Suggestion) => {
        const fromSuggestion = typeof s?.duration === 'number' ? s!.duration! : undefined;
        const fromTrack = typeof t?.duration === 'number' ? t!.duration! : undefined;
        const isSane = (v?: number) => typeof v === 'number' && v >= 30; // >=30s como limiar
        if (isSane(fromSuggestion)) return fromSuggestion as number;
        if (isSane(fromTrack)) return fromTrack as number;
        const fd = t?.formattedDuration;
        if (fd && /^(\d{1,2}):(\d{2})$/.test(fd)) {
          const [mm, ss] = fd.split(":");
          const sec = Number(mm) * 60 + Number(ss);
          if (isSane(sec)) return sec;
        }
        return 180;
      };

      // Origem na playlist define ordem base; reordenar por peso dos votos do dia
      const origIndex = new Map<string, number>();
      uniqueTracks.forEach((t, i) => origIndex.set(t.youtubeVideoId, i));
      const sortedVideos = [...uniqueTracks].sort((a, b) => {
        const aw = weightByVid.get(a.youtubeVideoId) ?? 0;
        const bw = weightByVid.get(b.youtubeVideoId) ?? 0;
        if (aw !== bw) return bw - aw;
        return (origIndex.get(a.youtubeVideoId) || 0) - (origIndex.get(b.youtubeVideoId) || 0);
      });

      // Montar queue item por vídeo (sem duplicatas), sinalizando se há sugestão paga representativa
      const queue: QueueItemDTO[] = sortedVideos.map((t, idx) => {
        const rep = repByVid.get(t.youtubeVideoId);
        const durationSec = parseDuration(t as any, rep);
        return {
          id: `queue_${t.youtubeVideoId}`,
          suggestionId: rep?.id || `auto_${t.youtubeVideoId}`,
          title: rep?.title || t.title || `Vídeo ${t.youtubeVideoId}`,
          artist: rep?.artist || t.artist || "Artista Desconhecido",
          duration: durationSec,
          thumbnailUrl: rep?.thumbnailUrl || t.thumbnailUrl,
          youtubeVideoId: t.youtubeVideoId,
          isPaid: !!rep?.isPaid,
          paymentAmount: rep?.paymentAmount ? Number(rep.paymentAmount) : undefined,
          clientName: rep?.clientSession?.clientName || (rep as any)?.clientName,
          tableName: rep ? `Mesa ${rep.clientSession?.tableNumber || (rep as any)?.tableNumber || "?"}` : undefined,
          sessionId: rep?.clientSessionId || "",
          addedAt: rep?.createdAt || new Date(),
          position: idx + 1,
          priority: 2,
          likeCount: 0, // Será preenchido abaixo com dados reais
        };
      });

      // Anexar métricas de votos (da tabela suggestions)
      try {
        if (queue.length > 0) {
          const videoIds = queue.map(item => item.youtubeVideoId);

          // Buscar votos consolidados da tabela suggestions
          const votesQuery = await AppDataSource.query(`
            SELECT
              youtube_video_id,
              SUM(COALESCE(upvotes, 0)) as total_upvotes,
              SUM(COALESCE(downvotes, 0)) as total_downvotes
            FROM suggestions
            WHERE restaurant_id = $1 AND youtube_video_id = ANY($2)
            GROUP BY youtube_video_id
          `, [restaurantId, videoIds]);

          // Criar mapa de votos por videoId
          const votesMap = new Map();
          votesQuery.forEach((row: any) => {
            votesMap.set(row.youtube_video_id, {
              likeCount: parseInt(row.total_upvotes) || 0,
              dislikeCount: parseInt(row.total_downvotes) || 0
            });
          });

          // Aplicar votos aos itens da fila
          queue.forEach((item) => {
            const votes = votesMap.get(item.youtubeVideoId) || { likeCount: 0, dislikeCount: 0 };
            item.likeCount = votes.likeCount; // Agora tipado corretamente
          });
        }
      } catch (e) {
        console.warn("Falha ao anexar métricas de votos:", e);
      }

  // Calcular tempos estimados
      let cumulativeDuration = 0;
      queue.forEach((item, index) => {
        if (index === 0) {
          item.estimatedPlayTime = new Date();
        } else {
          item.estimatedPlayTime = new Date(
            Date.now() + cumulativeDuration * 1000
          );
        }
        cumulativeDuration += item.duration;
      });

      // Calcular estatísticas (preliminar; ajustaremos currentlyPlaying abaixo)
      const stats: QueueStats = {
        totalItems: queue.length,
        paidItems: queue.filter((item) => item.isPaid).length,
        freeItems: queue.filter((item) => !item.isPaid).length,
        totalDuration: queue.reduce((sum, item) => sum + item.duration, 0),
        estimatedWaitTime:
          queue.length > 0
            ? queue[queue.length - 1].estimatedPlayTime!.getTime() - Date.now()
            : 0,
        currentlyPlaying: undefined,
        nextUp: queue[1],
      };

      // Consultar estado real do PlaybackService para definir o currentlyPlaying autoritativo
      try {
        const state = await playbackService.getPlaybackState(restaurantId);
        const cur = state?.currentTrack;
        if (cur) {
          const mappedCur: QueueItemDTO = {
            id: `queue_${cur.youtubeVideoId}`,
            suggestionId: cur.id,
            title: cur.title,
            artist: cur.artist,
            duration: cur.duration || 0,
            thumbnailUrl: cur.thumbnailUrl,
            youtubeVideoId: cur.youtubeVideoId,
            isPaid: false,
            paymentAmount: undefined,
            clientName: undefined,
            tableName: undefined,
            sessionId: state?.sessionId || "",
            addedAt: new Date(),
            estimatedPlayTime: new Date(),
            position: 0,
            priority: 2,
            likeCount: 0, // Música atual - votos não aplicáveis
          };
          stats.currentlyPlaying = mappedCur;
        }
      } catch (e) {
        // Em caso de falha, manter fallback (sem current)
      }

  return { queue, stats, currentlyPlaying: stats.currentlyPlaying };
    } catch (error) {
      console.error("Erro ao obter fila de reprodução:", error);
      return {
        queue: [],
        stats: {
          totalItems: 0,
          paidItems: 0,
          freeItems: 0,
          totalDuration: 0,
          estimatedWaitTime: 0,
        },
      };
    }
  }

  // Adicionar música à fila (chamado após pagamento)
  async addToQueue(suggestionId: string): Promise<QueueItemDTO | null> {
    try {
      const suggestion = await this.suggestionRepository.findOne({
        where: { id: suggestionId },
  relations: ["restaurant", "clientSession"],
      });

      if (!suggestion) {
        return null;
      }

      // Marcar como aprovada e definir posição na fila
      const queuePosition = await this.getNextQueuePosition(
        suggestion.restaurant.id,
        suggestion.isPaid
      );

      // Usar update em vez de save para evitar problemas com triggers
      await this.suggestionRepository.update(suggestion.id, {
        status: SuggestionStatus.APPROVED,
        queuePosition: queuePosition,
      });

      // Atualizar o objeto local
      suggestion.status = SuggestionStatus.APPROVED;
      suggestion.queuePosition = queuePosition;

      // Criar item da fila no banco de dados
  const queueItem = this.queueItemRepository.create({
        restaurant: suggestion.restaurant,
        suggestion: suggestion,
        clientSession: suggestion.clientSession,
        youtubeVideoId: suggestion.youtubeVideoId,
        title: suggestion.title,
        artist: suggestion.artist,
        duration: suggestion.duration || 180,
        thumbnailUrl: suggestion.thumbnailUrl,
        position: suggestion.queuePosition || 0,
        priority: suggestion.isPaid ? 1 : 2,
        isPaid: suggestion.isPaid,
        paymentAmount: suggestion.paymentAmount,
        clientName: suggestion.clientSession?.clientName || suggestion.clientName,
        tableNumber: (() => {
          const raw = (suggestion.clientSession?.tableNumber as any) ?? (suggestion.tableNumber as any);
          if (raw === null || raw === undefined || raw === "") return undefined;
          if (typeof raw === 'number') return raw;
          const n = parseInt(String(raw), 10);
          return Number.isFinite(n) ? n : undefined;
        })(),
        sessionToken: suggestion.clientSessionId,
        addedAt: new Date(),
        status: "pending",
      });

      console.log("💾 Salvando item na fila:", queueItem.title);
      await this.queueItemRepository.save(queueItem);
      console.log("✅ Item salvo na fila com ID:", queueItem.id);

      // Notificar adição à fila
      await notificationService.sendToRestaurant(suggestion.restaurant.id, {
        type: NotificationType.MUSIC,
        title: "🎵 Nova música na fila",
        message: `"${suggestion.title}" ${
          suggestion.isPaid ? "(PAGA)" : ""
        } adicionada à fila`,
        priority: suggestion.isPaid
          ? NotificationPriority.HIGH
          : NotificationPriority.NORMAL,
        category: "queue",
        data: {
          queueItem,
          isPaid: suggestion.isPaid,
        },
      });

      // Notificar cliente
      await notificationService.sendToSession(suggestion.clientSessionId, {
        type: NotificationType.SUCCESS,
        title: "🎵 Música na fila!",
        message: `"${suggestion.title}" está na posição ${queueItem.position} da fila`,
        priority: NotificationPriority.NORMAL,
        category: "queue",
        data: {
          queueItem,
          estimatedWait: this.formatDuration(
            queueItem.estimatedPlayTime
              ? (queueItem.estimatedPlayTime.getTime() - Date.now()) / 1000
              : 0
          ),
        },
      });

  // Invalidar cache para o restaurante
  try { this.queueCache.delete(suggestion.restaurant.id); } catch {}
  return queueItem.toInterface();
    } catch (error) {
      console.error("Erro ao adicionar à fila:", error);
      return null;
    }
  }

  // Marcar música como tocando
  async markAsPlaying(suggestionId: string): Promise<void> {
    try {
      const suggestion = await this.suggestionRepository.findOne({
        where: { id: suggestionId },
  relations: ["restaurant", "clientSession"],
      });

      if (!suggestion) return;

      suggestion.status = SuggestionStatus.PLAYING;
      suggestion.playedAt = new Date();

      await this.suggestionRepository.save(suggestion);

      // Iniciar sessão de votação se for música paga
      if (suggestion.isPaid) {
        // Aqui você chamaria o serviço de votação competitiva
        // await competitiveVotingService.startVotingSession(suggestionId);
      }

      // Notificar que música começou
      await notificationService.sendToRestaurant(suggestion.restaurant.id, {
        type: NotificationType.MUSIC,
        title: "🎵 Tocando agora",
        message: `"${suggestion.title}" por ${suggestion.artist}`,
        priority: NotificationPriority.HIGH,
        category: "now_playing",
        data: {
          suggestionId,
          title: suggestion.title,
          artist: suggestion.artist,
          isPaid: suggestion.isPaid,
          duration: suggestion.duration,
        },
      });

      // Notificar cliente que sua música começou
  await notificationService.sendToSession(suggestion.clientSessionId, {
        type: NotificationType.SUCCESS,
        title: "🎵 Sua música está tocando!",
        message: `"${suggestion.title}" começou a tocar agora`,
        priority: NotificationPriority.HIGH,
        category: "now_playing",
        data: {
          suggestionId,
          canStartKaraoke: suggestion.isPaid,
        },
      });
  // Invalidar cache
  try { if (suggestion.restaurant?.id) this.queueCache.delete(suggestion.restaurant.id); } catch {}
    } catch (error) {
      console.error("Erro ao marcar como tocando:", error);
    }
  }

  // Marcar música como concluída
  async markAsCompleted(suggestionId: string): Promise<void> {
    try {
      const suggestion = await this.suggestionRepository.findOne({
        where: { id: suggestionId },
        relations: ["restaurant"],
      });

      if (!suggestion) return;

      suggestion.status = SuggestionStatus.COMPLETED;
      suggestion.completedAt = new Date();

      await this.suggestionRepository.save(suggestion);

      // Colocar música em cooldown (10 minutos) para não voltar ao ranking imediatamente
      try {
        if (suggestion.restaurant?.id && suggestion.youtubeVideoId) {
          const { collaborativePlaylistService } = await import('./CollaborativePlaylistService');
          await collaborativePlaylistService.markSongInCooldown(
            suggestion.restaurant.id,
            suggestion.youtubeVideoId,
            10
          );
        }
      } catch (e) {
        console.warn("Falha ao aplicar cooldown após conclusão:", e);
      }

      // Atualizar posições da fila
      await this.updateQueuePositions(suggestion.restaurant.id);

      // Notificar conclusão
      await notificationService.sendToRestaurant(suggestion.restaurant.id, {
        type: NotificationType.INFO,
        title: "🎵 Música concluída",
        message: `"${suggestion.title}" terminou de tocar`,
        priority: NotificationPriority.LOW,
        category: "completed",
        data: {
          suggestionId,
          title: suggestion.title,
        },
      });
  // Invalidar cache
  try { this.queueCache.delete(suggestion.restaurant.id); } catch {}
    } catch (error) {
      console.error("Erro ao marcar como concluída:", error);
    }
  }

  // Marcar música como tocando por youtubeVideoId
  async markVideoAsPlaying(
    restaurantId: string,
    youtubeVideoId: string
  ): Promise<void> {
    try {
      const suggestion = await this.suggestionRepository
        .createQueryBuilder("suggestion")
        .leftJoinAndSelect("suggestion.restaurant", "restaurant")
        .where("restaurant.id = :restaurantId", { restaurantId })
        .andWhere("suggestion.youtubeVideoId = :youtubeVideoId", {
          youtubeVideoId,
        })
        .andWhere("suggestion.status IN (:...st)", {
          st: [SuggestionStatus.APPROVED, SuggestionStatus.PLAYING],
        })
        .orderBy(
          `CASE WHEN suggestion.status = '${SuggestionStatus.APPROVED}' THEN 0 ELSE 1 END`,
          "ASC"
        )
        .addOrderBy("suggestion.createdAt", "ASC")
        .getOne();

      if (!suggestion) return;

      suggestion.status = SuggestionStatus.PLAYING;
      suggestion.playedAt = new Date();
  await this.suggestionRepository.save(suggestion);
  // Invalidar cache
  try { this.queueCache.delete(restaurantId); } catch {}
    } catch (error) {
      console.error("Erro ao marcar como tocando por vídeo:", error);
    }
  }

  // Marcar música como concluída por youtubeVideoId
  async markVideoAsCompleted(
    restaurantId: string,
    youtubeVideoId: string
  ): Promise<void> {
    try {
      const suggestion = await this.suggestionRepository
        .createQueryBuilder("suggestion")
        .leftJoinAndSelect("suggestion.restaurant", "restaurant")
        .where("restaurant.id = :restaurantId", { restaurantId })
        .andWhere("suggestion.youtubeVideoId = :youtubeVideoId", {
          youtubeVideoId,
        })
        .andWhere("suggestion.status IN (:...st)", {
          st: [SuggestionStatus.PLAYING, SuggestionStatus.APPROVED],
        })
        .orderBy(
          `CASE WHEN suggestion.status = '${SuggestionStatus.PLAYING}' THEN 0 ELSE 1 END`,
          "ASC"
        )
        .addOrderBy("suggestion.playedAt", "DESC")
        .addOrderBy("suggestion.createdAt", "ASC")
        .getOne();

      if (!suggestion) return;

      suggestion.status = SuggestionStatus.COMPLETED;
      suggestion.completedAt = new Date();
      await this.suggestionRepository.save(suggestion);

      // Colocar música em cooldown (10 minutos)
      try {
        const { collaborativePlaylistService } = await import('./CollaborativePlaylistService');
        await collaborativePlaylistService.markSongInCooldown(
          restaurantId,
          youtubeVideoId,
          10
        );
      } catch (e) {
        console.warn("Falha ao aplicar cooldown após conclusão (por vídeo):", e);
      }

      // Atualizar posições remanescentes
  await this.updateQueuePositions(restaurantId);
  // Invalidar cache
  try { this.queueCache.delete(restaurantId); } catch {}
    } catch (error) {
      console.error("Erro ao marcar como concluída por vídeo:", error);
    }
  }

  // Remover música da fila
  async removeFromQueue(
    suggestionId: string,
    reason: string = "removed"
  ): Promise<boolean> {
    try {
      const suggestion = await this.suggestionRepository.findOne({
        where: { id: suggestionId },
  relations: ["restaurant", "clientSession"],
      });

      if (!suggestion) return false;

      suggestion.status = SuggestionStatus.REJECTED;
      suggestion.rejectionReason = reason;

      await this.suggestionRepository.save(suggestion);

      // Atualizar posições da fila
  await this.updateQueuePositions(suggestion.restaurant.id);
  try { this.queueCache.delete(suggestion.restaurant.id); } catch {}

      // Notificar remoção
      await notificationService.sendToSession(suggestion.clientSessionId, {
        type: NotificationType.WARNING,
        title: "🚫 Música removida",
        message: `"${suggestion.title}" foi removida da fila`,
        priority: NotificationPriority.NORMAL,
        category: "queue",
        data: {
          suggestionId,
          reason,
        },
      });

      return true;
    } catch (error) {
      console.error("Erro ao remover da fila:", error);
      return false;
    }
  }

  // Obter próxima posição na fila
  private async getNextQueuePosition(
    restaurantId: string,
    isPaid: boolean
  ): Promise<number> {
    const query = this.suggestionRepository
      .createQueryBuilder("suggestion")
      .leftJoinAndSelect("suggestion.restaurant", "restaurant")
      .where("restaurant.id = :restaurantId", { restaurantId })
      .andWhere("suggestion.status = :status", { status: "approved" })
      .andWhere("suggestion.playedAt IS NULL");

    if (isPaid) {
      // Músicas pagas: inserir no final das pagas, antes das gratuitas
      query.andWhere("suggestion.isPaid = true");
    }

    const maxPosition = await query
      .select("MAX(suggestion.queuePosition)", "maxPosition")
      .getRawOne();

    return (maxPosition?.maxPosition || 0) + 1;
  }

  // Atualizar posições da fila após mudanças
  private async updateQueuePositions(restaurantId: string): Promise<void> {
    try {
      const suggestions = await this.suggestionRepository
        .createQueryBuilder("suggestion")
        .leftJoinAndSelect("suggestion.restaurant", "restaurant")
        .where("restaurant.id = :restaurantId", { restaurantId })
        .andWhere("suggestion.status = :status", { status: "approved" })
        .andWhere("suggestion.playedAt IS NULL")
        .orderBy("suggestion.isPaid", "DESC")
        .addOrderBy(
          "CASE WHEN DATE(suggestion.createdAt) = CURRENT_DATE THEN 0 ELSE 1 END",
          "ASC"
        )
        .addOrderBy("suggestion.createdAt", "ASC")
        .getMany();

      // Atualizar posições
      for (let i = 0; i < suggestions.length; i++) {
        suggestions[i].queuePosition = i + 1;
        await this.suggestionRepository.save(suggestions[i]);
      }
  // Após atualizar posições, informar o próximo autoritativo
  await this.broadcastSelectedNext(restaurantId);
  // Invalidar cache
  try { this.queueCache.delete(restaurantId); } catch {}
    } catch (error) {
      console.error("Erro ao atualizar posições da fila:", error);
    }
  }

  // Obter estimativa de tempo para uma posição
  async getEstimatedWaitTime(
    restaurantId: string,
    position: number
  ): Promise<number> {
    try {
      const { queue } = await this.getPlaybackQueue(restaurantId);

      if (position <= 0 || position > queue.length) {
        return 0;
      }

      // Somar duração de todas as músicas antes desta posição
      let totalDuration = 0;
      for (let i = 0; i < position - 1; i++) {
        totalDuration += queue[i].duration;
      }

      return totalDuration;
    } catch (error) {
      console.error("Erro ao calcular tempo de espera:", error);
      return 0;
    }
  }

  // Obter estatísticas da fila
  async getQueueStats(restaurantId: string): Promise<QueueStats> {
    try {
      const { stats } = await this.getPlaybackQueue(restaurantId);
      return stats;
    } catch (error) {
      console.error("Erro ao obter estatísticas da fila:", error);
      return {
        totalItems: 0,
        paidItems: 0,
        freeItems: 0,
        totalDuration: 0,
        estimatedWaitTime: 0,
      };
    }
  }

  // Reordenar fila (para admin)
  async reorderQueue(
    restaurantId: string,
    newOrder: string[]
  ): Promise<boolean> {
    try {
      for (let i = 0; i < newOrder.length; i++) {
        const suggestionId = newOrder[i];
        await this.suggestionRepository.update(
          { id: suggestionId },
          { queuePosition: i + 1 }
        );
      }

      // Notificar mudança na fila
      await notificationService.sendToRestaurant(restaurantId, {
        type: NotificationType.INFO,
        title: "🔄 Fila reordenada",
        message: "A ordem da fila foi alterada pelo administrador",
        priority: NotificationPriority.NORMAL,
        category: "queue",
        data: {
          newOrder,
        },
      });

  // Emite o próximo vídeo autoritativo após reordenar
  await this.broadcastSelectedNext(restaurantId);
  try { this.queueCache.delete(restaurantId); } catch {}

      return true;
    } catch (error) {
      console.error("Erro ao reordenar fila:", error);
      return false;
    }
  }

  // Utilitário para formatar duração
  private formatDuration(seconds: number): string {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.floor(seconds % 60);

    if (minutes > 60) {
      const hours = Math.floor(minutes / 60);
      const remainingMinutes = minutes % 60;
      return `${hours}h ${remainingMinutes}m`;
    }

    return `${minutes}m ${remainingSeconds}s`;
  }

  // Limpar fila (remover músicas antigas)
  async clearOldQueue(
    restaurantId: string,
    olderThanHours: number = 24
  ): Promise<number> {
    try {
      const cutoffDate = new Date(Date.now() - olderThanHours * 60 * 60 * 1000);

      const result = await this.suggestionRepository
        .createQueryBuilder()
        .update(Suggestion)
        .set({ status: SuggestionStatus.EXPIRED })
        .where("restaurant.id = :restaurantId", { restaurantId })
        .andWhere("status = :status", { status: "approved" })
        .andWhere("createdAt < :cutoffDate", { cutoffDate })
        .andWhere("playedAt IS NULL")
        .execute();

      return result.affected || 0;
    } catch (error) {
      console.error("Erro ao limpar fila antiga:", error);
      return 0;
    }
  }

  // Método combinado: criar sugestão e adicionar à fila
  async createSuggestionAndAddToQueue(suggestionData: {
    youtubeVideoId: string;
    title: string;
    artist: string;
    restaurantId: string;
    isPaid?: boolean;
    clientSessionId: string;
    status?: string;
  }): Promise<QueueItemDTO | null> {
    console.log("🎵 Iniciando createSuggestionAndAddToQueue", { suggestionData });
    try {
      // Verificar se restaurante existe
      console.log("🏪 Verificando restaurante:", suggestionData.restaurantId);
      const restaurant = await this.restaurantRepository.findOne({
        where: { id: suggestionData.restaurantId, isActive: true },
      });

      if (!restaurant) {
        console.error("❌ Restaurante não encontrado:", suggestionData.restaurantId);
        throw new Error("Restaurante não encontrado ou inativo");
      }
      console.log("✅ Restaurante encontrado:", restaurant.name);

      // Verificar se sessão existe
      let session = await this.sessionRepository.findOne({
        where: {
          sessionToken: suggestionData.clientSessionId,
          restaurant: { id: suggestionData.restaurantId },
        },
      });

      if (!session) {
        // Criar sessão se não existir
        session = this.sessionRepository.create({
          sessionToken: suggestionData.clientSessionId,
          restaurant,
          // Usar um IP válido para o tipo inet; "unknown" causa erro no Postgres
          ipAddress: "0.0.0.0",
          userAgent: "unknown",
          lastActivity: new Date(),
          isActive: true,
        });
        await this.sessionRepository.save(session);
      }

      // Buscar informações do vídeo no YouTube
      let videoInfo;
      try {
        videoInfo = await this.youtubeService.getVideoInfo(
          suggestionData.youtubeVideoId
        );
      } catch (error) {
        console.warn(
          "Erro ao buscar info do YouTube, usando dados fornecidos:",
          error
        );
        videoInfo = {
          title: suggestionData.title,
          artist: suggestionData.artist,
          duration: 180, // 3 minutos padrão
          thumbnailUrl: `https://img.youtube.com/vi/${suggestionData.youtubeVideoId}/mqdefault.jpg`,
        };
      }

      // Impedir inclusão se música não faz parte da playlist ativa
      try {
        const active = await collaborativePlaylistService.getActivePlaylistForRestaurant(suggestionData.restaurantId);
        const allowedIds = new Set<string>((active?.tracks || []).map((t: any) => t.youtubeVideoId));
        if (allowedIds.size > 0 && !allowedIds.has(suggestionData.youtubeVideoId)) {
          throw new Error("Esta música não faz parte da playlist ativa do restaurante");
        }
      } catch (e) {
        console.warn("Restrição de playlist ativa ao criar sugestão:", e?.message || e);
        throw e;
      }

      // Criar sugestão
      const suggestion = new Suggestion();
      suggestion.youtubeVideoId = suggestionData.youtubeVideoId;
      suggestion.title = videoInfo.title || suggestionData.title;
      suggestion.artist = videoInfo.artist || suggestionData.artist;
      suggestion.duration = videoInfo.duration || 180;
      suggestion.thumbnailUrl = videoInfo.thumbnailUrl;
      suggestion.clientSessionId = suggestionData.clientSessionId;
      suggestion.status =
        (suggestionData.status as any) || SuggestionStatus.APPROVED;
      // Garantir booleano válido para coluna is_paid (evitar valores como {})
      const rawPaid: any = (suggestionData as any).isPaid;
      suggestion.isPaid =
        rawPaid === true ||
        rawPaid === "true" ||
        rawPaid === 1 ||
        rawPaid === "1";
      suggestion.source = "client" as any;
      suggestion.createdAt = new Date();
      suggestion.updatedAt = new Date();

      // Definir as relações
      suggestion.restaurant = restaurant;
      suggestion.sessionId = session.id;

      console.log("💾 Salvando sugestão:", suggestion.title);
      await this.suggestionRepository.save(suggestion);
      console.log("✅ Sugestão salva com ID:", suggestion.id);

      // Adicionar à fila
      console.log("🎵 Adicionando à fila:", suggestion.id);
      const queueItem = await this.addToQueue(suggestion.id);
      console.log("✅ Item adicionado à fila:", queueItem ? "sucesso" : "falhou");
      return queueItem;
    } catch (error) {
      console.error("Erro ao criar sugestão e adicionar à fila:", error);
      console.error("Stack trace:", error instanceof Error ? error.stack : error);
      console.error("Dados da sugestão:", suggestionData);
      throw error; // Re-throw para que o erro seja tratado adequadamente
    }
  }

  // Promover música para fila prioritária
  async promoteTrack(restaurantId: string, trackId: string): Promise<void> {
    try {
      // Buscar a sugestão
      const suggestion = await this.suggestionRepository.findOne({
        where: {
          id: trackId,
          restaurant: { id: restaurantId },
        },
        relations: ["restaurant"],
      });

      if (!suggestion) {
        throw new Error("Música não encontrada");
      }

      // Marcar como paga (prioridade)
      suggestion.isPaid = true;
      suggestion.paymentAmount = 2.0; // Valor padrão para promoção
      await this.suggestionRepository.save(suggestion);

      // Atualizar cache da fila
      const queueKey = `queue:${restaurantId}`;
      const cachedQueue = await redisClient.getClient().get(queueKey);

      if (cachedQueue) {
  const queue: QueueItemDTO[] = JSON.parse(cachedQueue);
        const trackIndex = queue.findIndex(
          (item) => item.suggestionId === trackId
        );

        if (trackIndex !== -1) {
          // Atualizar item na fila
          queue[trackIndex].isPaid = true;
          queue[trackIndex].paymentAmount = 2.0;
          queue[trackIndex].priority = 1; // Alta prioridade

          // Reordenar fila (prioridade primeiro)
          queue.sort((a, b) => a.priority - b.priority);

          // Atualizar posições
          queue.forEach((item, index) => {
            item.position = index + 1;
          });

          // Salvar fila atualizada
          await redisClient
            .getClient()
            .setEx(queueKey, 3600, JSON.stringify(queue));
        }
      }

      console.log(
        `✅ Música ${suggestion.title} promovida para fila prioritária`
      );
    } catch (error) {
      console.error("Erro ao promover música:", error);
      throw error;
    }
  }
}

export const playbackQueueService = new PlaybackQueueService();
export default PlaybackQueueService;
