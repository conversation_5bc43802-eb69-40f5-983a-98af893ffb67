import { useState, useEffect, useCallback, useRef } from 'react';

interface LoadingState {
  isLoading: boolean;
  error: string | null;
  progress: number;
  stage: string;
}

interface OptimizedLoadingOptions {
  stages: string[];
  onStageComplete?: (stage: string, data?: any) => void;
  onAllComplete?: (results: any[]) => void;
  onError?: (error: Error, stage: string) => void;
  timeout?: number;
  retries?: number;
}

/**
 * Hook para carregamento otimizado com stages e progress
 * Melhora a experiência do usuário mostrando progresso detalhado
 */
export const useOptimizedLoading = (options: OptimizedLoadingOptions) => {
  const {
    stages,
    onStageComplete,
    onAllComplete,
    onError,
    timeout = 30000,
    retries = 3
  } = options;

  const [state, setState] = useState<LoadingState>({
    isLoading: false,
    error: null,
    progress: 0,
    stage: ''
  });

  const abortControllerRef = useRef<AbortController>();
  const resultsRef = useRef<any[]>([]);

  // Função para executar um stage
  const executeStage = useCallback(async (
    stageName: string,
    stageIndex: number,
    executor: () => Promise<any>,
    attempt = 1
  ): Promise<any> => {
    try {
      setState(prev => ({
        ...prev,
        stage: stageName,
        progress: (stageIndex / stages.length) * 100
      }));

      console.log(`[OptimizedLoading] Executando stage: ${stageName} (tentativa ${attempt})`);

      // Timeout para o stage
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error(`Timeout no stage: ${stageName}`)), timeout);
      });

      const result = await Promise.race([
        executor(),
        timeoutPromise
      ]);

      console.log(`[OptimizedLoading] Stage ${stageName} concluído com sucesso`);
      onStageComplete?.(stageName, result);
      
      return result;
    } catch (error) {
      console.error(`[OptimizedLoading] Erro no stage ${stageName}:`, error);

      if (attempt < retries) {
        console.log(`[OptimizedLoading] Tentando novamente stage ${stageName} (${attempt + 1}/${retries})`);
        await new Promise(resolve => setTimeout(resolve, 1000 * attempt)); // Backoff
        return executeStage(stageName, stageIndex, executor, attempt + 1);
      }

      const errorObj = error instanceof Error ? error : new Error(String(error));
      onError?.(errorObj, stageName);
      throw errorObj;
    }
  }, [stages.length, timeout, retries, onStageComplete, onError]);

  // Função principal de carregamento
  const load = useCallback(async (executors: (() => Promise<any>)[]) => {
    if (executors.length !== stages.length) {
      throw new Error('Número de executors deve ser igual ao número de stages');
    }

    // Reset state
    setState({
      isLoading: true,
      error: null,
      progress: 0,
      stage: stages[0] || 'Iniciando...'
    });

    resultsRef.current = [];
    abortControllerRef.current = new AbortController();

    try {
      console.log('[OptimizedLoading] Iniciando carregamento otimizado');

      // Executar stages sequencialmente
      for (let i = 0; i < stages.length; i++) {
        if (abortControllerRef.current.signal.aborted) {
          throw new Error('Carregamento cancelado');
        }

        const result = await executeStage(stages[i], i, executors[i]);
        resultsRef.current.push(result);
      }

      // Finalizar
      setState(prev => ({
        ...prev,
        isLoading: false,
        progress: 100,
        stage: 'Concluído'
      }));

      console.log('[OptimizedLoading] Carregamento concluído com sucesso');
      onAllComplete?.(resultsRef.current);

      return resultsRef.current;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Erro desconhecido';
      
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: errorMessage
      }));

      console.error('[OptimizedLoading] Carregamento falhou:', error);
      throw error;
    }
  }, [stages, executeStage, onAllComplete]);

  // Função para cancelar carregamento
  const cancel = useCallback(() => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: 'Carregamento cancelado'
      }));
    }
  }, []);

  // Função para retry
  const retry = useCallback((executors: (() => Promise<any>)[]) => {
    setState(prev => ({ ...prev, error: null }));
    return load(executors);
  }, [load]);

  // Cleanup
  useEffect(() => {
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, []);

  return {
    ...state,
    load,
    cancel,
    retry,
    results: resultsRef.current
  };
};

/**
 * Hook específico para carregamento de dados do restaurante
 */
export const useRestaurantDataLoading = (restaurantId: string) => {
  return useOptimizedLoading({
    stages: [
      'Carregando dados básicos',
      'Carregando fila de reprodução',
      'Carregando estatísticas',
      'Configurando WebSocket'
    ],
    timeout: 15000,
    retries: 2
  });
};

/**
 * Hook para carregamento de componentes lazy
 */
export const useLazyComponentLoading = () => {
  const [loadedComponents, setLoadedComponents] = useState<Set<string>>(new Set());

  const loadComponent = useCallback(async (componentName: string, loader: () => Promise<any>) => {
    if (loadedComponents.has(componentName)) {
      return; // Já carregado
    }

    try {
      console.log(`[LazyLoading] Carregando componente: ${componentName}`);
      await loader();
      setLoadedComponents(prev => new Set([...prev, componentName]));
      console.log(`[LazyLoading] Componente ${componentName} carregado`);
    } catch (error) {
      console.error(`[LazyLoading] Erro ao carregar ${componentName}:`, error);
      throw error;
    }
  }, [loadedComponents]);

  const isLoaded = useCallback((componentName: string) => {
    return loadedComponents.has(componentName);
  }, [loadedComponents]);

  return {
    loadComponent,
    isLoaded,
    loadedComponents: Array.from(loadedComponents)
  };
};

export default useOptimizedLoading;
