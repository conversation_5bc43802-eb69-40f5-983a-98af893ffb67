import { useRef, useCallback } from 'react';

interface CacheEntry {
  data: any;
  timestamp: number;
  promise?: Promise<any>;
}

interface ApiCacheOptions {
  ttl?: number; // Time to live em ms (padrão: 30 segundos)
  maxRetries?: number;
  retryDelay?: number;
}

/**
 * Hook para cache inteligente de APIs com debounce automático
 * Evita múltiplas chamadas simultâneas para a mesma URL
 */
export const useApiCache = (options: ApiCacheOptions = {}) => {
  const {
    ttl = 30000, // 30 segundos
    maxRetries = 3,
    retryDelay = 1000
  } = options;

  const cacheRef = useRef<Map<string, CacheEntry>>(new Map());
  const pendingRequestsRef = useRef<Map<string, Promise<any>>>(new Map());

  // Limpar cache expirado
  const cleanExpiredCache = useCallback(() => {
    const now = Date.now();
    const cache = cacheRef.current;
    
    for (const [key, entry] of cache.entries()) {
      if (now - entry.timestamp > ttl) {
        cache.delete(key);
      }
    }
  }, [ttl]);

  // Fazer chamada com cache e debounce
  const cachedFetch = useCallback(async (
    url: string, 
    options: RequestInit = {},
    customTtl?: number
  ): Promise<any> => {
    const cacheKey = `${url}_${JSON.stringify(options)}`;
    const cache = cacheRef.current;
    const pendingRequests = pendingRequestsRef.current;
    const now = Date.now();
    const effectiveTtl = customTtl || ttl;

    // Limpar cache expirado periodicamente
    if (Math.random() < 0.1) { // 10% de chance
      cleanExpiredCache();
    }

    // Verificar se há dados em cache válidos
    const cachedEntry = cache.get(cacheKey);
    if (cachedEntry && (now - cachedEntry.timestamp) < effectiveTtl) {
      console.log(`[ApiCache] Cache hit para: ${url}`);
      return cachedEntry.data;
    }

    // Verificar se já há uma requisição pendente para esta URL
    const pendingRequest = pendingRequests.get(cacheKey);
    if (pendingRequest) {
      console.log(`[ApiCache] Aguardando requisição pendente para: ${url}`);
      return pendingRequest;
    }

    // Fazer nova requisição
    console.log(`[ApiCache] Nova requisição para: ${url}`);
    
    const makeRequest = async (attempt = 1): Promise<any> => {
      try {
        const response = await fetch(url, {
          ...options,
          headers: {
            'Content-Type': 'application/json',
            ...options.headers,
          }
        });

        if (response.status === 429) {
          // Rate limited - aguardar mais tempo
          const waitTime = Math.min(retryDelay * Math.pow(2, attempt), 30000); // Max 30s
          console.warn(`[ApiCache] Rate limited (${url}). Aguardando ${waitTime}ms...`);
          
          if (attempt <= maxRetries) {
            await new Promise(resolve => setTimeout(resolve, waitTime));
            return makeRequest(attempt + 1);
          } else {
            throw new Error(`Rate limit exceeded após ${maxRetries} tentativas`);
          }
        }

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const data = await response.json();
        
        // Armazenar no cache
        cache.set(cacheKey, {
          data,
          timestamp: now
        });

        return data;
      } catch (error) {
        console.error(`[ApiCache] Erro na requisição (${url}):`, error);
        
        // Se há dados em cache (mesmo expirados), usar como fallback
        if (cachedEntry) {
          console.warn(`[ApiCache] Usando cache expirado como fallback para: ${url}`);
          return cachedEntry.data;
        }
        
        throw error;
      }
    };

    // Criar promise e armazenar como pendente
    const requestPromise = makeRequest()
      .finally(() => {
        // Remover da lista de pendentes quando terminar
        pendingRequests.delete(cacheKey);
      });

    pendingRequests.set(cacheKey, requestPromise);
    
    return requestPromise;
  }, [ttl, maxRetries, retryDelay, cleanExpiredCache]);

  // Invalidar cache específico
  const invalidateCache = useCallback((urlPattern?: string) => {
    const cache = cacheRef.current;
    
    if (!urlPattern) {
      // Limpar todo o cache
      cache.clear();
      console.log('[ApiCache] Cache completamente limpo');
      return;
    }

    // Limpar cache que corresponde ao padrão
    for (const key of cache.keys()) {
      if (key.includes(urlPattern)) {
        cache.delete(key);
      }
    }
    console.log(`[ApiCache] Cache invalidado para padrão: ${urlPattern}`);
  }, []);

  // Estatísticas do cache
  const getCacheStats = useCallback(() => {
    const cache = cacheRef.current;
    const pending = pendingRequestsRef.current;
    const now = Date.now();
    
    let validEntries = 0;
    let expiredEntries = 0;
    
    for (const entry of cache.values()) {
      if (now - entry.timestamp < ttl) {
        validEntries++;
      } else {
        expiredEntries++;
      }
    }

    return {
      totalEntries: cache.size,
      validEntries,
      expiredEntries,
      pendingRequests: pending.size
    };
  }, [ttl]);

  return {
    cachedFetch,
    invalidateCache,
    getCacheStats,
    cleanExpiredCache
  };
};

export default useApiCache;
