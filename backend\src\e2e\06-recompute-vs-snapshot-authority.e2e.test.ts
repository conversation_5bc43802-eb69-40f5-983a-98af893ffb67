import { request, waitForHealth } from "./helpers/http";

const RESTAURANT_ID = process.env.E2E_RESTAURANT_ID || "demo-restaurant";

function n(v: any) {
  const x = Number(v);
  return Number.isFinite(x) ? x : 0;
}

describe("E2E: recomputeQueue/reorder ativos vs Snapshot (autoridade)", () => {
  beforeAll(async () => {
    const ok = await waitForHealth(90000, 2000);
    if (!ok) throw new Error("API não ficou healthy");
  });

  it("mantendo recomputeQueue ativo: votação reorganiza a fila sem depender de snapshot explícito", async () => {
    // 1) Pegar ranking atual para escolher alvos
    const r0 = await request(`/collaborative-playlist/${RESTAURANT_ID}/ranking?limit=10`, "GET");
    expect(r0.ok).toBe(true);
    const list = Array.isArray(r0.json?.data) ? r0.json!.data : [];
    if (list.length < 2) {
      // cenário insuficiente; apenas valida endpoint
      return;
    }

    // 2) Capturar fila atual
    const q0 = await request(`/playback-queue/${RESTAURANT_ID}`, "GET");
    expect(q0.ok).toBe(true);
    const queue0 = q0.json?.queue || [];

    // 3) Escolher dois vídeos e forçar que B ultrapasse A via supervoto
    const A = list[0];
    const B = list[1];

    // Se já está invertido, escolha outro par
    const initialOrderInQueue = queue0.map((i: any) => i.youtubeVideoId);
    const aIdx = initialOrderInQueue.indexOf(A.youtubeVideoId);
    const bIdx = initialOrderInQueue.indexOf(B.youtubeVideoId);

    if (aIdx === -1 || bIdx === -1) {
      // Se algum não estiver na fila (pode estar tocando ou filtrado), seguimos validando apenas o ranking/base
    }

    // 4) Disparar supervoto para B suficiente para passar A
    const aVotes = n(A.voteCount);
    const bVotes = n(B.voteCount);

    const deltaNeeded = Math.max(0, aVotes - bVotes + 1);
    // Converter deltaNeeded em valor PIX usando pesos 5->10, 20->50, 50->150
    // Estratégia: se deltaNeeded > 50, usar 50 (150 votos); senão 20 (50 votos); senão 5 (10 votos)
    const paymentAmount = deltaNeeded > 50 ? 50 : deltaNeeded > 10 ? 20 : 5;

    const svPayload = {
      youtubeVideoId: B.youtubeVideoId,
      paymentAmount,
      paymentId: `recompute_e2e_${paymentAmount}_${Date.now()}`,
      clientSessionId: `sess_${Date.now()}`,
    };

    const sv = await request(`/collaborative-playlist/${RESTAURANT_ID}/supervote`, "POST", svPayload);
    expect(sv.ok).toBe(true);

    // 5) Consultar ranking após o supervoto (deve refletir peso)
    const r1 = await request(`/collaborative-playlist/${RESTAURANT_ID}/ranking?limit=10`, "GET");
    expect(r1.ok).toBe(true);
    const after = Array.isArray(r1.json?.data) ? r1.json!.data : [];

    const A1 = after.find((x: any) => x.youtubeVideoId === A.youtubeVideoId) || A;
    const B1 = after.find((x: any) => x.youtubeVideoId === B.youtubeVideoId) || B;

    // B deve ter ganho votos significativos
    expect(n(B1.voteCount)).toBeGreaterThan(n(B.voteCount));

    // 6) Sem chamar snapshot explicitamente, conferir se a fila reordenou por recomputeQueue
    // Observação: CollaborativePlaylistService chama playbackService.recomputeQueue no supervoto

    // Pequeno delay para a recomputeQueue aplicar e a API refletir
    await new Promise((r) => setTimeout(r, 1500));

    const q1 = await request(`/playback-queue/${RESTAURANT_ID}`, "GET");
    expect(q1.ok).toBe(true);
    const queue1 = q1.json?.queue || [];

    const order1 = queue1.map((i: any) => i.youtubeVideoId);
    const idxA1 = order1.indexOf(A.youtubeVideoId);
    const idxB1 = order1.indexOf(B.youtubeVideoId);

    // Se ambos estão na fila, B deve estar em posição melhor (menor índice) do que A
    if (idxA1 !== -1 && idxB1 !== -1) {
      expect(idxB1).toBeLessThan(idxA1);
    }

    // Esse teste demonstra que recomputeQueue/reorder mantidos reordenam a fila
    // mesmo sem depender explicitamente do snapshot.
  });
});

