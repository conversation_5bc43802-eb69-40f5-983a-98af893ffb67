import { Request, Response, NextFunction } from 'express';
import { redisClient, redis } from '../config/redis';

interface RateLimitOptions {
  windowMs: number;
  maxRequests: number;
  keyGenerator?: (req: Request) => string;
  skipSuccessfulRequests?: boolean;
  skipFailedRequests?: boolean;
  message?: string;
}

interface RateLimitInfo {
  totalHits: number;
  totalTime: number;
  resetTime: Date;
}

/**
 * Middleware de Rate Limiting inteligente
 * Usa Redis para armazenar contadores distribuídos
 */
export class RateLimiter {
  private options: Required<RateLimitOptions>;

  constructor(options: RateLimitOptions) {
    this.options = {
      keyGenerator: (req) => `rate_limit:${req.ip}`,
      skipSuccessfulRequests: false,
      skipFailedRequests: false,
      message: 'Muitas requisições deste IP, tente novamente em 1 minuto.',
      ...options
    };
  }

  middleware() {
    return async (req: Request, res: Response, next: NextFunction) => {
      try {
        const key = this.options.keyGenerator(req);
        const windowMs = this.options.windowMs;
        const maxRequests = this.options.maxRequests;

        // Verificar rate limit atual
        const current = await this.getCurrentCount(key, windowMs);
        
        // Headers informativos
        res.set({
          'X-RateLimit-Limit': maxRequests.toString(),
          'X-RateLimit-Remaining': Math.max(0, maxRequests - current.totalHits).toString(),
          'X-RateLimit-Reset': current.resetTime.toISOString()
        });

        // Verificar se excedeu o limite
        if (current.totalHits >= maxRequests) {
          const retryAfter = Math.ceil((current.resetTime.getTime() - Date.now()) / 1000);
          
          res.set({
            'Retry-After': retryAfter.toString(),
            'X-RateLimit-Remaining': '0'
          });

          return res.status(429).json({
            error: this.options.message,
            retryAfter,
            limit: maxRequests,
            windowMs: windowMs
          });
        }

        // Incrementar contador após a resposta, respeitando flags de skip
        const opts = this.options;
        res.on('finish', async () => {
          try {
            const statusCode = res.statusCode;
            let shouldIncrement = true;
            if (statusCode >= 400 && opts.skipFailedRequests) shouldIncrement = false;
            if (statusCode < 400 && opts.skipSuccessfulRequests) shouldIncrement = false;
            if (shouldIncrement) {
              await this.incrementCount(key, windowMs);
            }
          } catch (err) {
            console.error('Rate limiter post-response increment error:', err);
          }
        });

        next();
      } catch (error) {
        console.error('Rate limiter error:', error);
        // Em caso de erro, permitir a requisição
        next();
      }
    };
  }

  private async getCurrentCount(key: string, windowMs: number): Promise<RateLimitInfo> {
    const now = Date.now();
    const windowStart = now - windowMs;
    
    try {
      // Usar comandos individuais (pipeline não disponível nesta versão)
      // const pipeline = redisClient.pipeline();
      
      // Usar comandos individuais
  await redis.zRemRangeByScore(key, 0, windowStart);
  const totalHits = (await redis.zCard(key)) || 0;
  await redis.expire(key, Math.ceil(windowMs / 1000) + 1);
      
      return {
        totalHits,
        totalTime: windowMs,
        resetTime: new Date(now + windowMs)
      };
    } catch (error) {
      console.error('Error getting rate limit count:', error);
      return {
        totalHits: 0,
        totalTime: windowMs,
        resetTime: new Date(now + windowMs)
      };
    }
  }

  private async incrementCount(key: string, windowMs: number): Promise<void> {
    const now = Date.now();
    
    try {
      // Usar comandos individuais
  await redis.zAdd(key, [{ score: now, value: `${now}-${Math.random()}` }]);
  await redis.expire(key, Math.ceil(windowMs / 1000) + 1);
    } catch (error) {
      console.error('Error incrementing rate limit:', error);
    }
  }
}

// Rate limiters pré-configurados
export const createApiRateLimiter = (maxRequests = 100, windowMs = 60000) => {
  return new RateLimiter({
    windowMs,
    maxRequests,
    keyGenerator: (req) => `api_rate_limit:${req.ip}`,
    message: 'Muitas requisições deste IP, tente novamente em 1 minuto.'
  });
};

export const createRestaurantRateLimiter = (maxRequests = 50, windowMs = 60000) => {
  return new RateLimiter({
    windowMs,
    maxRequests,
    keyGenerator: (req) => {
      const restaurantId = req.params.restaurantId || req.params.id;
      return `restaurant_rate_limit:${req.ip}:${restaurantId}`;
    },
    message: 'Muitas requisições para este restaurante, tente novamente em 1 minuto.'
  });
};

export const createPlaybackRateLimiter = (maxRequests = 30, windowMs = 60000) => {
  return new RateLimiter({
    windowMs,
    maxRequests,
    keyGenerator: (req) => {
      const restaurantId = req.params.restaurantId || req.params.id;
      return `playback_rate_limit:${req.ip}:${restaurantId}`;
    },
    message: 'Muitas requisições de playback, tente novamente em 1 minuto.',
    skipSuccessfulRequests: false // Contar todas as requisições de playback
  });
};

// Rate limiter específico para endpoints que são chamados frequentemente
export const createFrequentEndpointRateLimiter = (maxRequests = 20, windowMs = 60000) => {
  return new RateLimiter({
    windowMs,
    maxRequests,
    keyGenerator: (req) => `frequent_endpoint:${req.ip}:${req.path}`,
    message: 'Muitas requisições para este endpoint, tente novamente em 1 minuto.'
  });
};

export default RateLimiter;
