import { Router } from "express";
import { body, param, query, validationResult } from "../utils/validation";
import { playbackQueueService } from "../services/PlaybackQueueService";
import asyncHandler from "../middleware/asyncHandler";
import { optionalAuth } from "../middleware/auth";
import { ValidationError, NotFoundError } from "../utils/errors";
import { redisClient } from "../config/redis";
import { createPlaybackRateLimiter } from "../middleware/rateLimiter";
import { playbackService } from "../services/PlaybackService";

const router = Router();

// Rate limiter para endpoints de playback (20 requests por minuto)
const playbackRateLimit = createPlaybackRateLimiter(20, 60000);

// ROTA DE DEBUG TEMPORÁRIA PARA DIAGNÓSTICO
router.get("/debug-system/:restaurantId", [], asyncHandler(async (req, res) => {
  const { restaurantId } = req.params;
  console.log('🔍 DIAGNÓSTICO COMPLETO DO SISTEMA para', restaurantId);

  const { AppDataSource } = require("../config/database");

  // 1. Verificar sugestões por status
  const suggestionsByStatus = await AppDataSource.query(`
    SELECT
      status,
      COUNT(*) as count,
      STRING_AGG(title, ', ') as titles
    FROM suggestions
    WHERE restaurant_id = $1
    GROUP BY status
    ORDER BY count DESC
  `, [restaurantId]);

  // 2. Verificar música "Respeitando Minha Saudade"
  const respeitandoStatus = await AppDataSource.query(`
    SELECT
      id, title, status, upvotes, downvotes, created_at, updated_at
    FROM suggestions
    WHERE restaurant_id = $1
      AND title ILIKE '%Respeitando%Saudade%'
    ORDER BY created_at DESC
    LIMIT 5
  `, [restaurantId]);

  // 3. Verificar música "Gustavo Lima"
  const gustavoStatus = await AppDataSource.query(`
    SELECT
      id, title, status, upvotes, downvotes, created_at, updated_at
    FROM suggestions
    WHERE restaurant_id = $1
      AND title ILIKE '%Gustavo%Lima%'
    ORDER BY created_at DESC
    LIMIT 5
  `, [restaurantId]);

  // 4. Verificar estado do PlaybackService
  const { playbackService } = require("../services/PlaybackService");
  let playbackState = null;
  try {
    playbackState = await playbackService.getPlaybackState(restaurantId);
  } catch (e) {
    console.error('Erro ao obter estado do PlaybackService:', e);
  }

  // 5. Verificar histórico de reprodução
  const playbackHistory = await AppDataSource.query(`
    SELECT
      id, title, status, created_at, updated_at,
      CASE
        WHEN title ILIKE '%Respeitando%Saudade%' THEN 'RESPEITANDO_MATCH'
        WHEN title ILIKE '%Gustavo%Lima%' THEN 'GUSTAVO_MATCH'
        ELSE 'OTHER'
      END as match_type
    FROM suggestions
    WHERE restaurant_id = $1
      AND status IN ('playing', 'completed', 'skipped')
    ORDER BY updated_at DESC
    LIMIT 10
  `, [restaurantId]);

  // 6. Verificar fila atual
  const queueData = await playbackQueueService.getPlaybackQueue(restaurantId);

  // 7. Verificar estado do Redis
  const { redisClient } = require("../config/redis");
  let redisState = null;
  try {
    const redisData = await redisClient.get(`playback:${restaurantId}`);
    redisState = redisData ? JSON.parse(redisData) : null;
  } catch (e) {
    console.error('Erro ao acessar Redis:', e);
  }

  console.log('📊 DIAGNÓSTICO COMPLETO:', {
    suggestionsByStatus: suggestionsByStatus.length,
    respeitandoCount: respeitandoStatus.length,
    gustavoCount: gustavoStatus.length,
    playbackState: playbackState ? 'OK' : 'NULL',
    queueSize: queueData.queue.length,
    historySize: playbackHistory.length,
    redisState: redisState ? 'OK' : 'NULL'
  });

  res.json({
    success: true,
    diagnosis: {
      suggestionsByStatus,
      respeitandoSaudade: respeitandoStatus,
      gustavoLima: gustavoStatus,
      playbackServiceState: playbackState,
      playbackHistory,
      redisPlaybackState: redisState,
      currentQueue: queueData.queue.slice(0, 5),
      currentlyPlaying: queueData.currentlyPlaying,
      timestamp: Date.now()
    },
    debug: 'COMPLETE_SYSTEM_DIAGNOSIS_V2'
  });
}));

// ROTA DE TESTE PARA INICIAR REPRODUÇÃO
router.get("/test-play-first/:restaurantId", [], asyncHandler(async (req, res) => {
  const { restaurantId } = req.params;
  console.log('🎵 TESTE: INICIAR REPRODUÇÃO DA PRIMEIRA MÚSICA para', restaurantId);

  try {
    // 1. Obter primeira música da fila
    const queueData = await playbackQueueService.getPlaybackQueue(restaurantId);
    const firstSong = queueData.queue[0];

    if (!firstSong) {
      return res.json({
        success: false,
        error: 'Nenhuma música na fila',
        debug: 'NO_SONGS_IN_QUEUE'
      });
    }

    console.log('🎵 PRIMEIRA MÚSICA:', firstSong.title, 'ID:', firstSong.suggestionId);

    // 2. Iniciar reprodução usando PlaybackService
    const { playbackService } = require("../services/PlaybackService");
    await playbackService.playTrack(restaurantId, firstSong.suggestionId);

    // 3. Verificar estado após iniciar
    const newState = await playbackService.getPlaybackState(restaurantId);

    console.log('✅ REPRODUÇÃO INICIADA:', newState?.currentTrack?.title || 'NULL');

    res.json({
      success: true,
      message: 'Reprodução iniciada',
      songStarted: {
        id: firstSong.suggestionId,
        title: firstSong.title,
        artist: firstSong.artist
      },
      newPlaybackState: {
        currentTrack: newState?.currentTrack?.title || null,
        isPlaying: newState?.isPlaying || false
      },
      debug: 'PLAYBACK_STARTED'
    });

  } catch (error) {
    console.error('❌ ERRO ao iniciar reprodução:', error);
    res.json({
      success: false,
      error: error.message,
      debug: 'PLAYBACK_START_ERROR'
    });
  }
}));

// ROTA PARA FORÇAR SINCRONIZAÇÃO E LIMPEZA DE CACHE
router.get("/force-sync/:restaurantId", [], asyncHandler(async (req, res) => {
  const { restaurantId } = req.params;
  console.log('🔄 FORÇANDO SINCRONIZAÇÃO COMPLETA para', restaurantId);

  try {
    // 1. Limpar cache Redis
    const { redisClient } = require("../config/redis");
    const stateKey = `playback:${restaurantId}`;
    await redisClient.del(stateKey);
    console.log('🗑️ Cache Redis limpo');

    // 2. Obter estado autoritativo do PlaybackService
    const { playbackService } = require("../services/PlaybackService");
    const authoritativeState = await playbackService.getPlaybackState(restaurantId);
    console.log('📊 Estado autoritativo:', authoritativeState?.currentTrack?.title || 'NULL');

    // 3. Marcar todas as músicas PLAYING como COMPLETED (limpeza)
    const { AppDataSource } = require("../config/database");
    const cleanupResult = await AppDataSource.query(`
      UPDATE suggestions
      SET status = 'completed', updated_at = NOW()
      WHERE restaurant_id = $1 AND status = 'playing'
    `, [restaurantId]);
    console.log('🧹 Músicas PLAYING limpas:', cleanupResult.affectedRows || 0);

    // 4. Se há música tocando no PlaybackService, marcar como PLAYING
    if (authoritativeState?.currentTrack) {
      await AppDataSource.query(`
        UPDATE suggestions
        SET status = 'playing', played_at = NOW(), updated_at = NOW()
        WHERE id = $1
      `, [authoritativeState.currentTrack.id]);
      console.log('🎵 Música atual marcada como PLAYING:', authoritativeState.currentTrack.title);
    }

    // 5. Obter nova fila após limpeza
    const queueData = await playbackQueueService.getPlaybackQueue(restaurantId);

    // 6. Forçar atualização do estado Redis
    if (authoritativeState?.currentTrack) {
      const syncedState = {
        isPlaying: authoritativeState.isPlaying,
        currentSong: {
          id: authoritativeState.currentTrack.id,
          title: authoritativeState.currentTrack.title,
          artist: authoritativeState.currentTrack.artist,
          youtubeId: authoritativeState.currentTrack.youtubeVideoId,
          thumbnailUrl: authoritativeState.currentTrack.thumbnailUrl,
          duration: authoritativeState.currentTrack.duration,
        },
        volume: authoritativeState.volume || 70,
        position: authoritativeState.currentTime || 0,
        queue: [],
        shuffle: false,
        repeat: "none",
      };

      await redisClient.set(stateKey, JSON.stringify(syncedState), 3600);
      console.log('✅ Estado Redis sincronizado');
    }

    res.json({
      success: true,
      message: 'Sincronização completa realizada',
      results: {
        cacheCleared: true,
        playingTracksCleanedUp: cleanupResult.affectedRows || 0,
        currentTrack: authoritativeState?.currentTrack?.title || null,
        isPlaying: authoritativeState?.isPlaying || false,
        queueSize: queueData.queue.length,
        firstInQueue: queueData.queue[0]?.title || null
      },
      debug: 'FORCE_SYNC_COMPLETE'
    });

  } catch (error) {
    console.error('❌ ERRO na sincronização:', error);
    res.json({
      success: false,
      error: error.message,
      debug: 'FORCE_SYNC_ERROR'
    });
  }
}));

// ROTA PARA FORÇAR SINCRONIZAÇÃO E LIMPAR CACHE
router.post("/force-sync/:restaurantId", [], asyncHandler(async (req, res) => {
  const { restaurantId } = req.params;
  console.log('🔄 FORÇANDO SINCRONIZAÇÃO COMPLETA para', restaurantId);

  try {
    const { playbackService } = require("../services/PlaybackService");
    const { redisClient } = require("../config/redis");

    // 1. Obter estado autoritativo do PlaybackService
    const authoritativeState = await playbackService.getPlaybackState(restaurantId);

    // 2. Limpar cache Redis
    const stateKey = `playback:${restaurantId}`;
    await redisClient.del(stateKey);
    console.log('🗑️ Cache Redis limpo');

    // 3. Se há música tocando, sincronizar
    if (authoritativeState && authoritativeState.currentTrack) {
      const syncedState = {
        isPlaying: authoritativeState.isPlaying,
        currentSong: {
          id: authoritativeState.currentTrack.id,
          title: authoritativeState.currentTrack.title,
          artist: authoritativeState.currentTrack.artist,
          youtubeId: authoritativeState.currentTrack.youtubeVideoId,
          thumbnailUrl: authoritativeState.currentTrack.thumbnailUrl,
          duration: authoritativeState.currentTrack.duration,
        },
        volume: authoritativeState.volume || 70,
        position: authoritativeState.currentTime || 0,
        queue: [],
        shuffle: false,
        repeat: "none",
      };

      await redisClient.set(stateKey, JSON.stringify(syncedState), 3600);
      console.log('✅ Estado sincronizado:', authoritativeState.currentTrack.title);
    }

    // 4. Forçar atualização da fila
    const queueData = await playbackQueueService.getPlaybackQueue(restaurantId);

    // 5. Verificar se música atual está na fila (não deveria estar)
    const currentTrackInQueue = authoritativeState?.currentTrack ?
      queueData.queue.find(item => item.suggestionId === authoritativeState.currentTrack.id) : null;

    res.json({
      success: true,
      message: 'Sincronização forçada concluída',
      syncResult: {
        authoritativeState: authoritativeState ? {
          currentTrack: authoritativeState.currentTrack?.title || null,
          isPlaying: authoritativeState.isPlaying
        } : null,
        queueSize: queueData.queue.length,
        currentTrackInQueue: currentTrackInQueue ? true : false,
        firstInQueue: queueData.queue[0]?.title || null
      },
      debug: 'FORCE_SYNC_COMPLETE'
    });

  } catch (error) {
    console.error('❌ ERRO na sincronização forçada:', error);
    res.json({
      success: false,
      error: error.message,
      debug: 'FORCE_SYNC_ERROR'
    });
  }
}));

// ROTA PARA CORRIGIR REPRODUÇÃO: PARAR ATUAL E INICIAR PRIMEIRA DA FILA
router.post("/fix-playback/:restaurantId", [], asyncHandler(async (req, res) => {
  const { restaurantId } = req.params;
  console.log('🔧 CORRIGINDO REPRODUÇÃO para', restaurantId);

  try {
    const { playbackService } = require("../services/PlaybackService");
    const { AppDataSource } = require("../config/database");

    // 1. Parar música atual (se houver)
    const currentState = await playbackService.getPlaybackState(restaurantId);
    if (currentState && currentState.currentTrack) {
      console.log('⏹️ Parando música atual:', currentState.currentTrack.title);

      // Marcar música atual como COMPLETED
      await AppDataSource.query(`
        UPDATE suggestions
        SET status = 'completed', completed_at = NOW()
        WHERE id = $1
      `, [currentState.currentTrack.id]);

      // Parar reprodução
      await playbackService.stopPlayback(restaurantId);
    }

    // 2. Obter primeira música da fila
    const queueData = await playbackQueueService.getPlaybackQueue(restaurantId);
    const firstSong = queueData.queue[0];

    if (!firstSong) {
      return res.json({
        success: false,
        error: 'Nenhuma música na fila para iniciar',
        debug: 'NO_SONGS_IN_QUEUE'
      });
    }

    console.log('▶️ Iniciando primeira música da fila:', firstSong.title);

    // 3. Iniciar primeira música da fila
    await playbackService.playTrack(restaurantId, firstSong.suggestionId);

    // 4. Verificar estado após correção
    const newState = await playbackService.getPlaybackState(restaurantId);

    res.json({
      success: true,
      message: 'Reprodução corrigida com sucesso',
      correction: {
        stoppedTrack: currentState?.currentTrack?.title || null,
        startedTrack: newState?.currentTrack?.title || null,
        isPlaying: newState?.isPlaying || false,
        queueSize: queueData.queue.length
      },
      debug: 'PLAYBACK_FIXED'
    });

  } catch (error) {
    console.error('❌ ERRO ao corrigir reprodução:', error);
    res.json({
      success: false,
      error: error.message,
      debug: 'PLAYBACK_FIX_ERROR'
    });
  }
}));

// ROTA PARA MARCAR PRIMEIRA MÚSICA COMO PLAYING E ATUALIZAR FILA
router.post("/start-first-song/:restaurantId", [], asyncHandler(async (req, res) => {
  const { restaurantId } = req.params;
  console.log('🎵 INICIANDO PRIMEIRA MÚSICA E ATUALIZANDO FILA para', restaurantId);

  try {
    const { AppDataSource } = require("../config/database");

    // 1. Obter primeira música da fila ANTES de marcar como playing
    const queueData = await playbackQueueService.getPlaybackQueue(restaurantId);
    const firstSong = queueData.queue[0];

    if (!firstSong) {
      return res.json({
        success: false,
        error: 'Nenhuma música na fila para iniciar',
        debug: 'NO_SONGS_IN_QUEUE'
      });
    }

    console.log('🎵 Primeira música da fila:', firstSong.title, 'ID:', firstSong.suggestionId);

    // 2. Marcar música como PLAYING no banco
    await AppDataSource.query(`
      UPDATE suggestions
      SET status = 'playing', updated_at = NOW()
      WHERE id = $1 AND restaurant_id = $2
    `, [firstSong.suggestionId, restaurantId]);

    console.log('✅ Música marcada como PLAYING no banco');

    // 3. Iniciar reprodução no PlaybackService
    const { playbackService } = require("../services/PlaybackService");
    await playbackService.playTrack(restaurantId, firstSong.suggestionId);

    // 4. Verificar nova fila (deve ter uma música a menos)
    const newQueueData = await playbackQueueService.getPlaybackQueue(restaurantId);

    // 5. Verificar estado do PlaybackService
    const playbackState = await playbackService.getPlaybackState(restaurantId);

    console.log('📊 Resultado:', {
      filaAnterior: queueData.queue.length,
      filaNova: newQueueData.queue.length,
      musicaIniciada: playbackState?.currentTrack?.title || 'null',
      isPlaying: playbackState?.isPlaying || false
    });

    res.json({
      success: true,
      message: 'Primeira música iniciada e fila atualizada',
      result: {
        songStarted: {
          id: firstSong.suggestionId,
          title: firstSong.title,
          artist: firstSong.artist
        },
        queueBefore: queueData.queue.length,
        queueAfter: newQueueData.queue.length,
        newFirstInQueue: newQueueData.queue[0]?.title || null,
        playbackState: {
          currentTrack: playbackState?.currentTrack?.title || null,
          isPlaying: playbackState?.isPlaying || false
        }
      },
      debug: 'FIRST_SONG_STARTED'
    });

  } catch (error) {
    console.error('❌ ERRO ao iniciar primeira música:', error);
    res.json({
      success: false,
      error: error.message,
      debug: 'START_FIRST_SONG_ERROR'
    });
  }
}));

// ROTA PARA RESETAR MÚSICAS COMPLETED PARA APPROVED (VOLTAR PARA FILA)
router.post("/reset-completed-songs/:restaurantId", [], asyncHandler(async (req, res) => {
  const { restaurantId } = req.params;
  console.log('🔄 RESETANDO MÚSICAS COMPLETED PARA APPROVED para', restaurantId);

  try {
    const { AppDataSource } = require("../config/database");

    // 1. Resetar todas as músicas completed para approved
    const result = await AppDataSource.query(`
      UPDATE suggestions
      SET status = 'approved', updated_at = NOW()
      WHERE restaurant_id = $1 AND status = 'completed'
    `, [restaurantId]);

    console.log('✅ Músicas resetadas:', result.affectedRows || 'N/A');

    // 2. Verificar nova fila
    const queueData = await playbackQueueService.getPlaybackQueue(restaurantId);

    console.log('📊 Nova fila:', {
      totalMusicas: queueData.queue.length,
      primeiraMusica: queueData.queue[0]?.title || 'null'
    });

    res.json({
      success: true,
      message: 'Músicas completed resetadas para approved',
      result: {
        songsReset: result.affectedRows || 0,
        newQueueSize: queueData.queue.length,
        firstInQueue: queueData.queue[0]?.title || null,
          topSongs: queueData.queue.slice(0, 5).map(song => ({
          position: song.position,
          title: song.title,
          votes: song.likeCount || 0
        }))
      },
      debug: 'SONGS_RESET_TO_APPROVED'
    });

  } catch (error) {
    console.error('❌ ERRO ao resetar músicas:', error);
    res.json({
      success: false,
      error: error.message,
      debug: 'RESET_SONGS_ERROR'
    });
  }
}));

// ROTA PARA INVESTIGAR MÚSICA ESPECÍFICA
router.get("/investigate-song/:restaurantId/:songTitle", [], asyncHandler(async (req, res) => {
  const { restaurantId, songTitle } = req.params;
  console.log('🔍 INVESTIGANDO MÚSICA:', songTitle, 'para', restaurantId);

  try {
    const { AppDataSource } = require("../config/database");

    // 1. Buscar música no banco
    const songData = await AppDataSource.query(`
      SELECT
        id, title, artist, status, upvotes, downvotes,
        created_at, updated_at, restaurant_id
      FROM suggestions
      WHERE restaurant_id = $1
        AND title ILIKE $2
      ORDER BY created_at DESC
      LIMIT 5
    `, [restaurantId, `%${songTitle}%`]);

    // 2. Verificar se está na fila atual
    const queueData = await playbackQueueService.getPlaybackQueue(restaurantId);
    const songInQueue = queueData.queue.find(item =>
      item.title.toLowerCase().includes(songTitle.toLowerCase())
    );

    // 3. Verificar votos recentes
    const recentVotes = await AppDataSource.query(`
      SELECT
        v.id, v.vote_type, v.created_at, v.suggestion_id,
        s.title, s.upvotes, s.downvotes
      FROM votes v
      JOIN suggestions s ON v.suggestion_id = s.id
      WHERE s.restaurant_id = $1
        AND s.title ILIKE $2
      ORDER BY v.created_at DESC
      LIMIT 10
    `, [restaurantId, `%${songTitle}%`]);

    console.log('📊 Investigação:', {
      songFound: songData.length > 0,
      inQueue: !!songInQueue,
      recentVotes: recentVotes.length
    });

    res.json({
      success: true,
      investigation: {
        searchTerm: songTitle,
        songsFound: songData,
        inCurrentQueue: songInQueue ? {
          position: songInQueue.position,
          votes: songInQueue.likeCount || 0,
          title: songInQueue.title
        } : null,
        recentVotes: recentVotes,
        queueSize: queueData.queue.length
      },
      debug: 'SONG_INVESTIGATION'
    });

  } catch (error) {
    console.error('❌ ERRO na investigação:', error);
    res.json({
      success: false,
      error: error.message,
      debug: 'INVESTIGATION_ERROR'
    });
  }
}));

// ROTA PARA TESTAR: FINALIZAR MÚSICA ATUAL E ZERAR VOTOS
router.post("/test-finish-current-song/:restaurantId", [], asyncHandler(async (req, res) => {
  const { restaurantId } = req.params;
  console.log('🎵 TESTE: FINALIZANDO MÚSICA ATUAL E ZERANDO VOTOS para', restaurantId);

  try {
    const { playbackService } = require("../services/PlaybackService");
    const { AppDataSource } = require("../config/database");

    // 1. Obter música atual do PlaybackService
    const currentState = await playbackService.getPlaybackState(restaurantId);

    if (!currentState || !currentState.currentTrack) {
      return res.json({
        success: false,
        error: 'Nenhuma música tocando atualmente',
        debug: 'NO_CURRENT_TRACK'
      });
    }

    const currentTrack = currentState.currentTrack;
    console.log('🎵 Finalizando música:', currentTrack.title);

    // 2. Verificar votos ANTES de finalizar
    const votesBefore = await AppDataSource.query(`
      SELECT id, title, upvotes, downvotes, status
      FROM suggestions
      WHERE id = $1
    `, [currentTrack.id]);

    // 3. Finalizar música (isso vai zerar os votos automaticamente)
    await playbackService.finishCurrentTrack(restaurantId);

    // 4. Verificar votos DEPOIS de finalizar
    const votesAfter = await AppDataSource.query(`
      SELECT id, title, upvotes, downvotes, status
      FROM suggestions
      WHERE id = $1
    `, [currentTrack.id]);

    // 5. Verificar nova fila
    const newQueue = await playbackQueueService.getPlaybackQueue(restaurantId);

    console.log('📊 Resultado do teste:', {
      musicaFinalizada: currentTrack.title,
      votosAntes: votesBefore[0],
      votosDepois: votesAfter[0],
      novaFilaTamanho: newQueue.queue.length
    });

    res.json({
      success: true,
      message: 'Música finalizada e votos zerados com sucesso',
      testResult: {
        finishedSong: {
          id: currentTrack.id,
          title: currentTrack.title,
          artist: currentTrack.artist
        },
        votesBefore: votesBefore[0] || null,
        votesAfter: votesAfter[0] || null,
        newQueue: {
          size: newQueue.queue.length,
          firstSong: newQueue.queue[0]?.title || null,
          topSongs: newQueue.queue.slice(0, 3).map(song => ({
            position: song.position,
            title: song.title,
            votes: song.likeCount || 0
          }))
        }
      },
      debug: 'FINISH_SONG_TEST'
    });

  } catch (error) {
    console.error('❌ ERRO no teste de finalizar música:', error);
    res.json({
      success: false,
      error: error.message,
      debug: 'FINISH_SONG_TEST_ERROR'
    });
  }
}));

// ROTA PARA TESTAR: SIMULAR MÚSICA TOCANDO E ZERANDO VOTOS
router.post("/test-zero-votes/:restaurantId/:songId", [], asyncHandler(async (req, res) => {
  const { restaurantId, songId } = req.params;
  console.log('🧪 TESTE: SIMULANDO MÚSICA TOCANDO E ZERANDO VOTOS para', songId);

  try {
    const { AppDataSource } = require("../config/database");

    // 1. Verificar música ANTES
    const songBefore = await AppDataSource.query(`
      SELECT id, title, status, upvotes, downvotes
      FROM suggestions
      WHERE id = $1
    `, [songId]);

    if (!songBefore.length) {
      return res.json({
        success: false,
        error: 'Música não encontrada',
        debug: 'SONG_NOT_FOUND'
      });
    }

    console.log('🎵 Música ANTES:', songBefore[0]);

    // 2. SIMULAR: Marcar como completed E zerar votos
    await AppDataSource.query(`
      UPDATE suggestions
      SET
        status = 'completed',
        upvotes = 0,
        downvotes = 0,
        completed_at = NOW(),
        updated_at = NOW()
      WHERE id = $1
    `, [songId]);

    console.log('✅ Música marcada como completed e votos zerados');

    // 3. Verificar música DEPOIS
    const songAfter = await AppDataSource.query(`
      SELECT id, title, status, upvotes, downvotes
      FROM suggestions
      WHERE id = $1
    `, [songId]);

    // 4. Verificar nova fila
    const newQueue = await playbackQueueService.getPlaybackQueue(restaurantId);
    const songInNewQueue = newQueue.queue.find(item =>
      item.title.toLowerCase().includes(songBefore[0].title.toLowerCase())
    );

    console.log('📊 Resultado:', {
      votosAntes: songBefore[0],
      votosDepois: songAfter[0],
      aindaNaFila: !!songInNewQueue
    });

    res.json({
      success: true,
      message: 'Teste de zeragem de votos concluído',
      testResult: {
        songBefore: songBefore[0],
        songAfter: songAfter[0],
        stillInQueue: songInNewQueue ? {
          position: songInNewQueue.position,
          votes: songInNewQueue.likeCount || 0
        } : null,
        queueSize: newQueue.queue.length
      },
      debug: 'ZERO_VOTES_TEST'
    });

  } catch (error) {
    console.error('❌ ERRO no teste de zerar votos:', error);
    res.json({
      success: false,
      error: error.message,
      debug: 'ZERO_VOTES_TEST_ERROR'
    });
  }
}));

// ROTA PARA FORÇAR ZERAGEM DE VOTOS POR YOUTUBE VIDEO ID
router.post("/force-zero-votes/:restaurantId/:youtubeVideoId", [], asyncHandler(async (req, res) => {
  const { restaurantId, youtubeVideoId } = req.params;
  console.log('🔧 FORÇANDO ZERAGEM DE VOTOS para vídeo:', youtubeVideoId);

  try {
    const { AppDataSource } = require("../config/database");

    // 1. Verificar músicas ANTES
    const songsBefore = await AppDataSource.query(`
      SELECT id, title, status, upvotes, downvotes, youtube_video_id
      FROM suggestions
      WHERE restaurant_id = $1
        AND youtube_video_id = $2
      ORDER BY created_at DESC
    `, [restaurantId, youtubeVideoId]);

    console.log('🎵 Músicas ANTES da zeragem:', songsBefore);

    // 2. FORÇAR ZERAGEM DE VOTOS
    const updateResult = await AppDataSource.query(`
      UPDATE suggestions
      SET
        status = 'completed',
        upvotes = 0,
        downvotes = 0,
        completed_at = NOW(),
        updated_at = NOW()
      WHERE restaurant_id = $1
        AND youtube_video_id = $2
      RETURNING id, title, status, upvotes, downvotes
    `, [restaurantId, youtubeVideoId]);

    console.log('✅ Músicas APÓS zeragem:', updateResult);

    // 3. Verificar nova fila
    const newQueue = await playbackQueueService.getPlaybackQueue(restaurantId);
    const affectedSongs = newQueue.queue.filter(item =>
      item.youtubeVideoId === youtubeVideoId
    );

    console.log('📊 Músicas na nova fila:', affectedSongs);

    res.json({
      success: true,
      message: 'Votos zerados com sucesso',
      result: {
        youtubeVideoId,
        songsUpdated: updateResult.length,
        songsBefore,
        songsAfter: updateResult,
        inNewQueue: affectedSongs,
        queueSize: newQueue.queue.length
      },
      debug: 'FORCE_ZERO_VOTES'
    });

  } catch (error) {
    console.error('❌ ERRO ao forçar zeragem:', error);
    res.json({
      success: false,
      error: error.message,
      debug: 'FORCE_ZERO_VOTES_ERROR'
    });
  }
}));

/**
 * @swagger
 * /api/v1/playback-queue/{restaurantId}:
 *   get:
 *     summary: Obter fila de reprodução
 *     tags: [Playback Queue]
 *     parameters:
 *       - in: path
 *         name: restaurantId
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Fila de reprodução
 */
router.get(
  "/:restaurantId",
  [
    param("restaurantId")
      .notEmpty()
      .withMessage("ID do restaurante é obrigatório"),
  ],
  playbackRateLimit.middleware(), // Rate limiting reativado
  optionalAuth,
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new ValidationError("Parâmetros inválidos", errors.array());
    }

    const { restaurantId } = req.params;

    const queueData = await playbackQueueService.getPlaybackQueue(restaurantId);

    res.json({
      success: true,
      queue: queueData.queue,
      stats: queueData.stats,
      currentlyPlaying: queueData.currentlyPlaying,
    });
  })
);

/**
 * @swagger
 * /api/v1/playback-queue/{restaurantId}/like:
 *   post:
 *     summary: Registrar like/dislike global para uma música
 *     tags: [Playback Queue]
 *     parameters:
 *       - in: path
 *         name: restaurantId
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required: [youtubeVideoId, action]
 *             properties:
 *               youtubeVideoId:
 *                 type: string
 *               action:
 *                 type: string
 *                 enum: [like, dislike]
 *     responses:
 *       200:
 *         description: Contadores atualizados
 */
router.post(
  "/:restaurantId/like",
  [
    param("restaurantId")
      .notEmpty()
      .withMessage("ID do restaurante é obrigatório"),
    body("youtubeVideoId")
      .notEmpty()
      .withMessage("ID do vídeo é obrigatório"),
    body("action").isIn(["like", "dislike"]).withMessage("Ação inválida"),
  ],
  optionalAuth,
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new ValidationError("Parâmetros inválidos", errors.array());
    }

    const { restaurantId } = req.params;
    const { youtubeVideoId, action } = req.body as {
      youtubeVideoId: string;
      action: "like" | "dislike";
    };

    const likeKey = `analytics:likes:${restaurantId}:${youtubeVideoId}`;
    const dislikeKey = `analytics:dislikes:${restaurantId}:${youtubeVideoId}`;

    let likeCount = 0;
    let dislikeCount = 0;
    try {
      if (redisClient.isReady) {
        const client = redisClient.getClient();
        if (action === "like") {
          await client.incr(likeKey);
        } else {
          await client.incr(dislikeKey);
        }

        const [likeCountStr, dislikeCountStr] = await Promise.all([
          client.get(likeKey),
          client.get(dislikeKey),
        ]);

        likeCount = parseInt(likeCountStr || "0", 10) || 0;
        dislikeCount = parseInt(dislikeCountStr || "0", 10) || 0;
      }
    } catch (e) {
      console.warn("Redis indisponível ao atualizar likes/dislikes:", e);
    }

    res.json({
      success: true,
      youtubeVideoId,
      likeCount,
      dislikeCount,
    });
  })
);

/**
 * @swagger
 * /api/v1/playback-queue/{restaurantId}/likes:
 *   get:
 *     summary: Obter contadores globais de likes/dislikes de uma lista de vídeos
 *     tags: [Playback Queue]
 *     parameters:
 *       - in: path
 *         name: restaurantId
 *         required: true
 *         schema:
 *           type: string
 *       - in: query
 *         name: ids
 *         schema:
 *           type: string
 *           example: "id1,id2,id3"
 *     responses:
 *       200:
 *         description: Contadores por id
 */
router.get(
  "/:restaurantId/likes",
  [
    param("restaurantId")
      .notEmpty()
      .withMessage("ID do restaurante é obrigatório"),
    query("ids").optional().isString(),
  ],
  optionalAuth,
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new ValidationError("Parâmetros inválidos", errors.array());
    }

    const { restaurantId } = req.params;
    const idsParam = (req.query.ids as string) || "";
    const ids = idsParam
      .split(",")
      .map((s) => s.trim())
      .filter(Boolean);

    if (ids.length === 0) {
      return res.json({ success: true, counts: {} });
    }

    const counts: Record<string, { likeCount: number; dislikeCount: number }> = {};
    try {
      if (redisClient.isReady) {
        const client = redisClient.getClient();
        const likeKeys = ids.map(
          (id) => `analytics:likes:${restaurantId}:${id}`
        );
        const dislikeKeys = ids.map(
          (id) => `analytics:dislikes:${restaurantId}:${id}`
        );

        const [likes, dislikes] = await Promise.all([
          client.mGet(likeKeys),
          client.mGet(dislikeKeys),
        ]);

        ids.forEach((id, idx) => {
          counts[id] = {
            likeCount: parseInt(likes?.[idx] || "0", 10) || 0,
            dislikeCount: parseInt(dislikes?.[idx] || "0", 10) || 0,
          };
        });
      } else {
        ids.forEach((id) => {
          counts[id] = { likeCount: 0, dislikeCount: 0 };
        });
      }
    } catch (e) {
      console.warn("Redis indisponível ao obter likes/dislikes:", e);
      ids.forEach((id) => {
        counts[id] = { likeCount: 0, dislikeCount: 0 };
      });
    }

    res.json({ success: true, counts });
  })
);

/**
 * @swagger
 * /api/v1/playback-queue/{restaurantId}/stats:
 *   get:
 *     summary: Obter estatísticas da fila
 *     tags: [Playback Queue]
 *     parameters:
 *       - in: path
 *         name: restaurantId
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Estatísticas da fila
 */
router.get(
  "/:restaurantId/stats",
  [
    param("restaurantId")
      .notEmpty()
      .withMessage("ID do restaurante é obrigatório"),
  ],
  optionalAuth,
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new ValidationError("Parâmetros inválidos", errors.array());
    }

    const { restaurantId } = req.params;

    const stats = await playbackQueueService.getQueueStats(restaurantId);

    res.json({
      success: true,
      stats,
    });
  })
);

/**
 * @swagger
 * /api/v1/playback-queue/add/{suggestionId}:
 *   post:
 *     summary: Adicionar música à fila
 *     tags: [Playback Queue]
 *     parameters:
 *       - in: path
 *         name: suggestionId
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       201:
 *         description: Música adicionada à fila
 */
// Rota para adicionar sugestão existente à fila
router.post(
  "/add/:suggestionId",
  [
    param("suggestionId")
      .notEmpty()
      .withMessage("ID da sugestão é obrigatório"),
  ],
  optionalAuth,
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new ValidationError("Parâmetros inválidos", errors.array());
    }

    const { suggestionId } = req.params;

    const queueItem = await playbackQueueService.addToQueue(suggestionId);

    if (!queueItem) {
      throw new NotFoundError("Sugestão não encontrada");
    }

    res.status(201).json({
      success: true,
      message: "Música adicionada à fila",
      queueItem,
    });
  })
);

/**
 * @swagger
 * /api/v1/playback-queue/add:
 *   post:
 *     summary: Criar sugestão e adicionar à fila (rota combinada)
 *     tags: [Playback Queue]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               restaurantId:
 *                 type: string
 *               song:
 *                 type: object
 *                 properties:
 *                   videoId:
 *                     type: string
 *                   title:
 *                     type: string
 *                   artist:
 *                     type: string
 *               isPriority:
 *                 type: boolean
 *     responses:
 *       201:
 *         description: Música criada e adicionada à fila
 */
router.post(
  "/add",
  [
    body("restaurantId")
      .notEmpty()
      .withMessage("ID do restaurante é obrigatório"),
    body("song.videoId").notEmpty().withMessage("ID do vídeo é obrigatório"),
    body("song.title").notEmpty().withMessage("Título da música é obrigatório"),
    body("song.artist").notEmpty().withMessage("Artista é obrigatório"),
  ],
  optionalAuth,
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new ValidationError("Parâmetros inválidos", errors.array());
    }

    const { restaurantId, song } = req.body;
    // Coerção defensiva para booleano
    const rawPriority = (req.body?.isPriority as any);
    const isPriority = rawPriority === true || rawPriority === "true" || rawPriority === 1 || rawPriority === "1";
    const sessionId = req.headers["x-session-id"] as string;

    if (!sessionId) {
      throw new ValidationError("Session ID é obrigatório");
    }

    // Primeiro, criar a sugestão
    const suggestionData = {
      youtubeVideoId: song.videoId,
      title: song.title,
      artist: song.artist,
      restaurantId,
      isPaid: isPriority,
      clientSessionId: sessionId,
      status: "approved", // Aprovar automaticamente para adicionar à fila
    };

    const queueItem = await playbackQueueService.createSuggestionAndAddToQueue(
      suggestionData
    );

    if (!queueItem) {
      throw new ValidationError("Erro ao criar sugestão e adicionar à fila");
    }

    res.status(201).json({
      success: true,
      message: "Música adicionada à fila",
      queueItem,
    });
  })
);

/**
 * @swagger
 * /api/v1/playback-queue/play/{suggestionId}:
 *   post:
 *     summary: Marcar música como tocando
 *     tags: [Playback Queue]
 *     parameters:
 *       - in: path
 *         name: suggestionId
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Música marcada como tocando
 */
router.post(
  "/play/:suggestionId",
  [
    param("suggestionId")
      .notEmpty()
      .withMessage("ID da sugestão é obrigatório"),
  ],
  optionalAuth,
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new ValidationError("Parâmetros inválidos", errors.array());
    }

    const { suggestionId } = req.params;

    await playbackQueueService.markAsPlaying(suggestionId);

    res.json({
      success: true,
      message: "Música marcada como tocando",
    });
  })
);

/**
 * @swagger
 * /api/v1/playback-queue/{restaurantId}/play-by-video:
 *   post:
 *     summary: Marcar música como tocando por youtubeVideoId
 *     tags: [Playback Queue]
 *     parameters:
 *       - in: path
 *         name: restaurantId
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required: [youtubeVideoId]
 *             properties:
 *               youtubeVideoId:
 *                 type: string
 *     responses:
 *       200:
 *         description: Música marcada como tocando
 */
router.post(
  "/:restaurantId/play-by-video",
  [
    param("restaurantId").notEmpty().withMessage("ID do restaurante é obrigatório"),
    body("youtubeVideoId").notEmpty().withMessage("ID do vídeo é obrigatório"),
  ],
  optionalAuth,
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new ValidationError("Parâmetros inválidos", errors.array());
    }

    const { restaurantId } = req.params;
    const { youtubeVideoId } = req.body as { youtubeVideoId: string };

    await playbackQueueService.markVideoAsPlaying(restaurantId, youtubeVideoId);

    res.json({ success: true, message: "Música marcada como tocando" });
  })
);

/**
 * @swagger
 * /api/v1/playback-queue/complete/{suggestionId}:
 *   post:
 *     summary: Marcar música como concluída
 *     tags: [Playback Queue]
 *     parameters:
 *       - in: path
 *         name: suggestionId
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Música marcada como concluída
 */
router.post(
  "/complete/:suggestionId",
  [
    param("suggestionId")
      .notEmpty()
      .withMessage("ID da sugestão é obrigatório"),
  ],
  optionalAuth,
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new ValidationError("Parâmetros inválidos", errors.array());
    }

    const { suggestionId } = req.params;

    await playbackQueueService.markAsCompleted(suggestionId);

    res.json({
      success: true,
      message: "Música marcada como concluída",
    });
  })
);

/**
 * @swagger
 * /api/v1/playback-queue/{restaurantId}/complete-by-video:
 *   post:
 *     summary: Marcar música como concluída por youtubeVideoId
 *     tags: [Playback Queue]
 *     parameters:
 *       - in: path
 *         name: restaurantId
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required: [youtubeVideoId]
 *             properties:
 *               youtubeVideoId:
 *                 type: string
 *     responses:
 *       200:
 *         description: Música marcada como concluída
 */
router.post(
  "/:restaurantId/complete-by-video",
  [
    param("restaurantId").notEmpty().withMessage("ID do restaurante é obrigatório"),
    body("youtubeVideoId").notEmpty().withMessage("ID do vídeo é obrigatório"),
  ],
  optionalAuth,
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new ValidationError("Parâmetros inválidos", errors.array());
    }

    const { restaurantId } = req.params;
    const { youtubeVideoId } = req.body as { youtubeVideoId: string };

    await playbackQueueService.markVideoAsCompleted(restaurantId, youtubeVideoId);

    res.json({ success: true, message: "Música marcada como concluída" });
  })
);

/**
 * @swagger
 * /api/v1/playback-queue/{restaurantId}/song-ended:
 *   post:
 *     summary: Notificar fim de uma música por youtubeVideoId (compatibilidade com frontend)
 *     tags: [Playback Queue]
 *     parameters:
 *       - in: path
 *         name: restaurantId
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required: [youtubeVideoId]
 *             properties:
 *               youtubeVideoId:
 *                 type: string
 *     responses:
 *       200:
 *         description: Música marcada como concluída
 */
router.post(
  "/:restaurantId/song-ended",
  [
    param("restaurantId").notEmpty().withMessage("ID do restaurante é obrigatório"),
    body("youtubeVideoId").notEmpty().withMessage("ID do vídeo é obrigatório"),
  ],
  optionalAuth,
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new ValidationError("Parâmetros inválidos", errors.array());
    }

    const { restaurantId } = req.params;
    const { youtubeVideoId } = req.body as { youtubeVideoId: string };

    await playbackQueueService.markVideoAsCompleted(restaurantId, youtubeVideoId);
    // Sincronizar estado autoritativo de reprodução (idempotente)
    try {
      await playbackService.forceEndCurrentTrack(restaurantId);
    } catch {}

    res.json({ success: true, message: "Música marcada como concluída" });
  })
);

/**
 * @swagger
 * /api/v1/playback-queue/remove/{suggestionId}:
 *   delete:
 *     summary: Remover música da fila
 *     tags: [Playback Queue]
 *     parameters:
 *       - in: path
 *         name: suggestionId
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               reason:
 *                 type: string
 *                 default: "removed by admin"
 *     responses:
 *       200:
 *         description: Música removida da fila
 */
router.delete(
  "/remove/:suggestionId",
  [
    param("suggestionId")
      .notEmpty()
      .withMessage("ID da sugestão é obrigatório"),
    body("reason").optional().isString(),
  ],
  optionalAuth,
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new ValidationError("Parâmetros inválidos", errors.array());
    }

    const { suggestionId } = req.params;
    const { reason = "removed by admin" } = req.body;

    const success = await playbackQueueService.removeFromQueue(
      suggestionId,
      reason
    );

    if (!success) {
      throw new NotFoundError("Sugestão não encontrada");
    }

    res.json({
      success: true,
      message: "Música removida da fila",
    });
  })
);

/**
 * @swagger
 * /api/v1/playback-queue/{restaurantId}/reorder:
 *   post:
 *     summary: Reordenar fila de reprodução
 *     tags: [Playback Queue]
 *     parameters:
 *       - in: path
 *         name: restaurantId
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - newOrder
 *             properties:
 *               newOrder:
 *                 type: array
 *                 items:
 *                   type: string
 *                 description: Array de IDs das sugestões na nova ordem
 *     responses:
 *       200:
 *         description: Fila reordenada
 */
router.post(
  "/:restaurantId/reorder",
  [
    param("restaurantId")
      .notEmpty()
      .withMessage("ID do restaurante é obrigatório"),
    body("newOrder").isArray().withMessage("Nova ordem deve ser um array"),
    body("newOrder.*").isString().withMessage("IDs devem ser strings"),
  ],
  optionalAuth,
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new ValidationError("Dados inválidos", errors.array());
    }

    if (process.env.ONLY_ORCHESTRATOR === 'true') {
      return res.status(403).json({
        success: false,
        message:
          'Reordenação direta da fila desabilitada: ONLY_ORCHESTRATOR=true (apenas o orquestrador decide a ordem)',
      });
    }

    const { restaurantId } = req.params;
    const { newOrder } = req.body;

    const success = await playbackQueueService.reorderQueue(
      restaurantId,
      newOrder
    );

    if (!success) {
      throw new ValidationError("Erro ao reordenar fila");
    }

    res.json({
      success: true,
      message: "Fila reordenada com sucesso",
    });
  })
);

/**
 * @swagger
 * /api/v1/playback-queue/{restaurantId}/wait-time:
 *   get:
 *     summary: Obter tempo de espera estimado
 *     tags: [Playback Queue]
 *     parameters:
 *       - in: path
 *         name: restaurantId
 *         required: true
 *         schema:
 *           type: string
 *       - in: query
 *         name: position
 *         required: true
 *         schema:
 *           type: integer
 *           minimum: 1
 *     responses:
 *       200:
 *         description: Tempo de espera estimado
 */
router.get(
  "/:restaurantId/wait-time",
  [
    param("restaurantId")
      .notEmpty()
      .withMessage("ID do restaurante é obrigatório"),
    query("position")
      .isInt({ min: 1 })
      .withMessage("Posição deve ser um número positivo"),
  ],
  optionalAuth,
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new ValidationError("Parâmetros inválidos", errors.array());
    }

    const { restaurantId } = req.params;
    const { position } = req.query;

    const waitTimeSeconds = await playbackQueueService.getEstimatedWaitTime(
      restaurantId,
      parseInt(position as string)
    );

    const waitTimeMinutes = Math.ceil(waitTimeSeconds / 60);
    const waitTimeFormatted =
      waitTimeMinutes > 60
        ? `${Math.floor(waitTimeMinutes / 60)}h ${waitTimeMinutes % 60}m`
        : `${waitTimeMinutes}m`;

    res.json({
      success: true,
      waitTime: {
        seconds: waitTimeSeconds,
        minutes: waitTimeMinutes,
        formatted: waitTimeFormatted,
        position: parseInt(position as string),
      },
    });
  })
);

/**
 * @swagger
 * /api/v1/playback-queue/{restaurantId}/clear-old:
 *   post:
 *     summary: Limpar músicas antigas da fila
 *     tags: [Playback Queue]
 *     parameters:
 *       - in: path
 *         name: restaurantId
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               olderThanHours:
 *                 type: integer
 *                 default: 24
 *                 minimum: 1
 *     responses:
 *       200:
 *         description: Músicas antigas removidas
 */
router.post(
  "/:restaurantId/clear-old",
  [
    param("restaurantId")
      .notEmpty()
      .withMessage("ID do restaurante é obrigatório"),
    body("olderThanHours").optional().isInt({ min: 1 }),
  ],
  optionalAuth,
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new ValidationError("Parâmetros inválidos", errors.array());
    }

    const { restaurantId } = req.params;
    const { olderThanHours = 24 } = req.body;

    const removedCount = await playbackQueueService.clearOldQueue(
      restaurantId,
      olderThanHours
    );

    res.json({
      success: true,
      message: `${removedCount} músicas antigas removidas da fila`,
      removedCount,
    });
  })
);

/**
 * @swagger
 * /api/v1/playback-queue/{restaurantId}/promote:
 *   post:
 *     summary: Promover música para fila prioritária
 *     tags: [Playback Queue]
 *     parameters:
 *       - in: path
 *         name: restaurantId
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               trackId:
 *                 type: string
 *                 description: ID da música a ser promovida
 *     responses:
 *       200:
 *         description: Música promovida com sucesso
 *       404:
 *         description: Música não encontrada
 */
router.post(
  "/:restaurantId/promote",
  [
    param("restaurantId")
      .notEmpty()
      .withMessage("ID do restaurante é obrigatório"),
    body("trackId").notEmpty().withMessage("ID da música é obrigatório"),
  ],
  optionalAuth,
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new ValidationError("Parâmetros inválidos", errors.array());
    }

    const { restaurantId } = req.params;
    const { trackId } = req.body;

    try {
      await playbackQueueService.promoteTrack(restaurantId, trackId);

      res.json({
        success: true,
        message: "Música promovida para fila prioritária com sucesso",
      });
    } catch (error) {
      if (error instanceof NotFoundError) {
        throw error;
      }
      throw new ValidationError("Erro ao promover música");
    }
  })
);

export default router;